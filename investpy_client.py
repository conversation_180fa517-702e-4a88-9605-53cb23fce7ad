import investpy
import pandas as pd
from typing import Dict, Any, Optional
from decimal import Decimal, InvalidOperation
import json
from utils.logging import logger

def get_cleaned_ticker(ticker: str) -> str:
    """
    Remove stock exchange suffixes from ticker symbols and optionally remove leading zeros.

    Args:
        ticker: The ticker symbol with potential suffix (e.g., '0700.HK', '2002.KR')
        remove_leading_zeros: Whether to remove leading zeros after suffix removal (default: True)

    Returns:
        str: The ticker symbol without suffix and optionally without leading zeros
             (e.g., '0700.HK' -> '700', '2002.KR' -> '2002')
    """
    if not ticker:
        return ticker

    # Define common stock exchange suffixes
    suffixes = [
        '.HK',   # Hong Kong
        '.KS',   # Korea Stock Exchange
        '.T',    # Tokyo
        '.TW',   # Taiwan
    ]

    # Remove suffix if found
    cleaned_ticker = ticker
    ticker_upper = ticker.upper()
    for suffix in suffixes:
        if ticker_upper.endswith(suffix):
            cleaned_ticker = ticker[:-len(suffix)]
            break

    # Remove leading zeros if requested and the result is numeric
    if cleaned_ticker.isdigit():
        # Convert to int and back to string to remove leading zeros
        # But preserve at least one digit (don't convert "000" to "")
        cleaned_ticker = str(int(cleaned_ticker))

    return cleaned_ticker

def zfill_stock(stock, country):
    stock = get_cleaned_ticker(stock)
    if not country:
        return stock
    country = country.lower()
    if country == 'hong kong':
        # HK stocks are 4 digits
        stock = stock.zfill(4)
    elif country == 'south korea':
        # Korean stocks are 6 digits
        stock = stock.zfill(6)
    return stock

def check_stock_exists(stock: str, country: str):
    stock = zfill_stock(stock, country)
    try:
        investpy.stocks.get_stock_financial_summary(stock, country)
        return True
    except Exception as e:
        logger.warning(f"Error checking if stock {stock} {country} exists: {str(e)}")
        return False
    

def get_stock_financial_summary_formatted(
    stock: str,
    country: str,
    summary_types: Optional[list] = None
) -> Dict[str, Any]:
    """
    Query financial data from investpy and format it to match query_ticker_concepts_from_db2 structure.

    Args:
        stock: The stock symbol (e.g., '6758', '0700.HK', '2002.KR')
        country: The country name (e.g., 'Japan', 'Hong Kong', 'South Korea')
        summary_types: List of summary types to query. Defaults to all three types.

    Returns:
        dict: Financial data formatted to match query_ticker_concepts_from_db2 structure
    """
    if summary_types is None:
        summary_types = ['income_statement', 'balance_sheet', 'cash_flow_statement']

    if not check_stock_exists(stock, country):
        return {}
    # Remove suffix from stock symbol if requested
    original_stock = stock
    stock = zfill_stock(stock, country)
    if stock != original_stock:
        logger.info(f"Cleaned ticker: {original_stock} -> {stock}")

    try:
        # Initialize the result structure matching query_ticker_concepts_from_db2
        results = {
            'Annual': {
                'Income': {},
                'Balance': {},
                'CashFlow': {},
            },
            'Quarterly': {
                'Income': {},
                'Balance': {},
                'CashFlow': {},
            },
            'AvgGrowth3Y': {
                'Income': {},
                'Balance': {},
                'CashFlow': {},
            },
            'AvgGrowth5Y': {
                'Income': {},
                'Balance': {},
                'CashFlow': {},
            },
            'AvgGrowth3Q': {
                'Income': {},
                'Balance': {},
                'CashFlow': {},
            },
            'AvgGrowth5Q': {
                'Income': {},
                'Balance': {},
                'CashFlow': {},
            },
        }

        # Define mapping from investpy fields to display concepts
        # These mappings are based on actual investpy field names observed in testing
        income_field_mapping = {
            'Total Revenue': 'Revenue',
            'Gross Profit': 'Gross Profit',
            'Operating Income': 'Op. Income',
            'Net Income': 'Net Income',
        }

        balance_field_mapping = {
            'Total Assets': 'Total Assets',
            'Total Liabilities': 'Total Liabilities',
            'Total Equity': 'Total Equity',
        }

        cash_flow_field_mapping = {
            'Operating Cash Flow': 'Op.Cash Flow',
            'Cash From Investing Activities': 'Cash From Investing',
            'Cash From Financing Activities': 'Cash From Financing',
            'Net Change In Cash': 'Net Cash Change',
        }

        # Process each summary type
        for summary_type in summary_types:
            logger.info(f"Processing {summary_type} for {stock} ({country})")

            # Get annual data
            try:
                annual_data = investpy.get_stock_financial_summary(stock, country, summary_type, 'annual')
                logger.info(f"Retrieved annual {summary_type} data: {annual_data.shape}")
            except Exception as e:
                logger.warning(f"Failed to get annual {summary_type} data: {str(e)}")
                annual_data = pd.DataFrame()

            # Get quarterly data
            try:
                quarterly_data = investpy.get_stock_financial_summary(stock, country, summary_type, 'quarterly')
                logger.info(f"Retrieved quarterly {summary_type} data: {quarterly_data.shape}")
            except Exception as e:
                logger.warning(f"Failed to get quarterly {summary_type} data: {str(e)}")
                quarterly_data = pd.DataFrame()

            # Determine which statement type and field mapping to use
            if summary_type == 'income_statement':
                statement_type = 'Income'
                field_mapping = income_field_mapping
            elif summary_type == 'balance_sheet':
                statement_type = 'Balance'
                field_mapping = balance_field_mapping
            elif summary_type == 'cash_flow_statement':
                statement_type = 'CashFlow'
                field_mapping = cash_flow_field_mapping
            else:
                logger.warning(f"Unknown summary_type: {summary_type}")
                continue

            # Process annual data
            if not annual_data.empty:
                results['Annual'][statement_type] = _process_financial_data(
                    annual_data, field_mapping, 'annual'
                )

            # Process quarterly data
            if not quarterly_data.empty:
                results['Quarterly'][statement_type] = _process_financial_data(
                    quarterly_data, field_mapping, 'quarterly'
                )

        # Calculate growth rates using the same logic as query_ticker_concepts_from_db2
        _calculate_growth_rates(results)

        logger.info(f"Finished processing financial data for {stock} ({country}): {json.dumps(results)}")
        return results

    except Exception as e:
        logger.error(f"Error in get_stock_financial_summary_formatted: {str(e)}")
        return {}


def _process_financial_data(
    df: pd.DataFrame,
    field_mapping: Dict[str, str],
    period_type: str
) -> Dict[str, Any]:
    """
    Process financial data DataFrame and convert to the expected format.

    Args:
        df: pandas DataFrame with financial data
        field_mapping: mapping from investpy field names to display concepts
        period_type: 'annual' or 'quarterly'

    Returns:
        dict: Processed financial data in the expected format
    """
    processed_data = {}

    if df.empty:
        return processed_data

    # Print the DataFrame structure for debugging
    logger.info(f"DataFrame columns: {df.columns.tolist()}")
    logger.info(f"DataFrame index: {df.index.tolist()}")
    logger.info(f"DataFrame shape: {df.shape}")

    # The exact processing will depend on how investpy structures its data
    # This is a template that needs to be adjusted based on actual data structure

    try:
        # Based on the test output, it seems the DataFrame has financial items as columns and time periods as rows
        # Let's handle both possible structures

        # Check if we have time periods as index (rows) and financial items as columns
        if len(df.index) > 0 and len(df.columns) > 0:
            logger.info(f"Processing DataFrame with index: {df.index[:5].tolist()} and columns: {df.columns[:5].tolist()}")

            # Iterate through each row (time period)
            for idx, row in df.iterrows():
                time_period = str(idx)

                if period_type == 'annual':
                    # For annual data, try to extract year from date or use as-is
                    try:
                        # Try to parse as datetime first
                        if hasattr(idx, 'year'):
                            year = idx.year
                        else:
                            # Try to extract year from string
                            import re
                            year_match = re.search(r'(\d{4})', time_period)
                            if year_match:
                                year = int(year_match.group(1))
                            else:
                                year = int(time_period)

                        processed_data[year] = {}
                        current_period_key = year
                    except (ValueError, TypeError, AttributeError):
                        logger.warning(f"Could not parse year from index: {time_period}")
                        continue
                else:
                    # For quarterly data, try to extract meaningful period info
                    fiscal_year = None
                    fiscal_quarter = None

                    # Try to extract year and quarter from date
                    if hasattr(idx, 'year') and hasattr(idx, 'month'):
                        fiscal_year = idx.year
                        # Determine quarter from month
                        month = idx.month
                        if month <= 3:
                            fiscal_quarter = 1
                        elif month <= 6:
                            fiscal_quarter = 2
                        elif month <= 9:
                            fiscal_quarter = 3
                        else:
                            fiscal_quarter = 4

                        # Create a more readable period key
                        current_period_key = f"{fiscal_year}Q{fiscal_quarter}"
                    else:
                        # Try to parse from string
                        try:
                            import re
                            year_match = re.search(r'(\d{4})', time_period)
                            if year_match:
                                fiscal_year = int(year_match.group(1))
                                # Try to determine quarter from date
                                if hasattr(idx, 'month'):
                                    month = idx.month
                                    fiscal_quarter = (month - 1) // 3 + 1
                                current_period_key = f"{fiscal_year}Q{fiscal_quarter}" if fiscal_quarter else time_period
                            else:
                                current_period_key = time_period
                        except:
                            current_period_key = time_period

                    processed_data[current_period_key] = {}

                # Process each financial item (column) for this time period
                for column in df.columns:
                    investpy_field = str(column)
                    if investpy_field in field_mapping:
                        display_concept = field_mapping[investpy_field]
                        value = str(row[column]) if not pd.isna(row[column]) else None

                        if period_type == 'annual':
                            processed_data[current_period_key][display_concept] = {
                                "concept": investpy_field,
                                "value": value,
                                "fiscal_year": current_period_key,
                            }
                        else:
                            processed_data[current_period_key][display_concept] = {
                                "concept": investpy_field,
                                "value": value,
                                "fiscal_year": fiscal_year,
                                "fiscal_quarter": current_period_key,
                            }
                    else:
                        # Log unmapped fields for debugging
                        logger.debug(f"Unmapped field: {investpy_field}")

        # If no data was processed, try the alternative structure (financial items as rows, time periods as columns)
        if not processed_data and len(df.columns) > 0:
            logger.info("Trying alternative DataFrame structure (financial items as rows)")
            for column in df.columns:
                time_period = str(column)

                if period_type == 'annual':
                    try:
                        year = int(time_period)
                        processed_data[year] = {}
                        current_period_key = year
                    except (ValueError, TypeError):
                        logger.warning(f"Could not parse year from column: {time_period}")
                        continue
                else:
                    processed_data[time_period] = {}
                    current_period_key = time_period

                # Process each financial item (row) for this time period
                for idx, row in df.iterrows():
                    investpy_field = str(idx)
                    if investpy_field in field_mapping:
                        display_concept = field_mapping[investpy_field]
                        value = str(row[column]) if not pd.isna(row[column]) else None

                        if period_type == 'annual':
                            processed_data[current_period_key][display_concept] = {
                                "concept": investpy_field,
                                "value": value,
                                "fiscal_year": current_period_key,
                            }
                        else:
                            # Extract year and quarter from time period if possible
                            fiscal_year = None
                            fiscal_quarter = None
                            try:
                                if 'Q' in time_period:
                                    year_part, quarter_part = time_period.split('Q')
                                    fiscal_year = int(year_part)
                                    fiscal_quarter = int(quarter_part)
                            except:
                                pass

                            processed_data[current_period_key][display_concept] = {
                                "concept": investpy_field,
                                "value": value,
                                "fiscal_year": fiscal_year,
                                "fiscal_period": current_period_key,
                            }

    except Exception as e:
        logger.error(f"Error processing financial data: {str(e)}")

    return processed_data


def _calculate_growth_rates(results):
    """
    Calculate growth rates for all financial metrics in the results.

    Args:
        results: The results dictionary with Annual and Quarterly data
    """
    # Get available periods for calculations
    annual_periods = []
    quarterly_periods = []

    # Extract available periods from the data
    for statement_type in ['Income', 'Balance', 'CashFlow']:
        if results['Annual'][statement_type]:
            annual_periods = sorted(results['Annual'][statement_type].keys(), reverse=True)
            break

    for statement_type in ['Income', 'Balance', 'CashFlow']:
        if results['Quarterly'][statement_type]:
            quarterly_periods = sorted(results['Quarterly'][statement_type].keys(), reverse=True)
            break

    # Calculate annual growth rates
    for statement_type in ['Income', 'Balance', 'CashFlow']:
        # Get all display concepts for this statement type
        display_concepts = set()
        for period_data in results['Annual'][statement_type].values():
            display_concepts.update(period_data.keys())

        # Calculate 3Y and 5Y growth for each concept
        for display_concept in display_concepts:
            # 3Y growth (need at least 3 years)
            if len(annual_periods) >= 3:
                past_3_years = annual_periods[:3]
                avg_growth_3 = calculate_average_growth(
                    results['Annual'][statement_type],
                    display_concept,
                    past_3_years
                )
                if avg_growth_3 is not None:
                    formatted_growth = avg_growth_3.quantize(Decimal('0.00001'))
                    results['AvgGrowth3Y'][statement_type][display_concept] = str(formatted_growth)

            # 5Y growth (need at least 5 years)
            if len(annual_periods) >= 5:
                past_5_years = annual_periods[:5]
                avg_growth_5 = calculate_average_growth(
                    results['Annual'][statement_type],
                    display_concept,
                    past_5_years
                )
                if avg_growth_5 is not None:
                    formatted_growth = avg_growth_5.quantize(Decimal('0.00001'))
                    results['AvgGrowth5Y'][statement_type][display_concept] = str(formatted_growth)

    # Calculate quarterly growth rates
    for statement_type in ['Income', 'Balance', 'CashFlow']:
        # Get all display concepts for this statement type
        display_concepts = set()
        for period_data in results['Quarterly'][statement_type].values():
            display_concepts.update(period_data.keys())

        # Calculate 3Q and 5Q growth for each concept
        for display_concept in display_concepts:
            # 3Q growth (need at least 3 quarters)
            if len(quarterly_periods) >= 3:
                past_3_quarters = quarterly_periods[:3]
                avg_growth_3 = calculate_average_growth(
                    results['Quarterly'][statement_type],
                    display_concept,
                    past_3_quarters
                )
                if avg_growth_3 is not None:
                    formatted_growth = avg_growth_3.quantize(Decimal('0.00001'))
                    results['AvgGrowth3Q'][statement_type][display_concept] = str(formatted_growth)

            # 5Q growth (need at least 5 quarters)
            if len(quarterly_periods) >= 5:
                past_5_quarters = quarterly_periods[:5]
                avg_growth_5 = calculate_average_growth(
                    results['Quarterly'][statement_type],
                    display_concept,
                    past_5_quarters
                )
                if avg_growth_5 is not None:
                    formatted_growth = avg_growth_5.quantize(Decimal('0.00001'))
                    results['AvgGrowth5Q'][statement_type][display_concept] = str(formatted_growth)


def calculate_growth_rate(current, previous):
    """
    Calculate growth rate between two values.

    Args:
        current: The current period value
        previous: The previous period value

    Returns:
        Decimal: The growth rate as a decimal (e.g., 0.15 for 15% growth)
    """
    if previous == 0 or previous is None or current is None:
        return None

    # Convert to Decimal for precise division
    try:
        current_dec = Decimal(current)
        previous_dec = Decimal(previous)
        growth_rate = (current_dec / previous_dec) - Decimal('1')
        # Round to 5 decimal places
        return growth_rate.quantize(Decimal('0.00001'))
    except (TypeError, InvalidOperation):
        logger.debug(f"Error in growth rate calculation: {current} / {previous}")
        return None


def calculate_average_growth(years_data, concept, periods):
    """
    Calculate the average growth rate for a concept over the available periods.

    Args:
        years_data: Dictionary of period data
        concept: The financial concept to calculate growth for
        periods: List of periods (years or quarters) in chronological order

    Returns:
        Decimal: The average growth rate as a decimal
    """
    growth_rates = []

    # Calculate period-over-period growth rates
    for i in range(len(periods) - 1):
        current_period = periods[i]
        previous_period = periods[i + 1]

        # Skip if either period doesn't have the concept or has empty data
        if (concept not in years_data.get(current_period, {}) or
            concept not in years_data.get(previous_period, {}) or
            'value' not in years_data[current_period][concept] or
            'value' not in years_data[previous_period][concept]):
            continue

        try:
            # Get raw values
            current_raw = years_data[current_period][concept]['value']
            previous_raw = years_data[previous_period][concept]['value']

            # Handle various input types using Decimal for precise calculations
            try:
                if current_raw is None or previous_raw is None:
                    continue

                # Convert to string first if not already a string
                current_str = str(current_raw).strip().replace(',', '')
                previous_str = str(previous_raw).strip().replace(',', '')

                # Convert to Decimal (handles int, float, and numeric strings with better precision)
                current_value = Decimal(current_str)
                previous_value = Decimal(previous_str)

                growth_rate = calculate_growth_rate(current_value, previous_value)
                if growth_rate is not None:
                    growth_rates.append(growth_rate)

            except (InvalidOperation, ValueError) as e:
                # Log the issue with the specific values for debugging
                logger.debug(f"Cannot convert to Decimal for {concept} between {current_period} and {previous_period}: {str(e)}")
                logger.debug(f"Current raw: {current_raw}, Previous raw: {previous_raw}")
                continue

        except Exception as e:
            # Log the issue with the specific values for debugging
            logger.debug(f"Error in growth calculation for {concept} between {current_period} and {previous_period}: {str(e)}")
            continue

    # Calculate average if we have any valid growth rates
    if len(growth_rates) >= 1:
        # Use Decimal for precise average calculation
        total = sum(growth_rates)
        count = Decimal(len(growth_rates))
        return total / count

    return None


if __name__ == "__main__":
    # Test the suffix removal function
    test_tickers = ["0700.HK", "0101.HK", "2002.KR", "005930.KR", "AAPL", "6758.T", "ASML.AS", "SAP.DE"]
    print("Testing ticker suffix and leading zero removal:")
    for ticker in test_tickers:
        cleaned = get_cleaned_ticker(ticker)
        print(f"  {ticker} -> {cleaned}")

    print("\nTesting financial data retrieval:")
    get_stock_financial_summary_formatted("AAPL", "United States")
    get_stock_financial_summary_formatted("1", "Hong Kong")
    get_stock_financial_summary_formatted("001000", "South Korea")

    print(check_stock_exists("1", "Hong Kong"))
    print(check_stock_exists("0001", "Hong Kong"))
    print(check_stock_exists("9618", "Hong Kong"))
