from threading import Lock, BoundedSemaphore
from queue import Queue
from threading import Thread
import time
from utils.logging import logger
from utils.model_utils import update_model_metrics, select_provider_and_model
from providers import get_provider
from config.settings import settings

# Initialize model request queue and worker pool
model_queue = Queue(maxsize=settings.MODEL_QUEUE_SIZE)

# Use provider-specific locks instead of a single global lock
provider_locks = {
    'deepseek': Lock(),
    'openai': Lock(),
    'anthropic': Lock()
}

# Semaphore to limit total concurrent API calls
api_semaphore = BoundedSemaphore(settings.MAX_CONCURRENT_API_CALLS)

def model_worker():
    """
    Worker thread function to process model requests from the queue.
    """
    while True:
        try:
            task = model_queue.get()
            if task is None:
                break
            session_id, messages, provider_name, model_name, callback, kwargs = task
            
            # Get the appropriate lock for this provider
            provider_lock = provider_locks.get(provider_name.lower())
            if provider_lock is None:
                # If a new provider is added, create a lock for it
                provider_locks[provider_name.lower()] = Lock()
                provider_lock = provider_locks[provider_name.lower()]
            
            # Use semaphore to limit total concurrent API calls
            with api_semaphore:
                # Use provider-specific lock to prevent overwhelming a single provider
                with provider_lock:
                    provider = get_provider(provider_name)
                    
                    # Record start time for latency measurement
                    start_time = time.time()
                    try:
                        response = provider.chat_completion(
                            messages=messages,
                            model=model_name,
                            **kwargs
                        )
                        # Update metrics with successful call
                        latency = time.time() - start_time
                        update_model_metrics(f"{provider_name}/{model_name}", latency=latency)
                        
                        callback(session_id, response)
                    except Exception as e:
                        # Update metrics with error
                        update_model_metrics(f"{provider_name}/{model_name}", error=True)
                        raise e
        except Exception as e:
            logger.error(f"Model worker error: {str(e)}")
        finally:
            model_queue.task_done()

def process_streaming_request(messages, provider_name, model_name, session_id, **kwargs):
    """
    Process a streaming request to a model provider.
    
    Args:
        messages: List of message objects
        provider_name: The provider to use
        model_name: The model to use
        session_id: The conversation session ID
        **kwargs: Additional parameters including function definitions
        
    Returns:
        generator: A generator yielding streaming responses
    """
    # Get the appropriate lock for this provider
    provider_lock = provider_locks.get(provider_name.lower())
    if provider_lock is None:
        # If a new provider is added, create a lock for it
        provider_locks[provider_name.lower()] = Lock()
        provider_lock = provider_locks[provider_name.lower()]
    
    # Use semaphore to limit total concurrent API calls
    with api_semaphore:
        # Use provider-specific lock to prevent overwhelming a single provider
        with provider_lock:
            provider = get_provider(provider_name)
            
            # Record start time for latency measurement
            start_time = time.time()
            try:
                stream_response = provider.chat_completion(
                    messages=messages,
                    model=model_name,
                    stream=True,
                    **kwargs
                )
                
                # Update metrics with successful call
                latency = time.time() - start_time
                update_model_metrics(f"{provider_name}/{model_name}", latency=latency)
                
                return stream_response
            except Exception as e:
                # Update metrics with error
                update_model_metrics(f"{provider_name}/{model_name}", error=True)
                logger.error(f"Streaming error: {str(e)}")
                raise e

def queue_model_request(session_id, messages, provider_name, model_name, callback, **kwargs):
    """
    Queue a model request to be processed by a worker thread.
    
    Args:
        session_id: The conversation session ID
        messages: List of message objects
        provider_name: The provider to use
        model_name: The model to use
        callback: Function to call with the response
        **kwargs: Additional parameters including function definitions
        
    Returns:
        dict: Status information about the queued request
    """
    if model_queue.full():
        raise ValueError("Server busy, please try again later")
    
    model_queue.put((session_id, messages, provider_name, model_name, callback, kwargs))
    
    return {
        'status': 'queued', 
        'session_id': session_id,
        'provider': provider_name,
        'model': model_name
    }

# Start worker threads
def start_workers(num_workers=None):
    """
    Start worker threads to process model requests.
    
    Args:
        num_workers: Number of worker threads to start (default: settings.WORKER_THREADS)
    """
    if num_workers is None:
        num_workers = settings.WORKER_THREADS
        
    for i in range(num_workers):
        Thread(target=model_worker, daemon=True).start()
    
    logger.info(f"Started {num_workers} model worker threads")
