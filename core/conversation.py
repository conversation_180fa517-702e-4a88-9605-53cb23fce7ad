import json
import datetime
from typing import List, Dict, Any, Optional
from utils.redis_client import redis_client
from utils.logging import logger
from utils.search_utils import enrich_with_web_search
from utils.round_aware_search import enrich_messages_with_round_aware_search
from config.constants import CONVERSATION_KEY_PREFIX, FINANCE_CONSULTANT_SYSTEM_MESSAGE

class ConversationManager:
    """
    Manager for conversation history and context for user sessions.
    Supports multiple rounds of conversation with advanced features like
    context window management, round tracking, summarization, and branching.
    """
    
    def __init__(self, session_id):
        """
        Initialize a conversation manager for a specific session.
        
        Args:
            session_id: The unique identifier for this conversation session
        """
        self.session_id = session_id
        self.history_key = f"{CONVERSATION_KEY_PREFIX}{session_id}"
    
    def get_history(self, limit=5, max_tokens=None, round_id=None) -> List[Dict[str, Any]]:
        """
        Get the conversation history for this session with token count awareness.
        
        Args:
            limit: Maximum number of messages to retrieve (default: 5)
            max_tokens: Maximum number of tokens to include
            round_id: Optional round ID to filter messages by round
            
        Returns:
            list: List of message objects with role and content
        """
        history = redis_client.lrange(self.history_key, 0, -1) or []
        logger.info(f"Getting history for session {self.session_id}, round {round_id}, found {len(history)} messages")
        
        # Convert to message objects
        messages = []
        total_tokens = 0
        
        # Process from most recent to oldest if no specific round is requested
        if round_id is None:
            message_list = reversed(history) if max_tokens else history[-limit:]
        else:
            # Filter by round_id
            message_list = [msg for msg in history if json.loads(msg).get('round_id') == round_id]
            if not max_tokens:
                message_list = message_list[-limit:]
        
        for msg in message_list:
            message_data = json.loads(msg)
            
            # Skip if filtering by round and this message is from a different round
            if round_id is not None and message_data.get('round_id') != round_id:
                continue
                
            role = message_data.get('role')
            message = {
                "role": role,
                "content": message_data.get('content')
            }
            
            # Add function name for function messages
            if role == 'function':
                message['name'] = message_data.get('name')
            
            # Estimate token count (simplified)
            if max_tokens:
                message_tokens = len(message_data.get('content', '').split())
                
                # Check if adding this message would exceed the token limit
                if total_tokens + message_tokens > max_tokens:
                    break
                    
                if round_id is None:
                    messages.insert(0, message)  # Insert at beginning to maintain order
                else:
                    messages.append(message)
                    
                total_tokens += message_tokens
                
                if len(messages) >= limit:
                    break
            else:
                messages.append(message)
        
        return messages
    
    def get_raw_history(self, limit=None, round_id=None) -> List[str]:
        """
        Get the raw conversation history for this session.
        
        Args:
            limit: Optional maximum number of messages to retrieve
            round_id: Optional round ID to filter messages by round
            
        Returns:
            list: List of raw message strings
        """
        if limit and not round_id:
            history = redis_client.lrange(self.history_key, 0, limit-1) or []
        else:
            history = redis_client.lrange(self.history_key, 0, -1) or []
            
        if round_id is not None:
            # Filter by round_id
            history = [msg for msg in history if json.loads(msg).get('round_id') == round_id]
            if limit:
                history = history[:limit]
                
        return history
    
    def _get_current_round_id(self) -> int:
        """
        Get or create the current round ID.
        
        Returns:
            int: The current round ID
        """
        round_key = f"{self.history_key}:current_round"
        current_round = redis_client.get(round_key)
        
        if not current_round:
            current_round = 1
            redis_client.set(round_key, current_round)
        
        return int(current_round)
    
    def start_new_round(self) -> int:
        """
        Start a new conversation round.
        
        Returns:
            int: The new round ID
        """
        round_key = f"{self.history_key}:current_round"
        current_round = redis_client.get(round_key) or 0
        new_round = int(current_round) + 1
        redis_client.set(round_key, new_round)
        
        # Add a system message indicating a new round
        self.add_system_message(f"[Beginning conversation round {new_round}]", round_id=new_round)
        
        return new_round
    
    def add_user_message(self, content: str, round_id=None) -> Dict[str, Any]:
        """
        Add a user message to the conversation history.
        
        Args:
            content: The message content
            round_id: Optional identifier for the conversation round
            
        Returns:
            dict: The added message object
        """
        message = {
            'role': 'user',
            'content': content,
            'timestamp': str(datetime.now()),
            'round_id': round_id or self._get_current_round_id()
        }
        
        redis_client.rpush(self.history_key, json.dumps(message))
        return message
    
    def add_assistant_message(self, content: str, provider=None, model=None, round_id=None, function_calls=None) -> Dict[str, Any]:
        """
        Add an assistant message to the conversation history.
        
        Args:
            content: The message content
            provider: Optional provider name
            model: Optional model name
            round_id: Optional identifier for the conversation round
            function_calls: Optional list of function calls
            
        Returns:
            dict: The added message object
        """
        message = {
            'role': 'assistant',
            'content': content,
            'timestamp': str(datetime.now()),
            'round_id': round_id or self._get_current_round_id()
        }
        
        if provider:
            message['provider'] = provider
        
        if model:
            message['model'] = model
            
        if function_calls:
            message['function_calls'] = function_calls
        
        redis_client.rpush(self.history_key, json.dumps(message))
        return message
    
    def add_function_result(self, name: str, arguments: Dict[str, Any], result: Any, round_id=None) -> Dict[str, Any]:
        """
        Add a function result to the conversation history.
        
        Args:
            name: The function name
            arguments: The function arguments
            result: The function result
            round_id: Optional identifier for the conversation round
            
        Returns:
            dict: The added message object
        """
        message = {
            'role': 'function',
            'name': name,
            'content': json.dumps(result),
            'arguments': json.dumps(arguments),
            'timestamp': str(datetime.now()),
            'round_id': round_id or self._get_current_round_id()
        }
        
        redis_client.rpush(self.history_key, json.dumps(message))
        return message
    
    def add_system_message(self, content: str, round_id=None) -> Dict[str, Any]:
        """
        Add a system message to the conversation history.
        
        Args:
            content: The message content
            round_id: Optional identifier for the conversation round
            
        Returns:
            dict: The added message object
        """
        message = {
            'role': 'system',
            'content': content,
            'timestamp': str(datetime.now()),
            'round_id': round_id or self._get_current_round_id()
        }
        
        redis_client.rpush(self.history_key, json.dumps(message))
        return message
    
    def prepare_messages(self, query=None, context=None, web_search=True, include_summary=True, max_tokens=None, auto_detect_search=True) -> List[Dict[str, Any]]:
        """
        Prepare messages for a model request with improved multi-round support.
        
        Args:
            query: The user's query or None if using existing conversation
            context: Optional additional context to include
            web_search: Whether to enrich with web search results
            include_summary: Whether to include summaries of previous rounds
            max_tokens: Maximum number of tokens to include
            auto_detect_search: Whether to automatically detect if web search is needed
            
        Returns:
            list: List of message objects ready for the model
        """
        # Start with the Finance Consultant system message
        messages = [{
            "role": "system",
            "content": FINANCE_CONSULTANT_SYSTEM_MESSAGE
        }]
        
        # Add current date
        current_date = datetime.now().strftime("%Y-%m-%d")
        messages.append({
            "role": "system",
            "content": f"Current date: {current_date}"
        })
        
        # Get current round
        current_round = self._get_current_round_id()
        
        # Get previous rounds summary if available
        previous_rounds_summary = None
        if include_summary and current_round > 1:
            previous_rounds_summary = self.get_previous_rounds_summary(current_round)
            if previous_rounds_summary:
                messages.append({
                    "role": "system",
                    "content": f"Summary of previous conversation rounds: {previous_rounds_summary}"
                })
        
        # Get conversation history for current round
        round_messages = self.get_history(max_tokens=max_tokens, round_id=current_round)
        messages.extend(round_messages)
        
        # Add the current query if provided and not already included
        if query and (not round_messages or round_messages[-1]["role"] != "user" or round_messages[-1]["content"] != query):
            messages.append({
                "role": "user",
                "content": query
            })
        
        # Add any additional context
        if context:
            messages.append({
                "role": "system",
                "content": context
            })
        
        # Enrich with web search if enabled
        if web_search and query:
            try:
                # Use round-aware search
                messages = enrich_messages_with_round_aware_search(
                    query, 
                    messages, 
                    round_messages, 
                    previous_rounds_summary, 
                    auto_detect_search
                )
            except Exception as e:
                logger.warning(f"Round-aware search failed: {str(e)}. Falling back to basic methods.")
                
                # Fall back to enhanced search
                try:
                    # Import here to avoid circular imports
                    from utils.enhanced_search import perform_web_search
                    
                    search_results, content = perform_web_search(query)
                    
                    if content:
                        messages.append({
                            "role": "system",
                            "content": f"Web search results for '{query}':\n\n{content}"
                        })
                        logger.info(f"Enhanced web search found {len(search_results)} results for query: {query}")
                        return messages
                except Exception as e:
                    logger.warning(f"Enhanced web search failed: {str(e)}. Falling back to basic search.")
                
                # Fall back to the original search method if all else fails
                raw_history = self.get_raw_history(round_id=current_round)
                messages = enrich_with_web_search(query, messages, self.session_id, raw_history)
        
        return messages
    
    def summarize_previous_rounds(self, current_round_id, model="gpt-3.5-turbo") -> Optional[str]:
        """
        Summarize previous conversation rounds to maintain context efficiently.
        
        Args:
            current_round_id: The current round ID
            model: Model to use for summarization
                
        Returns:
            str: Summary of previous rounds
        """
        if current_round_id <= 1:
            return None
        
        # Get messages from previous rounds
        history = redis_client.lrange(self.history_key, 0, -1) or []
        
        previous_rounds = []
        for msg in history:
            message_data = json.loads(msg)
            if message_data.get('round_id', 0) < current_round_id:
                previous_rounds.append({
                    "role": message_data.get('role'),
                    "content": message_data.get('content')
                })
        
        if not previous_rounds:
            return None
        
        # Use a model to generate a summary
        from providers.provider_factory import get_provider
        
        try:
            provider = get_provider("openai")
            
            summary_request = [
                {"role": "system", "content": "Summarize the following conversation concisely while preserving key information:"},
                *previous_rounds
            ]
            
            response = provider.chat_completion(
                messages=summary_request,
                model=model
            )
            
            summary = response["choices"][0]["message"]["content"]
            
            # Store the summary
            summary_key = f"{self.history_key}:summary:{current_round_id}"
            redis_client.set(summary_key, summary)
            
            return summary
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            return None
    
    def get_previous_rounds_summary(self, current_round_id) -> Optional[str]:
        """
        Get the summary of previous rounds, generating it if it doesn't exist.
        
        Args:
            current_round_id: The current round ID
                
        Returns:
            str: Summary of previous rounds
        """
        summary_key = f"{self.history_key}:summary:{current_round_id}"
        summary = redis_client.get(summary_key)
        
        if summary:
            return summary.decode('utf-8') if isinstance(summary, bytes) else summary
        
        # Generate summary if it doesn't exist
        return self.summarize_previous_rounds(current_round_id)
    
    def create_branch(self, branch_name, from_message_index=None) -> str:
        """
        Create a new conversation branch from the current conversation.
        
        Args:
            branch_name: Name for the new branch
            from_message_index: Optional index to branch from (default: latest message)
                
        Returns:
            str: New branch session ID
        """
        # Generate a new session ID for the branch
        branch_session_id = f"{self.session_id}:{branch_name}"
        branch_history_key = f"{CONVERSATION_KEY_PREFIX}{branch_session_id}"
        
        # Get current history
        history = redis_client.lrange(self.history_key, 0, -1) or []
        
        # Copy messages up to the specified index
        if from_message_index is not None:
            history = history[:from_message_index+1]
        
        # Store in the new branch
        for msg in history:
            redis_client.rpush(branch_history_key, msg)
        
        # Add a system message indicating this is a branch
        branch_manager = ConversationManager(branch_session_id)
        branch_manager.add_system_message(f"[This is a branch '{branch_name}' from conversation {self.session_id}]")
        
        return branch_session_id
    
    def export_conversation(self, format="json") -> Optional[str]:
        """
        Export the entire conversation history.
        
        Args:
            format: Export format (json, text, markdown)
                
        Returns:
            str: Formatted conversation history
        """
        history = redis_client.lrange(self.history_key, 0, -1) or []
        
        if format == "json":
            return json.dumps([json.loads(msg) for msg in history], indent=2)
        
        elif format == "text":
            text_output = []
            for msg in history:
                message_data = json.loads(msg)
                role = message_data.get('role', '').upper()
                content = message_data.get('content', '')
                text_output.append(f"{role}: {content}\n")
            return "\n".join(text_output)
        
        elif format == "markdown":
            md_output = []
            for msg in history:
                message_data = json.loads(msg)
                role = message_data.get('role', '')
                content = message_data.get('content', '')
                
                if role == "user":
                    md_output.append(f"**User**: {content}\n")
                elif role == "assistant":
                    md_output.append(f"**Assistant**: {content}\n")
                elif role == "system":
                    md_output.append(f"*System: {content}*\n")
                elif role == "function":
                    name = message_data.get('name', '')
                    md_output.append(f"*Function {name}: {content}*\n")
            
            return "\n".join(md_output)
        
        return None
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the conversation.
                
        Returns:
            dict: Conversation statistics
        """
        history = redis_client.lrange(self.history_key, 0, -1) or []
        
        stats = {
            "total_messages": len(history),
            "user_messages": 0,
            "assistant_messages": 0,
            "system_messages": 0,
            "function_calls": 0,
            "rounds": 0,
            "rounds_data": {},
            "total_tokens": 0,
            "start_time": None,
            "last_time": None
        }
        
        rounds_seen = set()
        
        for msg in history:
            message_data = json.loads(msg)
            role = message_data.get('role')
            
            if role == "user":
                stats["user_messages"] += 1
            elif role == "assistant":
                stats["assistant_messages"] += 1
                # Count function calls
                if 'function_calls' in message_data:
                    stats["function_calls"] += len(message_data['function_calls'])
            elif role == "system":
                stats["system_messages"] += 1
            elif role == "function":
                stats["function_calls"] += 1
            
            # Track tokens (simplified)
            content = message_data.get('content', '')
            tokens = len(content.split())
            stats["total_tokens"] += tokens
            
            # Track rounds
            round_id = message_data.get('round_id', 0)
            if round_id not in rounds_seen:
                rounds_seen.add(round_id)
                stats["rounds"] += 1
                stats["rounds_data"][str(round_id)] = {"messages": 0, "tokens": 0, "function_calls": 0}
            
            stats["rounds_data"][str(round_id)]["messages"] += 1
            stats["rounds_data"][str(round_id)]["tokens"] += tokens
            
            # Count function calls per round
            if role == "function" or (role == "assistant" and 'function_calls' in message_data):
                if role == "function":
                    stats["rounds_data"][str(round_id)]["function_calls"] += 1
                else:
                    stats["rounds_data"][str(round_id)]["function_calls"] += len(message_data['function_calls'])
            
            # Track timestamps
            timestamp = message_data.get('timestamp')
            if timestamp:
                try:
                    time = datetime.datetime.fromisoformat(timestamp)
                    if stats["start_time"] is None or time < stats["start_time"]:
                        stats["start_time"] = str(time)
                    if stats["last_time"] is None or time > stats["last_time"]:
                        stats["last_time"] = str(time)
                except ValueError:
                    pass
        
        return stats
    
    def clear_history(self, round_id=None) -> None:
        """
        Clear the conversation history for this session.
        
        Args:
            round_id: Optional round ID to clear only messages from that round
        """
        if round_id is None:
            # Clear all history
            redis_client.delete(self.history_key)
            # Clear all related keys
            keys = redis_client.keys(f"{self.history_key}:*")
            if keys:
                redis_client.delete(*keys)
            logger.info(f"Cleared all conversation history for session {self.session_id}")
        else:
            # Clear only messages from the specified round
            history = redis_client.lrange(self.history_key, 0, -1) or []
            new_history = []
            
            for msg in history:
                message_data = json.loads(msg)
                if message_data.get('round_id') != round_id:
                    new_history.append(msg)
            
            # Delete the old history
            redis_client.delete(self.history_key)
            
            # Add back the filtered messages
            if new_history:
                redis_client.rpush(self.history_key, *new_history)
            
            # Clear the summary for this round
            summary_key = f"{self.history_key}:summary:{round_id}"
            redis_client.delete(summary_key)
            
            logger.info(f"Cleared conversation history for round {round_id} in session {self.session_id}")
