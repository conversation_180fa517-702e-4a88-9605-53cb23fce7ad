FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    APP_MODE=async \
    PORT=8080

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose port
EXPOSE ${PORT}

# Run the application
CMD ["sh", "-c", "python run.py --mode ${APP_MODE} --host 0.0.0.0 --port ${PORT}"]

# Usage:
# Build: docker build -t go-gpt-backend .
# Run (sync mode): docker run -p 5000:5000 -e PORT=5000 --env-file .env go-gpt-backend
# Run (async mode): docker run -p 5000:5000 -e PORT=5000 -e APP_MODE=async --env-file .env go-gpt-backend
