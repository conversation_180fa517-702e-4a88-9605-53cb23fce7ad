import os
from flask import Flask
from api import api
from middleware import setup_rate_limiter
from utils.logging import logger
from config.settings import settings
# Import sample functions to register them

def create_app():
    """
    Create and configure the Flask application.
    
    Returns:
        Flask: The configured Flask application
    """
    app = Flask(__name__)
    
    # Initialize OpenAI Assistant
    from openai import OpenAI
    from utils.assistant_utils import setup_vector_store
    from utils.assistant_utils import initialize_assistant
    client = OpenAI(
        api_key=os.getenv('OPENAI_API_KEY'),
        base_url=os.getenv('OPENAI_API_URL'),
        default_headers={"OpenAI-Beta": "assistants=v2"}
    )
    enable_file_search = False
    if settings.ENABLE_FILE_SEARCH:
        # Set up vector store and handle failure gracefully
        enable_file_search = setup_vector_store(client, os.getenv('VECTOR_NAME'))
        if not enable_file_search:
            logger.warning("Failed to set up vector store, continuing without file_search capability")
    assistant_id = initialize_assistant(client, enable_file_search=enable_file_search)
    logger.info(f"Initialized OpenAI Assistant with ID: {assistant_id}")
    
    # Register API blueprint
    app.register_blueprint(api)
    
    # Set up rate limiting
    setup_rate_limiter(app)
    
    return app

if __name__ == '__main__':
    app = create_app()
    port = settings.PORT
    logger.info(f"Starting server on port {port}")
    app.run(host='0.0.0.0', port=port)
