"""
Schema registry model for tracking database schemas.

This module provides a model for tracking database schemas in the application.
It is used by the multi-schema database manager to keep track of schemas and
their metadata.
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, func
from sqlalchemy.ext.declarative import declarative_base

from models.base import Base, BaseModel


class SchemaRegistry(Base, BaseModel):
    """
    Schema registry model for tracking database schemas.
    
    This model is used to track database schemas in the application. It stores
    metadata about each schema, such as its name, description, and whether it
    is active.
    """
    
    __tablename__ = 'schema_registry'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    schema_name = Column(String(100), nullable=False, unique=True)
    description = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def __init__(self, schema_name, description=None, is_active=True):
        """
        Initialize a new schema registry entry.
        
        Args:
            schema_name: Name of the schema
            description: Optional description of the schema
            is_active: Whether the schema is active
        """
        self.schema_name = schema_name
        self.description = description
        self.is_active = is_active
    
    def __repr__(self):
        """
        Get a string representation of the schema registry entry.
        
        Returns:
            str: String representation
        """
        return f"<SchemaRegistry(schema_name='{self.schema_name}', is_active={self.is_active})>"
