from dataclasses import dataclass
from typing import Optional, Dict, Any
from datetime import datetime

@dataclass
class Message:
    """
    Represents a message in a conversation.
    """
    role: str
    content: str
    timestamp: datetime = None
    provider: Optional[str] = None
    model: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the message to a dictionary.
        
        Returns:
            Dict: The message as a dictionary
        """
        result = {
            'role': self.role,
            'content': self.content,
            'timestamp': self.timestamp.isoformat()
        }
        
        if self.provider:
            result['provider'] = self.provider
            
        if self.model:
            result['model'] = self.model
            
        if self.metadata:
            result['metadata'] = self.metadata
            
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """
        Create a message from a dictionary.
        
        Args:
            data: The dictionary containing message data
            
        Returns:
            Message: The created message
        """
        timestamp = data.get('timestamp')
        if timestamp and isinstance(timestamp, str):
            timestamp = datetime.fromisoformat(timestamp)
            
        return cls(
            role=data['role'],
            content=data['content'],
            timestamp=timestamp,
            provider=data.get('provider'),
            model=data.get('model'),
            metadata=data.get('metadata')
        )
