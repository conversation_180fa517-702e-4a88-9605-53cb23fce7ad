"""
Database models for financial calendar events.
"""
from typing import Dict, Any
from sqlalchemy import <PERSON>umn, Integer, String, Float, Date, DateTime, BigInteger, ForeignKey, UniqueConstraint, Text, Boolean, JSON
from sqlalchemy.sql import func

from models.base import Base

class FinancialCalendarEvent(Base):
    """
    Financial calendar event model representing economic events like CPI, PPI, etc.
    """
    __tablename__ = "financial_calendar_events"
    __table_args__ = (
        UniqueConstraint('date', 'country', 'event_name', name='_date_country_event_name'),
        {'schema': None}  # Schema will be set dynamically
    )
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    event_name = Column(String(255), nullable=False, index=True)
    event_type = Column(String(100), nullable=False, index=True)  # e.g., 'CPI', 'PPI', 'NFP'
    country = Column(String(50), nullable=False, index=True)
    date = Column(Date, nullable=False, index=True)
    time = Column(String(50), nullable=True)  # Time of day if available
    date_time = Column(DateTime)  # Combined date and time
    actual = Column(String(50), nullable=True)
    forecast = Column(String(50), nullable=True)
    previous = Column(String(50), nullable=True)
    impact = Column(String(20), nullable=True, index=True)  # 'High', 'Medium', 'Low'
    description = Column(Text, nullable=True)
    is_released = Column(Boolean, default=False, nullable=False)
    is_completed = Column(Boolean, default=False, nullable=False) # Job completed or not
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    retry_count = Column(Integer, default=0, nullable=False)
    
    @classmethod
    def set_schema(cls, schema_name):
        """
        Set the schema for this model.
        
        Args:
            schema_name: Name of the schema to use
        """
        cls.__table__.schema = schema_name
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the model to a dictionary.
        """
        return {
            "id": self.id,
            "event_name": self.event_name,
            "event_type": self.event_type,
            "country": self.country,
            "date": self.date.isoformat() if self.date else None,
            "time": self.time,
            "date_time": self.date_time.isoformat() if self.date_time else None,
            "actual": self.actual,
            "forecast": self.forecast,
            "previous": self.previous,
            "impact": self.impact,
            "description": self.description,
            "is_released": self.is_released,
            "is_completed": self.is_completed,
            "last_updated": self.last_updated.isoformat() if self.last_updated else None,
            "retry_count": self.retry_count
        }
