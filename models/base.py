"""
Base model for SQLAlchemy models.

This module provides a base model class that all SQLAlchemy models should inherit from.
It provides common functionality for all models, such as serialization and deserialization.
"""

from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, DateTime, func
from datetime import datetime
import json

# Create a base class for all models
Base = declarative_base()


class BaseModel:
    """
    Base model for SQLAlchemy models.
    
    This class provides common functionality for all models, such as
    serialization and deserialization.
    """
    
    def to_dict(self):
        """
        Convert the model to a dictionary.
        
        Returns:
            dict: Dictionary representation of the model
        """
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            
            # Handle datetime objects
            if isinstance(value, datetime):
                value = value.isoformat()
            
            result[column.name] = value
        
        return result
    
    def to_json(self):
        """
        Convert the model to a JSON string.
        
        Returns:
            str: JSON string representation of the model
        """
        return json.dumps(self.to_dict())
    
    @classmethod
    def from_dict(cls, data):
        """
        Create a model instance from a dictionary.
        
        Args:
            data: Dictionary containing model data
            
        Returns:
            BaseModel: Model instance
        """
        instance = cls()
        
        for column in cls.__table__.columns:
            if column.name in data:
                value = data[column.name]
                
                # Handle datetime strings
                if isinstance(value, str) and column.type.python_type == datetime:
                    try:
                        value = datetime.fromisoformat(value)
                    except ValueError:
                        # If the string is not in ISO format, try to parse it
                        try:
                            value = datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
                        except ValueError:
                            # If we can't parse it, just use the string
                            pass
                
                setattr(instance, column.name, value)
        
        return instance
    
    @classmethod
    def from_json(cls, json_str):
        """
        Create a model instance from a JSON string.
        
        Args:
            json_str: JSON string containing model data
            
        Returns:
            BaseModel: Model instance
        """
        data = json.loads(json_str)
        return cls.from_dict(data)
