"""
Database model for storing user-specific query information.
"""
import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Float, Date, DateTime, BigInteger, ForeignKey, UniqueConstraint, Text, Boolean, JSON, Index
from sqlalchemy.sql import func

from models.base import Base

class UserQuery(Base):
    """
    UserQuery model representing user-specific query information.
    """
    __tablename__ = "user_query"
    __table_args__ = (
        Index('idx_uid', 'uid'),
        Index('idx_query_id', 'query_id'),
        {'schema': None}  # Schema will be set dynamically
    )
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    uid = Column(String(100), nullable=False)
    query_id = Column(BigInteger, ForeignKey("query.id"), nullable=False)
    query_time = Column(DateTime, default=func.now(), nullable=False)
    completed_time = Column(DateTime, nullable=True)
    status = Column(String(20), nullable=False, default="pending")
    read = Column(Boolean, nullable=False, default=False)
    
    @classmethod
    def set_schema(cls, schema_name):
        """
        Set the schema for this model.
        
        Args:
            schema_name: Name of the schema to use
        """
        cls.__table__.schema = schema_name
    
    def __repr__(self):
        return f"<UserQuery(id={self.id}, uid='{self.uid}', status='{self.status}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the model to a dictionary.
        """
        return {
            "id": self.id,
            "uid": self.uid,
            "query_id": self.query_id,
            "query_time": self.query_time.isoformat() if self.query_time else None,
            "completed_time": self.completed_time.isoformat() if self.completed_time else None,
            "status": self.status,
            "read": self.read
        }
