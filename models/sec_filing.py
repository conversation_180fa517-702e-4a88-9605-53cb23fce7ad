"""
Database models for SEC filings.
"""
import datetime
from typing import Op<PERSON>, Dict, Any
from sqlalchemy import <PERSON>umn, Integer, String, Float, Date, DateTime, BigInteger, ForeignKey, UniqueConstraint, Text, Boolean, Table
from sqlalchemy.sql import func

from models.base import Base

class Stock(Base):
    """
    Stock model representing basic stock information.
    """
    __tablename__ = "stocks"
    __table_args__ = {'schema': None}  # Schema will be set dynamically
    
    # Primary identifiers
    ticker = Column(String(20), primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    
    @classmethod
    def set_schema(cls, schema_name):
        """
        Set the schema for this model.
        
        Args:
            schema_name: Name of the schema to use
        """
        cls.__table__.schema = schema_name
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the model to a dictionary.
        """
        return {
            "ticker": self.ticker,
            "name": self.name,
            "last_updated": self.last_updated.isoformat() if self.last_updated else None
        }

class SECFiling(Base):
    """
    SEC filing model representing metadata about SEC filings.
    """
    __tablename__ = "sec_filings"
    __table_args__ = (
        UniqueConstraint('ticker', 'accession_number', name='_ticker_accession_uc'),
        {'schema': None}  # Schema will be set dynamically
    )
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    ticker = Column(String(20), ForeignKey("stocks.ticker"), nullable=False, index=True)
    cik = Column(String(20), nullable=False, index=True)
    accession_number = Column(String(20), nullable=False, index=True)
    filing_type = Column(String(20), nullable=False, index=True)  # e.g., '10-K', '10-Q'
    filing_date = Column(Date, nullable=False, index=True)
    report_date = Column(Date, nullable=True, index=True)
    fiscal_year = Column(Integer, nullable=True)
    fiscal_quarter = Column(Integer, nullable=True)
    url = Column(Text, nullable=True)  # TXT URL
    html_url = Column(Text, nullable=True)  # HTML URL
    is_processed = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    @classmethod
    def set_schema(cls, schema_name):
        """
        Set the schema for this model.
        
        Args:
            schema_name: Name of the schema to use
        """
        cls.__table__.schema = schema_name
        # Also update the ForeignKey reference
        for fk in cls.__table__.foreign_keys:
            if fk.column.table.name == "stocks":
                fk.target_fullname = f"{schema_name}.stocks.ticker"
    
    def __repr__(self):
        return f"<SECFiling(ticker='{self.ticker}', filing_type='{self.filing_type}', filing_date='{self.filing_date}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the model to a dictionary.
        """
        return {
            "id": self.id,
            "ticker": self.ticker,
            "cik": self.cik,
            "accession_number": self.accession_number,
            "filing_type": self.filing_type,
            "filing_date": self.filing_date.isoformat() if self.filing_date else None,
            "report_date": self.report_date.isoformat() if self.report_date else None,
            "fiscal_year": self.fiscal_year,
            "fiscal_quarter": self.fiscal_quarter,
            "url": self.url,
            "html_url": self.html_url,
            "is_processed": self.is_processed,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class SECFilingSection(Base):
    """
    SEC filing section model representing sections of SEC filings.
    """
    __tablename__ = "sec_filing_sections"
    __table_args__ = (
        UniqueConstraint('filing_id', 'section_name', name='_filing_section_uc'),
        {'schema': None}  # Schema will be set dynamically
    )
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    filing_id = Column(BigInteger, ForeignKey("sec_filings.id"), nullable=False, index=True)
    section_name = Column(String(100), nullable=False, index=True)  # e.g., 'RISK_FACTORS', 'MD_AND_A'
    section_text = Column(Text, nullable=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    @classmethod
    def set_schema(cls, schema_name):
        """
        Set the schema for this model.
        
        Args:
            schema_name: Name of the schema to use
        """
        cls.__table__.schema = schema_name
        # Also update the ForeignKey reference
        for fk in cls.__table__.foreign_keys:
            if fk.column.table.name == "sec_filings":
                fk.target_fullname = f"{schema_name}.sec_filings.id"
    
    def __repr__(self):
        return f"<SECFilingSection(filing_id={self.filing_id}, section_name='{self.section_name}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the model to a dictionary.
        """
        return {
            "id": self.id,
            "filing_id": self.filing_id,
            "section_name": self.section_name,
            "section_text": self.section_text[:100] + "..." if self.section_text and len(self.section_text) > 100 else self.section_text,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
