"""
Database model for storing user queries and LLM answers.
"""
import datetime
import json
from typing import Optional, Dict, Any, List
from sqlalchemy import Column, Integer, String, Float, Date, DateTime, BigInteger, ForeignKey, UniqueConstraint, Text, Boolean, JSON
from sqlalchemy.sql import func
from sqlalchemy.types import TypeDecorator

from models.base import Base

# Custom JSON type for databases that don't support JSON natively
class JSONType(TypeDecorator):
    """
    Custom JSON type for SQLAlchemy that serializes/deserializes JSON data.
    """
    impl = Text

    def process_bind_param(self, value, dialect):
        if value is not None:
            return json.dumps(value)
        return None

    def process_result_value(self, value, dialect):
        if value is not None:
            return json.loads(value)
        return None

class Query(Base):
    """
    Query model representing user queries and LLM answers.
    """
    __tablename__ = "query"
    __table_args__ = (
        {'schema': None}  # Schema will be set dynamically
    )
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    thread_id = Column(String(100), nullable=False)
    run_id = Column(String(100), nullable=True)
    user_query = Column(Text, nullable=False)
    llm_answer = Column(Text, nullable=True)
    status = Column(String(20), nullable=False, default="pending")
    resources = Column(JSON().with_variant(JSONType, "mysql"), nullable=True)
    model = Column(String(100), nullable=True)
    system_prompt = Column(Text, nullable=True)
    type = Column(String(50), nullable=True)
    cacheable = Column(Boolean, nullable=False, default=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    @classmethod
    def set_schema(cls, schema_name):
        """
        Set the schema for this model.
        
        Args:
            schema_name: Name of the schema to use
        """
        cls.__table__.schema = schema_name
    
    def __repr__(self):
        return f"<Query(id={self.id}, status='{self.status}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the model to a dictionary.
        """
        return {
            "id": self.id,
            "user_query": self.user_query,
            "llm_answer": self.llm_answer,
            "status": self.status,
            "resources": self.resources or [],
            "model": self.model,
            "system_prompt": self.system_prompt,
            "type": self.type,
            "cacheable": self.cacheable,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
    
    def add_resource(self, function_name: str, arguments: Dict[str, Any], output: Any) -> None:
        """
        Add a resource to the resources list.
        
        Args:
            function_name: The name of the function that was called
            arguments: The arguments passed to the function
            output: The output of the function call
        """
        if self.resources is None:
            self.resources = []
            
        resource = {
            "function_name": function_name,
            "arguments": arguments,
            "output": output,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        self.resources.append(resource)
