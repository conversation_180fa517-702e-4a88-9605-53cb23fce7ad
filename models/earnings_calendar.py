"""
Database models for earnings calendar events.
"""
from typing import Dict, Any
from sqlalchemy import Column, Integer, String, DateTime, Text
from sqlalchemy.sql import func

from models.base import Base

class EarningsCalendarEvent(Base):
    """
    Earnings calendar event model representing company earnings announcements.
    """
    __tablename__ = "earnings_calendar_events"
    
    id = Column(Integer, primary_key=True)
    company = Column(String(255), nullable=False)
    company_name = Column(String(255), nullable=False)
    ticker = Column(String(32), nullable=False)
    eps_forecast = Column(String(50), nullable=True)
    revenue_forecast = Column(String(50), nullable=True)
    eps_actual = Column(String(50), nullable=True)
    revenue_actual = Column(String(50), nullable=True)
    market_cap = Column(Text, nullable=True)
    release_date = Column(DateTime, default=func.now())
    release_time = Column(Integer, nullable=True) # 0: unknown, 1: pre-market, 2: after-market
    
    @classmethod
    def set_schema(cls, schema_name):
        """
        Set the schema for this model.
        
        Args:
            schema_name: Name of the schema to use
        """
        cls.__table__.schema = schema_name
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the model to a dictionary.
        """
        return {
            "id": self.id,
            "company": self.company,
            "company_name": self.company_name,
            "ticker": self.ticker,
            "eps_forecast": self.eps_forecast,
            "revenue_forecast": self.revenue_forecast,
            "eps_actual": self.eps_actual,
            "revenue_actual": self.revenue_actual,
            "market_cap": self.market_cap,
            "release_date": self.release_date.isoformat() if self.release_date else None,
            "release_time": self.release_time
        }
