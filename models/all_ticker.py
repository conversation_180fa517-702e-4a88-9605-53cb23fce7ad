"""
Database models for earnings calendar events.
"""
from sqlalchemy import <PERSON><PERSON><PERSON>, Boolean, String

from models.base import Base

class AllTicker(Base):
    __tablename__ = 'all_tickers'

    ticker = Column(String(32), primary_key=True)
    prev_ticker = Column(String(32))
    country = Column(String(100))
    exchange = Column(String(100))
    name = Column(String(200))
    logo = Column(String(200))
    is_sp_500 = Column(Boolean, default=False)
    is_nasdap_100 = Column(Boolean, default=False)
    is_nasdaq_composite = Column(Boolean, default=False)
    is_dow_jones = Column(Boolean, default=False)
    is_russell_2000 = Column(Boolean, default=False)
    is_facts_processed = Column(Boolean, default=False)
