"""
Test script to verify the fix for handling async generators in the async_openai_assistant_provider.py file.
"""
import asyncio
import json
from providers.async_openai_assistant_provider import AsyncOpenAIAssistantProvider

async def test_event_handler_methods():
    """Test that the EventHandler methods now return values instead of yielding them."""
    print("Testing async generator fix in EventHandler methods...")
    
    # Create a mock text object
    class MockText:
        def __init__(self, value):
            self.value = value
    
    # Create a mock delta object
    class MockDelta:
        def __init__(self, value):
            self.value = value
    
    # Create a mock snapshot object
    class MockSnapshot:
        def __init__(self):
            pass
    
    # Create a mock stream object
    class MockStream:
        def __init__(self):
            self.current_run = type('obj', (object,), {'id': 'test_run_id'})
    
    # Define a simplified version of the EventHandler class with our fix
    class EventHandler:
        async def on_text_created(self, text):
            print(f"on_text_created called with text: {text.value}")
            # Return the response instead of yielding it
            return {
                "choices": [
                    {
                        "delta": {
                            "content": text.value
                        }
                    }
                ],
                "thread_id": "test_thread_id",
                "run_id": "test_run_id"
            }
        
        async def on_text_delta(self, delta, snapshot):
            print(f"on_text_delta called with delta: {delta.value}")
            # Return the response instead of yielding it
            if delta.value:
                return {
                    "choices": [
                        {
                            "delta": {
                                "content": delta.value
                            }
                        }
                    ],
                    "thread_id": "test_thread_id",
                    "run_id": "test_run_id",
                    "is_delta": True
                }
            return None
    
    # Create instances of our test objects
    handler = EventHandler()
    text = MockText("This is a test message")
    delta = MockDelta("This is a test delta")
    empty_delta = MockDelta("")
    snapshot = MockSnapshot()
    
    # Test on_text_created
    print("\nTesting on_text_created:")
    result = await handler.on_text_created(text)
    print(f"Result: {result}")
    assert result["choices"][0]["delta"]["content"] == "This is a test message", "Failed to get the correct content from on_text_created"
    
    # Test on_text_delta with content
    print("\nTesting on_text_delta with content:")
    result = await handler.on_text_delta(delta, snapshot)
    print(f"Result: {result}")
    assert result["choices"][0]["delta"]["content"] == "This is a test delta", "Failed to get the correct content from on_text_delta"
    
    # Test on_text_delta with empty content
    print("\nTesting on_text_delta with empty content:")
    result = await handler.on_text_delta(empty_delta, snapshot)
    print(f"Result: {result}")
    assert result is None, "Failed to get None from on_text_delta with empty content"
    
    print("\nAll tests passed! The fix for handling async generators works correctly.")

# Run the test
if __name__ == "__main__":
    asyncio.run(test_event_handler_methods())
