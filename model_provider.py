import os
from abc import ABC, abstractmethod
from typing import Dict, Any
from openai import OpenAI

class ModelProvider(ABC):
    @abstractmethod
    def chat_completion(self, messages: list, model: str, **kwargs) -> Dict[str, Any]:
        pass

class DeepSeekProvider(ModelProvider):
    def __init__(self):
        self.client = OpenAI(
            api_key=os.getenv('DEEPSEEK_API_KEY'),
            base_url=os.getenv('DEEPSEEK_API_URL')
        )

    def chat_completion(self, messages: list, model: str, **kwargs) -> Dict[str, Any]:
        stream = kwargs.pop('stream', True)  # Default to streaming
        response = self.client.chat.completions.create(
            model=model,
            messages=messages,
            stream=stream,
            **kwargs
        )
        
        if stream:
            return response  # Return the streaming response directly
        else:
            return response.model_dump()

class OpenAIProvider(ModelProvider):
    def __init__(self):
        self.client = OpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_API_URL')
        )

    def chat_completion(self, messages: list, model: str, **kwargs) -> Dict[str, Any]:
        stream = kwargs.pop('stream', True)  # Default to streaming
        response = self.client.chat.completions.create(
            model=model,
            messages=messages,
            stream=stream,
            **kwargs
        )
        
        if stream:
            return response  # Return the streaming response directly
        else:
            return response.model_dump()

class AnthropicProvider(ModelProvider):
    def __init__(self):
        self.client = OpenAI(
            api_key=os.getenv('ANTHROPIC_API_KEY'),
            base_url=os.getenv('ANTHROPIC_API_URL')
        )

    def chat_completion(self, messages: list, model: str, **kwargs) -> Dict[str, Any]:
        stream = kwargs.pop('stream', True)  # Default to streaming
        response = self.client.chat.completions.create(
            model=model,
            messages=messages,
            stream=stream,
            **kwargs
        )
        
        if stream:
            return response  # Return the streaming response directly
        else:
            return response.model_dump()

def get_provider(provider_name: str) -> ModelProvider:
    providers = {
        'deepseek': DeepSeekProvider,
        'openai': OpenAIProvider,
        'anthropic': AnthropicProvider
    }
    
    provider_class = providers.get(provider_name.lower())
    if not provider_class:
        raise ValueError(f'Unknown provider: {provider_name}')
    
    return provider_class()
