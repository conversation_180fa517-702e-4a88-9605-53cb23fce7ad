"""
FastAPI routes for the OpenAI Assistant API.
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Request, HTTPException, Depends, Head<PERSON>
from pydantic import BaseModel, <PERSON>
from typing import Optional
from services.async_assistant_service import AsyncAssistantService
from utils.logging import logger
from middleware.rate_limiting import async_rate_limit
from middleware.auth import async_require_auth

# Create a router for FastAPI assistant routes
router = APIRouter()
assistant_service = AsyncAssistantService()

# Pydantic models for request/response
class AssistantQueryRequest(BaseModel):
    query: str = Field(..., description="User's question or message")
    thread_id: Optional[str] = Field(None, description="Optional thread ID for continuing a conversation")
    model: str = Field("gpt-4-turbo-preview", description="Model name to use")

class ThreadResponse(BaseModel):
    thread_id: str = Field(..., description="Thread ID")
    created: bool = Field(..., description="Whether the thread was created successfully")
    error: Optional[str] = Field(None, description="Error message if any")

@router.post('/assistant')
@async_require_auth
async def assistant_query(request: AssistantQueryRequest, auth0_sub: str = Header(None)):
    """
    Handle assistant API queries asynchronously.
    
    Args:
        request: The query request
        
    Returns:
        JSON response with assistant's answer
    """
    try:
        # Process the query
        result = await assistant_service.query(
            user_query=request.query,
            thread_id=request.thread_id,
            model=request.model,
            auth0_sub=auth0_sub
        )
        
        # Check for errors
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error in async assistant query: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post('/pub/assistant')
async def public_assistant_query(request: AssistantQueryRequest):
    """
    Handle public (unauthenticated) assistant API queries asynchronously.
    
    Args:
        request: The query request
        
    Returns:
        JSON response with assistant's answer
    """
    try:
        # Process the query without auth0_sub (no thread persistence)
        result = await assistant_service.query(
            user_query=request.query,
            thread_id=request.thread_id,
            model=request.model
        )
        
        # Check for errors
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error in async assistant query: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post('/assistant/thread')
@async_require_auth
async def create_thread(auth0_sub: str = Header(None)):
    """
    Create a new conversation thread asynchronously.
    
    Returns:
        JSON response with thread ID
    """
    try:
        result = await assistant_service.create_thread()
        
        # If thread was created successfully and auth0_sub is provided, store in Redis
        if result.get('created') and auth0_sub and result.get('thread_id'):
            from utils.async_redis_client import get_async_redis_client
            redis_client = await get_async_redis_client()
            await redis_client.set(f"thread:{auth0_sub}", result.get('thread_id'))
            logger.info(f"Stored thread ID {result.get('thread_id')} for user {auth0_sub} in Redis")
        
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error creating thread: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post('/pub/assistant/thread')
async def public_create_thread():
    """
    Create a new conversation thread asynchronously (public endpoint).
    
    Returns:
        JSON response with thread ID
    """
    try:
        result = await assistant_service.create_thread()
        
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error creating thread: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete('/assistant/thread/{thread_id}')
@async_require_auth
async def delete_thread(thread_id: str, auth0_sub: str = Header(None)):
    """
    Delete a conversation thread asynchronously.
    
    Args:
        thread_id: The thread ID to delete
        
    Returns:
        JSON response with result
    """
    try:
        result = await assistant_service.delete_thread(thread_id)
        
        # If thread was deleted successfully and auth0_sub is provided, remove from Redis
        if result.get('deleted') and auth0_sub:
            from utils.async_redis_client import get_async_redis_client
            redis_client = await get_async_redis_client()
            await redis_client.delete(f"thread:{auth0_sub}")
            logger.info(f"Removed thread ID for user {auth0_sub} from Redis")
        
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error deleting thread {thread_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete('/pub/assistant/thread/{thread_id}')
async def public_delete_thread(thread_id: str):
    """
    Delete a conversation thread asynchronously (public endpoint).
    
    Args:
        thread_id: The thread ID to delete
        
    Returns:
        JSON response with result
    """
    try:
        result = await assistant_service.delete_thread(thread_id)
        
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error deleting thread {thread_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get('/assistant/thread/{thread_id}/messages')
@async_require_auth
async def get_thread_messages(thread_id: str):
    """
    Get all messages in a thread asynchronously.
    
    Args:
        thread_id: The thread ID
        
    Returns:
        JSON response with messages
    """
    try:
        result = await assistant_service.get_thread_messages(thread_id)
        
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error getting messages for thread {thread_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get('/pub/assistant/thread/{thread_id}/messages')
async def public_get_thread_messages(thread_id: str):
    """
    Get all messages in a thread asynchronously (public endpoint).
    
    Args:
        thread_id: The thread ID
        
    Returns:
        JSON response with messages
    """
    try:
        result = await assistant_service.get_thread_messages(thread_id)
        
        if result.get('error'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except Exception as e:
        logger.error(f"Error getting messages for thread {thread_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
