"""
Async API routes for user query operations.
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException, Query
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Optional
from datetime import datetime
from services.async_user_query_service import AsyncUserQueryService
from utils.logging import logger
from middleware.auth import async_require_auth

# Create a router for FastAPI user query routes
router = APIRouter()
user_query_service = AsyncUserQueryService()

# Pydantic models for response
class QueryMetadata(BaseModel):
    """Metadata associated with a query"""
    reference_urls: Optional[List[Dict[str, str]]] = Field(None, description="Reference URLs used in the answer")
    follow_up_questions: Optional[List[str]] = Field(None, description="Suggested follow-up questions")

class CompanyFact(BaseModel):
    """Company financial facts"""
    ticker: Optional[str] = Field(None, description="Company ticker symbol")
    name: Optional[str] = Field(None, description="Company name")
    facts: Optional[Dict[str, Any]] = Field(None, description="Financial facts about the company")

class UserQueryResponse(BaseModel):
    """User query response model"""
    id: int = Field(..., description="The user query ID")
    query: str = Field(..., description="The user's query text")
    timestamp: str = Field(..., description="Timestamp when the query was made")
    answer: str = Field(..., description="The answer to the query")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata associated with the query")
    company_fact: Optional[Dict[str, Any]] = Field(None, description="Company financial facts")
    tickers: List[str] = Field([], description="List of ticker symbols mentioned in the query")

class PaginationInfo(BaseModel):
    """Pagination information"""
    total_count: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    total_pages: int = Field(..., description="Total number of pages")

class TickerFactsResponse(BaseModel):
    """Response model for ticker facts"""
    success: bool = Field(..., description="Whether the request was successful")
    ticker: str = Field(..., description="The ticker symbol")
    company_fact: Optional[Dict[str, Any]] = Field(None, description="Company financial facts")
    error: Optional[str] = Field(None, description="Error message if any")

class UserQueriesResponse(BaseModel):
    """Response model for user queries with pagination"""
    queries: List[Dict[str, Any]] = Field(..., description="List of user queries")
    pagination: PaginationInfo = Field(..., description="Pagination information")
    error: Optional[str] = Field(None, description="Error message if any")

@router.patch('/user_query/{id}')
@async_require_auth
async def update_user_query_read_status(
    id: int,
    auth0_sub: str = Header(None)
):
    """
    Update the read status of a user query from 0 to 1.

    Args:
        id: User query ID
        auth0_sub: User identifier from request header

    Returns:
        JSON response with success status
    """
    try:
        if not auth0_sub:
            raise HTTPException(status_code=401, detail="User identifier (auth0_sub) is required")

        result = await user_query_service.update_read_status(
            id=id,
            uid=auth0_sub
        )

        # Check for success
        if not result.get('success'):
            if "not found" in result.get('message', ''):
                raise HTTPException(status_code=404, detail=result.get('message'))
            else:
                raise HTTPException(status_code=500, detail=result.get('message'))

        return result

    except HTTPException as he:
        # Re-raise HTTP exceptions
        raise he
    except Exception as e:
        logger.error(f"Error in update_user_query_read_status endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete('/user_query/{id}')
@async_require_auth
async def delete_user_query(
    id: int,
    auth0_sub: str = Header(None)
):
    """
    Delete a user query by ID.

    Args:
        id: User query ID
        auth0_sub: User identifier from request header

    Returns:
        JSON response with success status
    """
    try:
        if not auth0_sub:
            raise HTTPException(status_code=401, detail="User identifier (auth0_sub) is required")

        result = await user_query_service.delete_user_query(
            id=id,
            uid=auth0_sub
        )

        # Check for success
        if not result.get('success'):
            if "not found" in result.get('message', ''):
                raise HTTPException(status_code=404, detail=result.get('message'))
            else:
                raise HTTPException(status_code=500, detail=result.get('message'))

        return result

    except HTTPException as he:
        # Re-raise HTTP exceptions
        raise he
    except Exception as e:
        logger.error(f"Error in delete_user_query endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get('/ticker_facts/{ticker}', response_model=TickerFactsResponse)
@async_require_auth
async def get_ticker_facts(
    ticker: str,
    query_id: Optional[int] = Query(None, description="Query ID to get market information from"),
    auth0_sub: str = Header(None)
):
    """
    Get financial facts for a specific ticker.

    Args:
        ticker: The ticker symbol (e.g., 'AAPL', 'MSFT')
        query_id: Optional query ID to get market information from
        auth0_sub: User identifier from request header

    Returns:
        JSON response with company financial facts
    """
    try:
        if not auth0_sub:
            raise HTTPException(status_code=401, detail="User identifier (auth0_sub) is required")

        # Validate ticker format (optional)
        if not ticker or not isinstance(ticker, str) or len(ticker) > 20:
            raise HTTPException(status_code=400, detail="Invalid ticker symbol")

        # Convert ticker to uppercase
        ticker = ticker.upper()

        result = await user_query_service.get_ticker_facts(ticker, query_id)

        # Check for errors
        if not result.get('success'):
            logger.error(f"Error getting ticker facts: {result.get('error')}")
            raise HTTPException(status_code=500, detail=result.get('error'))

        return result

    except HTTPException as he:
        # Re-raise HTTP exceptions
        raise he
    except Exception as e:
        logger.error(f"Error in get_ticker_facts endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get('/user_query', response_model=UserQueriesResponse)
@async_require_auth
async def get_user_queries(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search term to match against user_query (case-insensitive)"),
    auth0_sub: str = Header(None)
):
    """
    Get queries for a specific user with pagination.

    Args:
        page: Page number (starting from 1)
        page_size: Number of items per page
        auth0_sub: User identifier from request header

    Returns:
        JSON response with queries and pagination info
    """
    try:
        if not auth0_sub:
            raise HTTPException(status_code=401, detail="User identifier (auth0_sub) is required")

        result = await user_query_service.get_user_queries(
            uid=auth0_sub,
            page=page,
            page_size=page_size,
            search=search
        )

        # Check for errors
        if result.get('error'):
            logger.error(f"Error getting user queries: {result.get('error')}")
            raise HTTPException(status_code=500, detail=result.get('error'))

        return result

    except HTTPException as he:
        # Re-raise HTTP exceptions
        raise he
    except Exception as e:
        logger.error(f"Error in get_user_queries endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
