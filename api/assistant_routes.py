"""
API routes for the OpenAI Assistant API.
"""
from flask import Blueprint, request, jsonify, g
from services.assistant_service import AssistantService
from utils.logging import logger
from middleware.rate_limiting import rate_limit
from middleware.auth import require_auth

# Create a blueprint for assistant routes
assistant_bp = Blueprint('assistant', __name__)
assistant_service = AssistantService()

@assistant_bp.route('/assistant', methods=['POST'])
@rate_limit
@require_auth
def assistant_query():
    """
    Handle authenticated assistant API queries.
    Requires auth0_sub in request headers.
    
    Request body:
    {
        "query": "User's question or message",
        "thread_id": "Optional thread ID for continuing a conversation",
        "model": "Optional model name (default: gpt-4-turbo-preview)"
    }
    
    Returns:
        JSON response with assistant's answer
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        query = data.get('query')
        if not query:
            return jsonify({"error": "No query provided"}), 400
        
        thread_id = data.get('thread_id')
        model = data.get('model', 'gpt-4-turbo-preview')
        
        # Get auth0_sub from flask.g (set by require_auth middleware)
        auth0_sub = g.auth0_sub
        
        # Process the query with auth0_sub for thread persistence
        result = assistant_service.query(
            user_query=query,
            thread_id=thread_id,
            model=model,
            auth0_sub=auth0_sub
        )
        
        # Check for errors
        if result.get('error'):
            return jsonify(result), 500
        
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error in assistant query: {str(e)}")
        return jsonify({"error": str(e)}), 500

@assistant_bp.route('/pub/assistant', methods=['POST'])
@rate_limit
def public_assistant_query():
    """
    Handle public (unauthenticated) assistant API queries.
    
    Request body:
    {
        "query": "User's question or message",
        "thread_id": "Optional thread ID for continuing a conversation",
        "model": "Optional model name (default: gpt-4-turbo-preview)"
    }
    
    Returns:
        JSON response with assistant's answer
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        query = data.get('query')
        if not query:
            return jsonify({"error": "No query provided"}), 400
        
        thread_id = data.get('thread_id')
        model = data.get('model', 'gpt-4-turbo-preview')
        
        # Process the query without auth0_sub (no thread persistence)
        result = assistant_service.query(
            user_query=query,
            thread_id=thread_id,
            model=model
        )
        
        # Check for errors
        if result.get('error'):
            return jsonify(result), 500
        
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error in assistant query: {str(e)}")
        return jsonify({"error": str(e)}), 500

@assistant_bp.route('/assistant/thread', methods=['POST'])
@rate_limit
@require_auth
def create_thread():
    """
    Create a new conversation thread.
    
    Returns:
        JSON response with thread ID
    """
    try:
        result = assistant_service.create_thread()
        
        if result.get('error'):
            return jsonify(result), 500
        
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error creating thread: {str(e)}")
        return jsonify({"error": str(e)}), 500

@assistant_bp.route('/assistant/thread/<thread_id>', methods=['DELETE'])
@rate_limit
@require_auth
def delete_thread(thread_id):
    """
    Delete a conversation thread.
    
    Args:
        thread_id: The thread ID to delete
        
    Returns:
        JSON response with result
    """
    try:
        result = assistant_service.delete_thread(thread_id)
        
        if result.get('error'):
            return jsonify(result), 500
        
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error deleting thread {thread_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@assistant_bp.route('/assistant/thread/<thread_id>/messages', methods=['GET'])
@rate_limit
@require_auth
def get_thread_messages(thread_id):
    """
    Get all messages in a thread.
    
    Args:
        thread_id: The thread ID
        
    Returns:
        JSON response with messages
    """
    try:
        result = assistant_service.get_thread_messages(thread_id)
        
        if result.get('error'):
            return jsonify(result), 500
        
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error getting messages for thread {thread_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@assistant_bp.route('/assistant/thread/<thread_id>/run/<run_id>/cancel', methods=['POST'])
@rate_limit
@require_auth
def cancel_run(thread_id, run_id):
    """
    Cancel an in-progress run.
    
    Args:
        thread_id: The thread ID
        run_id: The run ID to cancel
        
    Returns:
        JSON response with result
    """
    try:
        result = assistant_service.cancel_run(thread_id, run_id)
        
        if result.get('error'):
            return jsonify(result), 500
        
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error cancelling run {run_id} in thread {thread_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@assistant_bp.route('/assistant/cancel', methods=['POST'])
@rate_limit
@require_auth
def cancel_active_run():
    """
    Cancel the most recent active run for the authenticated user.
    
    Returns:
        JSON response with result
    """
    try:
        # Get auth0_sub from flask.g (set by require_auth middleware)
        auth0_sub = g.auth0_sub
        
        result = assistant_service.cancel_active_run(auth0_sub=auth0_sub)
        
        if result.get('error'):
            return jsonify(result), 500
        
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error cancelling active run: {str(e)}")
        return jsonify({"error": str(e)}), 500
