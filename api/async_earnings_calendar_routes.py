"""
Async API routes for earnings calendar data.
"""
from typing import Optional
from fastapi import APIRouter, HTTPException, Query
from utils.logging import logger
from services.async_earnings_calendar_service import AsyncEarningsCalendarService

# Create router
router = APIRouter(
    tags=["Earnings Calendar"],
    responses={404: {"description": "Not found"}},
)

# Initialize service
earnings_calendar_service = AsyncEarningsCalendarService()

@router.get("/pub/earnings/events")
async def public_get_earnings_calendar(
    days: int = Query(7, description="Number of days to look ahead"),
    company: Optional[str] = Query(None, description="Filter by company name (supports partial matching)"),
    time_filter: Optional[str] = Query(None, description="Filter by time ('upcoming' or 'past')"),
    page: int = Query(1, description="Page number", ge=1),
    page_size: int = Query(10, description="Items per page", ge=1, le=100),
):
    """
    Public endpoint to get earnings calendar events for the next N days and notable events from previous 4 days.

    Args:
        days: Number of days to look ahead (default: 7)
        company: Filter by company name
        time_filter: Filter by time ('upcoming' or 'past')
        page: Page number for pagination
        page_size: Number of items per page

    Returns:
        Earnings calendar events and pagination info
    """
    try:
        logger.info(f"Getting public earnings calendar for {days} days ahead")
        result = await earnings_calendar_service.get_earnings_calendar(
            days=days,
            company=company,
            time_filter=time_filter,
            page=page,
            page_size=page_size,
        )

        if 'error' in result:
            logger.error(f"Error getting public earnings calendar: {result['error']}")
            raise HTTPException(status_code=500, detail=result['error'])

        return result
    except Exception as e:
        logger.error(f"Error in public_get_earnings_calendar endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
