"""
Async API routes for SEC filings.
"""
from fastapi import APIRout<PERSON>, <PERSON><PERSON>, Query, Path, HTTPException
from typing import Optional, List
from pydantic import BaseModel
from services.async_sec_filing_service import AsyncSECFilingService
from utils.logging import logger
from middleware.auth import async_require_auth

# Create a router for FastAPI SEC filing routes
router = APIRouter()
sec_filing_service = AsyncSECFilingService()

@router.get('/pub/sec-filings')
async def get_public_sec_filings(
    ticker: Optional[str] = Query(None, description="Stock ticker symbol"),
    filing_type: Optional[str] = Query(None, description="Type of filing (e.g., '10-K', '10-Q')"),
    fiscal_year: Optional[int] = Query(None, description="Fiscal year of the filing"),
    fiscal_quarter: Optional[int] = Query(None, description="Fiscal quarter of the filing"),
    page: int = Query(1, description="Page number for pagination"),
    page_size: int = Query(20, description="Number of items per page"),
    ticker_search: Optional[str] = Query(None, description="Search string to filter tickers (contains search)")
):
    """
    Public endpoint to get SEC filings based on the provided parameters.

    Special behaviors:
    - If no parameters are provided, returns ticker information in page format
    - If only ticker is provided, returns all unique filing types for that ticker
    - If ticker and filing_type are provided, returns filings based on the type:
      - For '10-K': returns fiscal years
      - For '10-Q': returns fiscal quarter and year combinations
      - For '8-K': returns filing dates

    Args:
        ticker: Stock ticker symbol
        filing_type: Type of filing (e.g., '10-K', '10-Q')
        fiscal_year: Fiscal year of the filing
        fiscal_quarter: Fiscal quarter of the filing
        page: Page number for pagination
        page_size: Number of items per page
        ticker_search: Search string to filter tickers (contains search)
        
    Returns:
        JSON response with SEC filings data
    """
    try:
        result = await sec_filing_service.get_filings(
            ticker=ticker,
            filing_type=filing_type,
            fiscal_year=fiscal_year,
            fiscal_quarter=fiscal_quarter,
            page=page,
            page_size=page_size,
            ticker_search=ticker_search
        )
        
        if 'error' in result:
            raise HTTPException(status_code=500, detail=result['error'])
        
        return result
    except Exception as e:
        logger.error(f"Error in get_public_sec_filings: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get('/pub/sec-filings/{filing_id}')
async def get_public_sec_filing_details(
    filing_id: int = Path(..., description="ID of the filing")
):
    """
    Public endpoint to get detailed information about a specific SEC filing.
    
    Args:
        filing_id: ID of the filing
        
    Returns:
        JSON response with filing details and sections
    """
    try:
        result = await sec_filing_service.get_filing_details(filing_id)
        
        if 'error' in result:
            raise HTTPException(status_code=404 if "not found" in result['error'] else 500, detail=result['error'])
        
        return result
    except Exception as e:
        logger.error(f"Error in get_public_sec_filing_details: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get('/sec-filings')
@async_require_auth
async def get_sec_filings(
    ticker: Optional[str] = Query(None, description="Stock ticker symbol"),
    filing_type: Optional[str] = Query(None, description="Type of filing (e.g., '10-K', '10-Q')"),
    fiscal_year: Optional[int] = Query(None, description="Fiscal year of the filing"),
    fiscal_quarter: Optional[int] = Query(None, description="Fiscal quarter of the filing"),
    page: int = Query(1, description="Page number for pagination"),
    page_size: int = Query(20, description="Number of items per page"),
    ticker_search: Optional[str] = Query(None, description="Search string to filter tickers (contains search)"),
    auth0_sub: str = Header(None)
):
    """
    Get SEC filings based on the provided parameters.
    If no parameters are provided, returns ticker information in page format.
    
    Args:
        ticker: Stock ticker symbol
        filing_type: Type of filing (e.g., '10-K', '10-Q')
        fiscal_year: Fiscal year of the filing
        fiscal_quarter: Fiscal quarter of the filing
        page: Page number for pagination
        page_size: Number of items per page
        ticker_search: Search string to filter tickers (contains search)
        auth0_sub: User identifier from request header
        
    Returns:
        JSON response with SEC filings data
    """
    try:
        result = await sec_filing_service.get_filings(
            ticker=ticker,
            filing_type=filing_type,
            fiscal_year=fiscal_year,
            fiscal_quarter=fiscal_quarter,
            page=page,
            page_size=page_size,
            ticker_search=ticker_search
        )
        
        if 'error' in result:
            raise HTTPException(status_code=500, detail=result['error'])
        
        return result
    except Exception as e:
        logger.error(f"Error in get_sec_filings: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get('/sec-filings/{filing_id}')
@async_require_auth
async def get_sec_filing_details(
    filing_id: int = Path(..., description="ID of the filing"),
    auth0_sub: str = Header(None)
):
    """
    Get detailed information about a specific SEC filing.
    
    Args:
        filing_id: ID of the filing
        auth0_sub: User identifier from request header
        
    Returns:
        JSON response with filing details and sections
    """
    try:
        result = await sec_filing_service.get_filing_details(filing_id)
        
        if 'error' in result:
            raise HTTPException(status_code=404 if "not found" in result['error'] else 500, detail=result['error'])
        
        return result
    except Exception as e:
        logger.error(f"Error in get_sec_filing_details: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get('/get-sec-filing')
@async_require_auth
async def get_sec_filing(
    ticker: Optional[str] = Query(None, description="Stock ticker symbol"),
    filing_type: Optional[str] = Query(None, description="Type of filing (e.g., '10-K', '10-Q', '8-K')"),
    fiscal_year: Optional[int] = Query(None, description="Fiscal year of the filing"),
    fiscal_quarter: Optional[int] = Query(None, description="Fiscal quarter of the filing"),
    page: int = Query(1, description="Page number for pagination"),
    page_size: int = Query(20, description="Number of items per page"),
    ticker_search: Optional[str] = Query(None, description="Search string to filter tickers (contains search)"),
    auth0_sub: str = Header(None)
):
    """
    Get SEC filings based on the provided parameters.
    
    Special behaviors:
    - If no parameters are provided, returns ticker information in page format
    - If filing_type is 8-K, returns only filing dates
    - If ticker and filing_type are provided and filing_type is 8-K, returns all filing dates for that ticker
    - If ticker_search is provided, returns tickers containing the search string
    
    Args:
        ticker: Stock ticker symbol
        filing_type: Type of filing (e.g., '10-K', '10-Q', '8-K')
        fiscal_year: Fiscal year of the filing
        fiscal_quarter: Fiscal quarter of the filing
        page: Page number for pagination
        page_size: Number of items per page
        ticker_search: Search string to filter tickers (contains search)
        auth0_sub: User identifier from request header
        
    Returns:
        JSON response with SEC filings data
    """
    try:
        result = await sec_filing_service.get_filings(
            ticker=ticker,
            filing_type=filing_type,
            fiscal_year=fiscal_year,
            fiscal_quarter=fiscal_quarter,
            page=page,
            page_size=page_size,
            ticker_search=ticker_search
        )
        
        if 'error' in result:
            raise HTTPException(status_code=500, detail=result['error'])
        
        return result
    except Exception as e:
        logger.error(f"Error in get_sec_filing: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
