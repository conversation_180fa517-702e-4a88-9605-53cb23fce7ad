"""
Async API routes for shared query operations.
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException, Query as QueryParam
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Optional

from services.async_shared_query_service import AsyncSharedQueryService
from utils.logging import logger
from middleware.auth import async_require_auth

# Create router
router = APIRouter()
shared_query_service = AsyncSharedQueryService()

# Pydantic models
class CreateShareRequest(BaseModel):
    """Request model for creating a share."""
    query_ids: List[int] = Field(..., min_items=1, description="List of query IDs to share")
    title: Optional[str] = Field(None, max_length=255, description="Optional custom title")
    description: Optional[str] = Field(None, max_length=1000, description="Optional description")
    expires_in_days: Optional[int] = Field(None, ge=1, le=365, description="Expiration in days (1-365)")

class ShareResponse(BaseModel):
    """Response model for share operations."""
    success: bool
    share_id: Optional[str] = None
    share_url: Optional[str] = None
    expires_at: Optional[str] = None
    existing: Optional[bool] = None
    query_count: Optional[int] = None
    error: Optional[str] = None

class PublicQueryResponse(BaseModel):
    """Response model for public query access."""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

# Authenticated routes (require auth)
@router.post('/share/query', response_model=ShareResponse)
@async_require_auth
async def create_share(
    request: CreateShareRequest,
    auth0_sub: str = Header(None)
):
    """
    Create a shareable link for one or more queries.

    Args:
        request: Share configuration including query_ids
        auth0_sub: User identifier from header

    Returns:
        Share information including the public URL
    """
    try:
        if not auth0_sub:
            raise HTTPException(status_code=401, detail="Authentication required")

        # Use the new multi-share service method
        result = await shared_query_service.create_multi_share(
            query_ids=request.query_ids,
            user_id=auth0_sub,
            title=request.title,
            description=request.description,
            expires_in_days=request.expires_in_days
        )

        if not result.get('success'):
            if "not found" in result.get('error', '').lower():
                raise HTTPException(status_code=404, detail=result.get('error'))
            else:
                raise HTTPException(status_code=400, detail=result.get('error'))

        return ShareResponse(
            success=result.get('success'),
            share_id=result.get('share_id'),
            share_url=result.get('share_url'),
            expires_at=result.get('expires_at'),
            existing=result.get('existing'),
            query_count=result.get('query_count')
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in create_share endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get('/share/my-shares')
@async_require_auth
async def get_my_shares(
    page: int = QueryParam(1, ge=1, description="Page number"),
    page_size: int = QueryParam(10, ge=1, le=50, description="Items per page"),
    auth0_sub: str = Header(None)
):
    """Get all shares created by the authenticated user."""
    try:
        if not auth0_sub:
            raise HTTPException(status_code=401, detail="Authentication required")
        
        result = await shared_query_service.get_user_shares(
            user_id=auth0_sub,
            page=page,
            page_size=page_size
        )
        
        if not result.get('success'):
            raise HTTPException(status_code=500, detail=result.get('error'))
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_my_shares endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete('/share/{share_id}')
@async_require_auth
async def revoke_share(
    share_id: str,
    auth0_sub: str = Header(None)
):
    """Revoke a shared query."""
    try:
        if not auth0_sub:
            raise HTTPException(status_code=401, detail="Authentication required")
        
        result = await shared_query_service.revoke_share(
            share_id=share_id,
            user_id=auth0_sub
        )
        
        if not result.get('success'):
            if "not found" in result.get('error', '').lower():
                raise HTTPException(status_code=404, detail=result.get('error'))
            else:
                raise HTTPException(status_code=400, detail=result.get('error'))
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in revoke_share endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Public routes (no authentication required)
@router.get('/pub/shared/{share_id}', response_model=PublicQueryResponse)
async def get_shared_query(share_id: str):
    """
    Get a shared query by share ID - PUBLIC ACCESS.
    
    Args:
        share_id: The UUID share identifier
        
    Returns:
        Query data for public consumption
    """
    try:
        result = await shared_query_service.get_shared_query(share_id)
        
        if not result.get('success'):
            if "not found" in result.get('error', '').lower():
                raise HTTPException(status_code=404, detail="Shared query not found")
            elif "no longer accessible" in result.get('error', '').lower():
                raise HTTPException(status_code=410, detail="Shared query has expired or been revoked")
            else:
                raise HTTPException(status_code=400, detail=result.get('error'))
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_shared_query endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
