"""
Async API routes for financial calendar data.
"""
from typing import Optional
from fastapi import APIRouter, HTTPException, Query, Depends, Header
from utils.logging import logger
from services.async_financial_calendar_service import AsyncFinancialCalendarService
from middleware.auth import async_require_auth

# Create router
router = APIRouter(
    tags=["Financial Calendar"],
    responses={404: {"description": "Not found"}},
)

# Initialize service
financial_calendar_service = AsyncFinancialCalendarService()

@router.get("/events")
@async_require_auth
async def get_economic_calendar(
    days: int = Query(7, description="Number of days to look ahead"),
    country: Optional[str] = Query(None, description="Filter by country or region ('United States', 'Europe', 'Asia')"),
    event_type: Optional[str] = Query(None, description="Filter by event type"),
    impact: Optional[str] = Query(None, description="Filter by impact level (High, Medium, Low)"),
    time_filter: Optional[str] = Query(None, description="Filter by time ('upcoming' or 'past')"),
    page: int = Query(1, description="Page number", ge=1),
    page_size: int = Query(30, description="Items per page", ge=1, le=100),
    event_count: int = Query(7, description="Number of event count"),
    auth0_sub: str = Header(None)
):
    """
    Get economic calendar events for the next N days and high impact events from previous 4 days.
    
    Args:
        days: Number of days to look ahead (default: 7)
        country: Filter by country or region ('United States', 'Europe', 'Asia')
        event_type: Filter by event type
        impact: Filter by impact level (High, Medium, Low)
        time_filter: Filter by time ('upcoming' or 'past')
        page: Page number for pagination
        page_size: Number of items per page
        auth0_sub: User identifier from request header
        
    Returns:
        Economic calendar events and pagination info
    """
    try:
        logger.info(f"Getting economic calendar for {days} days ahead")
        result = await financial_calendar_service.get_economic_calendar(
            days=days,
            country=country,
            event_type=event_type,
            impact=impact,
            time_filter=time_filter,
            page=page,
            page_size=page_size,
            event_count=event_count,
        )
        
        if 'error' in result:
            logger.error(f"Error getting economic calendar: {result['error']}")
            raise HTTPException(status_code=500, detail=result['error'])
        
        return result
    except Exception as e:
        logger.error(f"Error in get_economic_calendar endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/events/{event_id}")
@async_require_auth
async def get_event_details(
    event_id: int,
    auth0_sub: str = Header(None)
):
    """
    Get detailed information about a specific economic calendar event.
    
    Args:
        event_id: ID of the event
        auth0_sub: User identifier from request header
        
    Returns:
        Event details
    """
    try:
        logger.info(f"Getting details for event {event_id}")
        result = await financial_calendar_service.get_event_details(event_id)
        
        if 'error' in result:
            if "not found" in result['error'].lower():
                raise HTTPException(status_code=404, detail=result['error'])
            else:
                logger.error(f"Error getting event details: {result['error']}")
                raise HTTPException(status_code=500, detail=result['error'])
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_event_details endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/pub/events")
async def public_get_economic_calendar(
    days: int = Query(7, description="Number of days to look ahead"),
    country: Optional[str] = Query(None, description="Filter by country or region ('United States', 'Europe', 'Asia')"),
    event_type: Optional[str] = Query(None, description="Filter by event type"),
    impact: Optional[str] = Query(None, description="Filter by impact level (High, Medium, Low)"),
    time_filter: Optional[str] = Query(None, description="Filter by time ('upcoming' or 'past')"),
    page: int = Query(1, description="Page number", ge=1),
    page_size: int = Query(30, description="Items per page", ge=1, le=100),
    event_count: int = Query(7, description="Number of event count"),
):
    """
    Public endpoint to get economic calendar events for the next N days and high impact events from previous 4 days.
    
    Args:
        days: Number of days to look ahead (default: 7)
        country: Filter by country or region ('United States', 'Europe', 'Asia')
        event_type: Filter by event type
        impact: Filter by impact level (High, Medium, Low)
        time_filter: Filter by time ('upcoming' or 'past')
        page: Page number for pagination
        page_size: Number of items per page
        
    Returns:
        Economic calendar events and pagination info
    """
    try:
        logger.info(f"Getting public economic calendar for {days} days ahead")
        result = await financial_calendar_service.get_economic_calendar(
            days=days,
            country=country,
            event_type=event_type,
            impact=impact,
            time_filter=time_filter,
            page=page,
            page_size=page_size,
            event_count=event_count,
        )
        
        if 'error' in result:
            logger.error(f"Error getting public economic calendar: {result['error']}")
            raise HTTPException(status_code=500, detail=result['error'])
        
        return result
    except Exception as e:
        logger.error(f"Error in public_get_economic_calendar endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/pub/events/{event_id}")
async def public_get_event_details(
    event_id: int
):
    """
    Public endpoint to get detailed information about a specific economic calendar event.
    
    Args:
        event_id: ID of the event
        
    Returns:
        Event details
    """
    try:
        logger.info(f"Getting public details for event {event_id}")
        result = await financial_calendar_service.get_event_details(event_id)
        
        if 'error' in result:
            if "not found" in result['error'].lower():
                raise HTTPException(status_code=404, detail=result['error'])
            else:
                logger.error(f"Error getting public event details: {result['error']}")
                raise HTTPException(status_code=500, detail=result['error'])
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in public_get_event_details endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
