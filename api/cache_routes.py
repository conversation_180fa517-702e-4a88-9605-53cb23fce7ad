from flask import Blueprint, jsonify, request
from utils.cache import (
    invalidate_cache, 
    get_cache_stats, 
    warm_cache, 
    enforce_cache_size_limits,
    periodic_cache_maintenance
)

cache_bp = Blueprint('cache', __name__, url_prefix='/cache')

@cache_bp.route('/stats', methods=['GET'])
def get_stats():
    """
    Get cache statistics.
    
    Returns:
        dict: Cache statistics
    """
    stats = get_cache_stats()
    return jsonify(stats)

@cache_bp.route('/invalidate', methods=['POST'])
def invalidate():
    """
    Invalidate cached responses.
    
    Request body:
        provider (optional): Provider name to invalidate only responses from that provider
        model (optional): Model name to invalidate only responses from that model
        
    Returns:
        dict: Number of keys deleted
    """
    data = request.json or {}
    provider = data.get('provider')
    model = data.get('model')
    
    count = invalidate_cache(provider=provider, model=model)
    
    return jsonify({
        'deleted': count,
        'provider': provider,
        'model': model
    })

@cache_bp.route('/warm', methods=['POST'])
def warm():
    """
    Warm the cache by preloading frequently accessed items into memory.
    
    Returns:
        dict: Status information
    """
    warm_cache()
    
    return jsonify({
        'status': 'success',
        'message': 'Cache warming initiated'
    })

@cache_bp.route('/enforce-limits', methods=['POST'])
def enforce_limits():
    """
    Enforce cache size limits by removing least recently used items.
    
    Returns:
        dict: Status information
    """
    enforce_cache_size_limits()
    
    return jsonify({
        'status': 'success',
        'message': 'Cache size limits enforced'
    })

@cache_bp.route('/maintenance', methods=['POST'])
def maintenance():
    """
    Perform periodic cache maintenance tasks.
    
    Returns:
        dict: Status information
    """
    periodic_cache_maintenance()
    
    return jsonify({
        'status': 'success',
        'message': 'Cache maintenance completed'
    })

@cache_bp.route('/config', methods=['GET'])
def get_config():
    """
    Get cache configuration.
    
    Returns:
        dict: Cache configuration
    """
    from config.settings import settings
    
    config = {
        'enable_cache': settings.ENABLE_CACHE,
        'cache_ttl': settings.CACHE_TTL,
        'cache_compression': settings.CACHE_COMPRESSION,
        'cache_memory_enabled': settings.CACHE_MEMORY_ENABLED,
        'cache_memory_max_items': settings.CACHE_MEMORY_MAX_ITEMS,
        'cache_memory_ttl': settings.CACHE_MEMORY_TTL,
        'cache_persistent_enabled': settings.CACHE_PERSISTENT_ENABLED,
        'cache_persistent_path': settings.CACHE_PERSISTENT_PATH,
        'cache_lru_policy': settings.CACHE_LRU_POLICY,
        'cache_max_size_mb': settings.CACHE_MAX_SIZE_MB,
        'cache_similarity_threshold': settings.CACHE_SIMILARITY_THRESHOLD
    }
    
    return jsonify(config)
