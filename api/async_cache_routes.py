from fastapi import APIRouter, Body
from typing import Dict, Any, Optional
from utils.async_cache import (
    async_invalidate_cache, 
    async_get_cache_stats, 
    warm_cache, 
    enforce_cache_size_limits,
    periodic_cache_maintenance
)

router = APIRouter(prefix="/cache", tags=["cache"])

@router.get("/stats")
async def get_stats() -> Dict[str, Any]:
    """
    Get cache statistics.
    
    Returns:
        dict: Cache statistics
    """
    stats = await async_get_cache_stats()
    return stats

@router.post("/invalidate")
async def invalidate(
    provider: Optional[str] = Body(None, description="Provider name to invalidate only responses from that provider"),
    model: Optional[str] = Body(None, description="Model name to invalidate only responses from that model")
) -> Dict[str, Any]:
    """
    Invalidate cached responses.
    
    Args:
        provider: Provider name to invalidate only responses from that provider
        model: Model name to invalidate only responses from that model
        
    Returns:
        dict: Number of keys deleted
    """
    count = await async_invalidate_cache(provider=provider, model=model)
    
    return {
        'deleted': count,
        'provider': provider,
        'model': model
    }

@router.post("/warm")
async def warm() -> Dict[str, Any]:
    """
    Warm the cache by preloading frequently accessed items into memory.
    
    Returns:
        dict: Status information
    """
    await warm_cache()
    
    return {
        'status': 'success',
        'message': 'Cache warming initiated'
    }

@router.post("/enforce-limits")
async def enforce_limits() -> Dict[str, Any]:
    """
    Enforce cache size limits by removing least recently used items.
    
    Returns:
        dict: Status information
    """
    await enforce_cache_size_limits()
    
    return {
        'status': 'success',
        'message': 'Cache size limits enforced'
    }

@router.post("/maintenance")
async def maintenance() -> Dict[str, Any]:
    """
    Perform periodic cache maintenance tasks.
    
    Returns:
        dict: Status information
    """
    await periodic_cache_maintenance()
    
    return {
        'status': 'success',
        'message': 'Cache maintenance completed'
    }

@router.get("/config")
async def get_config() -> Dict[str, Any]:
    """
    Get cache configuration.
    
    Returns:
        dict: Cache configuration
    """
    from config.settings import settings
    
    config = {
        'enable_cache': settings.ENABLE_CACHE,
        'cache_ttl': settings.CACHE_TTL,
        'cache_compression': settings.CACHE_COMPRESSION,
        'cache_memory_enabled': settings.CACHE_MEMORY_ENABLED,
        'cache_memory_max_items': settings.CACHE_MEMORY_MAX_ITEMS,
        'cache_memory_ttl': settings.CACHE_MEMORY_TTL,
        'cache_persistent_enabled': settings.CACHE_PERSISTENT_ENABLED,
        'cache_persistent_path': settings.CACHE_PERSISTENT_PATH,
        'cache_lru_policy': settings.CACHE_LRU_POLICY,
        'cache_max_size_mb': settings.CACHE_MAX_SIZE_MB,
        'cache_similarity_threshold': settings.CACHE_SIMILARITY_THRESHOLD
    }
    
    return config
