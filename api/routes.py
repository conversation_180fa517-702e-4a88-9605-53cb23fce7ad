from flask import Blueprint, request, g
from services.conversation_service import ConversationService
from utils.logging import logger
from api.cache_routes import cache_bp
from api.assistant_routes import assistant_bp
from middleware.rate_limiting import rate_limit
from middleware.auth import require_auth

# Create API blueprint
api = Blueprint('api', __name__)

# Register cache routes
api.register_blueprint(cache_bp)

# Register assistant routes
api.register_blueprint(assistant_bp)

# Create conversation service
conversation_service = ConversationService()

@api.route('/')
def health_check():
    """
    Health check endpoint.
    
    Returns:
        dict: Status information
    """
    return {'status': 'ok'}

@api.route('/models', methods=['GET'])
def list_models():
    """
    List available models with their capabilities and metrics.
    
    Returns:
        dict: Information about available models
    """
    return conversation_service.list_models()

@api.route('/query', methods=['POST'])
@rate_limit
@require_auth
def handle_query():
    """
    Handle an authenticated query from a user.
    Requires auth0_sub in request headers.
    
    Returns:
        Response: The response to send back to the client
    """
    try:
        data = request.json
        
        # Get auth0_sub from flask.g (set by require_auth middleware)
        auth0_sub = g.auth0_sub
        
        # Pass auth0_sub to the conversation service
        return conversation_service.handle_query(data, auth0_sub=auth0_sub)
    except Exception as e:
        logger.error(f"API error: {str(e)}")
        return {'error': f'API error: {str(e)}'}, 500

@api.route('/pub/query', methods=['POST'])
@rate_limit
def handle_public_query():
    """
    Handle a public (unauthenticated) query from a user.
    
    Returns:
        Response: The response to send back to the client
    """
    try:
        data = request.json
        return conversation_service.handle_query(data)
    except Exception as e:
        logger.error(f"API error: {str(e)}")
        return {'error': f'API error: {str(e)}'}, 500

@api.route('/conversation/new-round', methods=['POST'])
def start_new_round():
    """
    Start a new conversation round.
    
    Returns:
        dict: Information about the new round
    """
    try:
        data = request.json
        return conversation_service.start_new_round(data)
    except Exception as e:
        logger.error(f"API error: {str(e)}")
        return {'error': f'API error: {str(e)}'}, 500

@api.route('/conversation/stats/<session_id>', methods=['GET'])
def get_conversation_stats(session_id):
    """
    Get statistics about a conversation.
    
    Returns:
        dict: Conversation statistics
    """
    try:
        return conversation_service.get_conversation_stats(session_id)
    except Exception as e:
        logger.error(f"API error: {str(e)}")
        return {'error': f'API error: {str(e)}'}, 500

@api.route('/conversation/export', methods=['POST'])
def export_conversation():
    """
    Export a conversation.
    
    Returns:
        dict: The exported conversation
    """
    try:
        data = request.json
        return conversation_service.export_conversation(data)
    except Exception as e:
        logger.error(f"API error: {str(e)}")
        return {'error': f'API error: {str(e)}'}, 500

@api.route('/conversation/branch', methods=['POST'])
def create_branch():
    """
    Create a new conversation branch.
    
    Returns:
        dict: Information about the new branch
    """
    try:
        data = request.json
        return conversation_service.create_branch(data)
    except Exception as e:
        logger.error(f"API error: {str(e)}")
        return {'error': f'API error: {str(e)}'}, 500

@api.route('/conversation/clear', methods=['POST'])
def clear_history():
    """
    Clear conversation history.
    
    Returns:
        dict: Status information
    """
    try:
        data = request.json
        return conversation_service.clear_history(data)
    except Exception as e:
        logger.error(f"API error: {str(e)}")
        return {'error': f'API error: {str(e)}'}, 500
