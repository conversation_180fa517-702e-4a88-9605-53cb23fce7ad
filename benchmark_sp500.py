import asyncio
import time
from utils.function_registry import function_registry
import utils.sample_functions  # This will register the functions

async def benchmark():
    print("Benchmarking get_tickers_dividend function...")
    
    # Verify the function is registered
    if not function_registry.has_function("get_tickers_dividend"):
        print("Function get_tickers_dividend not found in registry")
        return
    
    # First run - this will likely hit the API and cache the results
    print("\nFirst run (no cache):")
    start_time = time.time()
    result = await function_registry.execute_function(
        "get_tickers_dividend", 
        limit=10,
        min_dividend_yield=1.0,
        sort_by="yield"
    )
    end_time = time.time()
    
    # Print execution time and basic stats
    print(f"Execution time: {end_time - start_time:.2f} seconds")
    print(f"Results: {len(result.get('stocks', []))} stocks found")
    print(f"Cache version: {result.get('metadata', {}).get('cache_version', 'N/A')}")
    
    # Wait a moment
    print("\nWaiting 2 seconds before second run...")
    await asyncio.sleep(2)
    
    # Second run - this should use the cache
    print("\nSecond run (should use cache):")
    start_time = time.time()
    result = await function_registry.execute_function(
        "get_tickers_dividend", 
        limit=10,
        min_dividend_yield=1.0,
        sort_by="yield"
    )
    end_time = time.time()
    
    # Print execution time and basic stats
    print(f"Execution time: {end_time - start_time:.2f} seconds")
    print(f"Results: {len(result.get('stocks', []))} stocks found")
    print(f"Cache version: {result.get('metadata', {}).get('cache_version', 'N/A')}")
    
    # Third run - test sector-specific caching
    print("\nThird run (sector-specific query):")
    start_time = time.time()
    result = await function_registry.execute_function(
        "get_tickers_dividend", 
        limit=5,
        sector="Technology",
        sort_by="yield"
    )
    end_time = time.time()
    
    # Print execution time and basic stats
    print(f"Execution time: {end_time - start_time:.2f} seconds")
    print(f"Results: {len(result.get('stocks', []))} stocks found")
    print(f"Cache version: {result.get('metadata', {}).get('cache_version', 'N/A')}")
    
    # Print top 5 results from the last query
    print("\nTop 5 Technology stocks by dividend yield:")
    for i, stock in enumerate(result.get('stocks', [])[:5]):
        print(f"{i+1}. {stock['ticker']}: {stock['name']} - Yield: {stock['dividend_yield']}%, Rate: ${stock['dividend_rate']}/share")

if __name__ == "__main__":
    asyncio.run(benchmark())
