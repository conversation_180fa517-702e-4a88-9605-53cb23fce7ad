import logging
from typing import List, Optional
from utils.function_registry import register_function, function_registry

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d:%(funcName)s] - %(message)s')

# Test function with list[str] parameter
@register_function(
    description="Test function with list[str] parameter",
    parameter_descriptions={
        "engines": "List of search engines to use (e.g., ['google', 'duckduckgo'])"
    }
)
def test_list_param(engines: List[str]):
    """Test function with list[str] parameter."""
    return f"Using search engines: {', '.join(engines)}"

# Test function with Optional[List[str]] parameter
@register_function(
    description="Test function with Optional[List[str]] parameter",
    parameter_descriptions={
        "engines": "List of search engines to use (e.g., ['google', 'duckduckgo'])"
    }
)
def test_optional_list_param(engines: Optional[List[str]] = None):
    """Test function with Optional[List[str]] parameter."""
    if engines:
        return f"Using search engines: {', '.join(engines)}"
    return "No search engines specified"

# Get the schemas for the test functions
list_schema = function_registry.get_function_schema("test_list_param")
optional_list_schema = function_registry.get_function_schema("test_optional_list_param")

# Print the schemas
print("\n=== Function Schema: List[str] ===")
print(f"Function name: {list_schema['name']}")
print(f"Description: {list_schema['description']}")
print("\nParameters:")
for param_name, param_schema in list_schema['parameters']['properties'].items():
    print(f"  - {param_name}: {param_schema['type']} ({param_schema['description']})")

print("\n=== Function Schema: Optional[List[str]] ===")
print(f"Function name: {optional_list_schema['name']}")
print(f"Description: {optional_list_schema['description']}")
print("\nParameters:")
for param_name, param_schema in optional_list_schema['parameters']['properties'].items():
    print(f"  - {param_name}: {param_schema['type']} ({param_schema['description']})")

# Verify the type mappings
list_param = list_schema['parameters']['properties']['engines']
optional_list_param = optional_list_schema['parameters']['properties']['engines']

print(f"\nVerification for List[str]:")
print(f"  - Type: '{list_param['type']}'")
assert list_param['type'] == "array", f"Expected 'array', got '{list_param['type']}'"
print("  ✅ Type mapping is correct! List[str] is correctly mapped to 'array'")

print(f"  - Items: {list_param.get('items', 'Missing!')}")
assert 'items' in list_param, "Missing 'items' property in array schema"
assert list_param['items']['type'] == "string", f"Expected items type 'string', got '{list_param['items']['type']}'"
print("  ✅ Items property is present with correct type 'string'")

print(f"\nVerification for Optional[List[str]]:")
print(f"  - Type: '{optional_list_param['type']}'")
assert optional_list_param['type'] == "array", f"Expected 'array', got '{optional_list_param['type']}'"
print("  ✅ Type mapping is correct! Optional[List[str]] is correctly mapped to 'array'")

print(f"  - Items: {optional_list_param.get('items', 'Missing!')}")
assert 'items' in optional_list_param, "Missing 'items' property in array schema"
assert optional_list_param['items']['type'] == "string", f"Expected items type 'string', got '{optional_list_param['items']['type']}'"
print("  ✅ Items property is present with correct type 'string'")
