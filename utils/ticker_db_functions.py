from asyncio.log import logger
from typing import Optional

from sqlalchemy import text

from investpy_client import get_cleaned_ticker
from utils.multi_schema_db_manager import db_manager

DEFAULT_SCHEMA = "stock_data"

async def get_current_ticker(prev_ticker, market: Optional[str] = None):
    try:
        prev_ticker = get_cleaned_ticker(prev_ticker)

        current_ticker = prev_ticker
        async with await db_manager.get_db_session(DEFAULT_SCHEMA) as session:
            from utils.stocks_db_functions import get_ticker_table_name_for_market

            table_name = get_ticker_table_name_for_market(market)

            # for US
            if table_name == 'all_tickers':
                ticker_field = 'prev_ticker'
                query_sql = f"""
                SELECT ticker
                FROM {table_name}
                WHERE {ticker_field} = :prev_ticker
                LIMIT 1
                """
                sql_one_liner = ' '.join(query_sql.strip().split())
                logger.info(f"Executing SQL: {sql_one_liner}")

                query = f"""
                SELECT ticker
                FROM {table_name}
                WHERE {ticker_field} = :prev_ticker
                LIMIT 1
                """

                params = {
                    "prev_ticker": prev_ticker,
                }

                result = await session.execute(text(query), params)
                record = result.fetchone()
                if record:
                    current_ticker = record[0]

        current_ticker = get_cleaned_ticker(current_ticker)
        return current_ticker
    except Exception as e:
        logger.warning(f"Error in get_current_ticker: {str(e)}")
        return prev_ticker

async def get_ticker_by_name(name):
    async with await db_manager.get_db_session(DEFAULT_SCHEMA) as session:
        query_sql = f"""
        SELECT ticker
        FROM all_tickers
        WHERE name = :name
        LIMIT 1
        """
        sql_one_liner = ' '.join(query_sql.strip().split())
        logger.info(f"Executing SQL: {sql_one_liner}")

        query = """
        SELECT ticker
        FROM all_tickers
        WHERE name = :name
        LIMIT 1
        """

        params = {
            "name": name,
        }

        result = await session.execute(text(query), params)
        record = result.fetchone()
        if not record:
            return ''
        return record[0]

async def get_company_name_by_ticker(ticker, market: Optional[str] = None):
    """
    Get company name from all_tickers table by ticker symbol and market.

    Args:
        ticker: The ticker symbol (e.g., 'AAPL', 'MSFT')
        market: Optional market identifier (e.g., 'United States', 'Japan', 'South Korea', 'Taiwan', 'Hong Kong')

    Returns:
        str: Company name if found, empty string if not found
    """
    async with await db_manager.get_db_session(DEFAULT_SCHEMA) as session:
        from utils.stocks_db_functions import get_ticker_table_name_for_market

        table_name = get_ticker_table_name_for_market(market)

        if table_name == 'all_tickers':
            ticker_field = 'ticker'
        else:
            ticker_field = 'symbol'

        query_sql = f"""
        SELECT name
        FROM {table_name}
        WHERE {ticker_field} = :ticker
        LIMIT 1
        """
        sql_one_liner = ' '.join(query_sql.strip().split())
        logger.info(f"Executing SQL: {sql_one_liner}")

        query = f"""
        SELECT name
        FROM {table_name}
        WHERE {ticker_field} = :ticker
        LIMIT 1
        """

        params = {
            "ticker": ticker,
        }

        result = await session.execute(text(query), params)
        record = result.fetchone()
        if record:
            return record[0]

        return f"{ticker} ({market})"

async def get_yf_symbol_by_ticker(ticker, market: Optional[str] = None):
    async with await db_manager.get_db_session(DEFAULT_SCHEMA) as session:
        from utils.stocks_db_functions import get_ticker_table_name_for_market

        table_name = get_ticker_table_name_for_market(market)

        if table_name == 'all_tickers':
            ticker_field = 'ticker'
        else:
            ticker_field = 'symbol'

        query_sql = f"""
        SELECT yf_symbol
        FROM {table_name}
        WHERE {ticker_field} = :ticker
        LIMIT 1
        """
        sql_one_liner = ' '.join(query_sql.strip().split())
        logger.info(f"Executing SQL: {sql_one_liner}")

        query = f"""
        SELECT yf_symbol
        FROM {table_name}
        WHERE {ticker_field} = :ticker
        LIMIT 1
        """

        params = {
            "ticker": ticker,
        }

        result = await session.execute(text(query), params)
        record = result.fetchone()
        if record:
            return record[0]

        return ticker