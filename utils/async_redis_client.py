import os
import json
import redis.asyncio as redis
from typing import Optional, List, Any, Dict, Union
from utils.logging import logger
from config.settings import settings

# Initialize Redis client
async_redis_client = None

async def init_async_redis_client():
    """
    Initialize the async Redis client.
    
    Returns:
        redis.Redis: The initialized Redis client
    """
    global async_redis_client
    if async_redis_client is None:
        redis_host = settings.REDIS_HOST
        redis_port = settings.REDIS_PORT
        redis_url = f"redis://{redis_host}:{redis_port}"
        
        try:
            async_redis_client = redis.from_url(
                redis_url,
                encoding="utf-8",
                decode_responses=True,
                db=settings.REDIS_DB,
                max_connections=settings.REDIS_MAX_CONNECTIONS
            )
            logger.info(f"Async Redis client initialized at {redis_url}")
        except Exception as e:
            logger.error(f"Failed to initialize async Redis client: {str(e)}")
            raise
    
    return async_redis_client

async def get_async_redis_client():
    """
    Get the async Redis client, initializing it if necessary.
    
    Returns:
        redis.Redis: The Redis client
    """
    if async_redis_client is None:
        await init_async_redis_client()
    return async_redis_client

class AsyncRedisClient:
    """
    Async Redis client wrapper with convenience methods.
    """
    
    @staticmethod
    async def get(key: str) -> Optional[str]:
        """
        Get a value from Redis.
        
        Args:
            key: The key to get
            
        Returns:
            The value, or None if the key doesn't exist
        """
        client = await get_async_redis_client()
        return await client.get(key)
    
    @staticmethod
    async def set(key: str, value: str) -> bool:
        """
        Set a value in Redis.
        
        Args:
            key: The key to set
            value: The value to set
            
        Returns:
            True if successful
        """
        client = await get_async_redis_client()
        return await client.set(key, value)
    
    @staticmethod
    async def setex(key: str, ttl: int, value: str) -> bool:
        """
        Set a value in Redis with an expiration time.
        
        Args:
            key: The key to set
            ttl: Time to live in seconds
            value: The value to set
            
        Returns:
            True if successful
        """
        client = await get_async_redis_client()
        return await client.setex(key, ttl, value)
    
    @staticmethod
    async def delete(key: str) -> int:
        """
        Delete a key from Redis.
        
        Args:
            key: The key to delete
            
        Returns:
            Number of keys deleted
        """
        client = await get_async_redis_client()
        return await client.delete(key)
    
    @staticmethod
    async def exists(key: str) -> bool:
        """
        Check if a key exists in Redis.
        
        Args:
            key: The key to check
            
        Returns:
            True if the key exists
        """
        client = await get_async_redis_client()
        return await client.exists(key) > 0
    
    @staticmethod
    async def lpush(key: str, value: str) -> int:
        """
        Push a value to the left of a list.
        
        Args:
            key: The list key
            value: The value to push
            
        Returns:
            The length of the list after the push
        """
        client = await get_async_redis_client()
        return await client.lpush(key, value)
    
    @staticmethod
    async def rpush(key: str, value: str) -> int:
        """
        Push a value to the right of a list.
        
        Args:
            key: The list key
            value: The value to push
            
        Returns:
            The length of the list after the push
        """
        client = await get_async_redis_client()
        return await client.rpush(key, value)
    
    @staticmethod
    async def lrange(key: str, start: int, end: int) -> List[str]:
        """
        Get a range of values from a list.
        
        Args:
            key: The list key
            start: The start index
            end: The end index
            
        Returns:
            The list of values
        """
        client = await get_async_redis_client()
        return await client.lrange(key, start, end)
    
    @staticmethod
    async def hset(key: str, field: str, value: str) -> int:
        """
        Set a field in a hash.
        
        Args:
            key: The hash key
            field: The field to set
            value: The value to set
            
        Returns:
            1 if field is new, 0 if field was updated
        """
        client = await get_async_redis_client()
        return await client.hset(key, field, value)
    
    @staticmethod
    async def hget(key: str, field: str) -> Optional[str]:
        """
        Get a field from a hash.
        
        Args:
            key: The hash key
            field: The field to get
            
        Returns:
            The value, or None if the field doesn't exist
        """
        client = await get_async_redis_client()
        return await client.hget(key, field)
    
    @staticmethod
    async def hgetall(key: str) -> Dict[str, str]:
        """
        Get all fields and values from a hash.
        
        Args:
            key: The hash key
            
        Returns:
            A dictionary of field-value pairs
        """
        client = await get_async_redis_client()
        return await client.hgetall(key)
    
    @staticmethod
    async def incr(key: str) -> int:
        """
        Increment a key.
        
        Args:
            key: The key to increment
            
        Returns:
            The new value
        """
        client = await get_async_redis_client()
        return await client.incr(key)
    
    @staticmethod
    async def expire(key: str, ttl: int) -> bool:
        """
        Set an expiration time on a key.
        
        Args:
            key: The key to expire
            ttl: Time to live in seconds
            
        Returns:
            True if successful
        """
        client = await get_async_redis_client()
        return await client.expire(key, ttl)

# Create a singleton instance
async_redis = AsyncRedisClient()
