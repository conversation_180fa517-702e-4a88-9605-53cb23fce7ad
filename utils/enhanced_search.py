import json
import re
import time
import os
from typing import List, Dict, Any, Optional, Tuple
import requests
from bs4 import BeautifulSoup
import html2text
from googlesearch import search as google_search
from urllib.parse import urlparse
from utils.logging import logger
from utils.redis_client import redis_client
from config.settings import settings
from utils.content_extractor import extract_main_content, extract_article_metadata, is_paywall_content
from openai import AsyncOpenAI

# Initialize HTML to text converter
html_converter = html2text.HTML2Text()
html_converter.ignore_links = False
html_converter.ignore_images = True
html_converter.ignore_tables = False
html_converter.ignore_emphasis = True
html_converter.body_width = 0  # No wrapping

# Search provider registry
search_providers = {}

class SearchResult:
    """
    Represents a search result with metadata.
    """
    def __init__(self, title: str, url: str, snippet: str, source: str, 
                 credibility_score: float = 0.0, content: Optional[str] = None):
        self.title = title
        self.url = url
        self.snippet = snippet
        self.source = source  # Which search provider found this result
        self.credibility_score = credibility_score
        self.content = content  # Full content, if fetched
        self.timestamp = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'title': self.title,
            'url': self.url,
            'snippet': self.snippet,
            'source': self.source,
            'credibility_score': self.credibility_score,
            'content': self.content,
            'timestamp': self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchResult':
        """Create from dictionary representation."""
        result = cls(
            title=data['title'],
            url=data['url'],
            snippet=data['snippet'],
            source=data['source'],
            credibility_score=data.get('credibility_score', 0.0),
            content=data.get('content')
        )
        result.timestamp = data.get('timestamp', time.time())
        return result

def register_search_provider(name):
    """
    Decorator to register a search provider function.
    
    Args:
        name: The name of the search provider
    """
    def decorator(func):
        search_providers[name] = func
        return func
    return decorator

@register_search_provider('google')
def search_google(query: str, num_results: int = 5) -> List[SearchResult]:
    """
    Search using Google.
    
    Args:
        query: The search query
        num_results: Number of results to return
        
    Returns:
        List of search results
    """
    try:
        # Use SerpAPI if configured
        if settings.USE_SERP_API and settings.SERP_API_KEY:
            logger.info(f"Using SerpAPI for Google search: {query}, {num_results}")
            
            # Initial parameters with improved defaults
            params = {
                "engine": "google",
                "q": query,
                "api_key": settings.SERP_API_KEY,
                "num": 20, #num_results,
                "gl": "us",  # Set country to US
                "hl": "en",  # Set language to English
                "device": "desktop"  # Default to desktop
            }
            
            # First attempt with default parameters
            response = requests.get("https://serpapi.com/search", params=params)
            response.raise_for_status()
            data = response.json()
            
            # Log which result types are present
            result_types = [key for key in data.keys() if key.endswith('_results') or key == 'answer_box' or key == 'knowledge_graph']
            logger.info(f"SerpAPI result types: {result_types}")
            
            results = []
            
            # Try to extract results from organic_results
            if "organic_results" in data:
                logger.info(f"Found {len(data['organic_results'])} organic results")
                for result in data["organic_results"]:
                    title = result.get("title", "")
                    url = result.get("link", "")
                    snippet = result.get("snippet", "")

                    # Skip URLs defined in ignored domain
                    if contain_ignore_domains(url) :
                        logger.warning(f"Skipping Ignored URL: {url}")
                        continue
                    
                    results.append(SearchResult(
                        title=title,
                        url=url,
                        snippet=snippet,
                        source='google_serp',
                        credibility_score=calculate_credibility_score(url, title, snippet)
                    ))

                    if len(results) >= 10:
                        break
            
            # If no organic results, try mobile device parameter
            if not results and "organic_results" not in data:
                logger.info("No organic results found, trying with mobile device parameter")
                params["device"] = "mobile"
                
                mobile_response = requests.get("https://serpapi.com/search", params=params)
                mobile_response.raise_for_status()
                mobile_data = mobile_response.json()
                
                if "organic_results" in mobile_data:
                    logger.info(f"Found {len(mobile_data['organic_results'])} organic results with mobile parameter")
                    for result in mobile_data["organic_results"]:
                        title = result.get("title", "")
                        url = result.get("link", "")
                        snippet = result.get("snippet", "")

                        # Skip ignored URLs
                        if contain_ignore_domains(url):
                            logger.warning(f"Skipping Ignored URL: {url}")
                            continue
                        
                        results.append(SearchResult(
                            title=title,
                            url=url,
                            snippet=snippet,
                            source='google_serp_mobile',
                            credibility_score=calculate_credibility_score(url, title, snippet)
                        ))

                        if len(results) >= 10:
                            break
                else:
                    # Update data to use mobile data for fallbacks
                    data = mobile_data
            
            # If still no results, try news search
            if not results and "organic_results" not in data:
                logger.info("No organic results found, trying news search")
                params["device"] = "desktop"  # Reset to desktop
                params["tbm"] = "nws"  # News search
                
                news_response = requests.get("https://serpapi.com/search", params=params)
                news_response.raise_for_status()
                news_data = news_response.json()
                
                if "news_results" in news_data:
                    logger.info(f"Found {len(news_data['news_results'])} news results")
                    for result in news_data["news_results"]:
                        title = result.get("title", "")
                        url = result.get("link", "")
                        snippet = result.get("snippet", "")

                        # Skip ignored URLs
                        if contain_ignore_domains(url):
                            logger.warning(f"Skipping ignored URL: {url}")
                            continue
                        
                        results.append(SearchResult(
                            title=title,
                            url=url,
                            snippet=snippet,
                            source='google_serp_news',
                            credibility_score=calculate_credibility_score(url, title, snippet)
                        ))

                        if len(results) >= 10:
                            break
                else:
                    # Update data to use news data for fallbacks
                    data = news_data
            
            # If still no results, try to extract from other result types
            if not results:
                logger.info("No standard results found, trying to extract from alternative result types")
                
                # Try knowledge_graph
                if "knowledge_graph" in data:
                    kg = data["knowledge_graph"]
                    title = kg.get("title", "Knowledge Graph Result")
                    url = kg.get("website", "") or kg.get("source", {}).get("link", "")

                    if contain_ignore_domains(url):
                        logger.warning(f"Skipping ignored URL: {url}")
                    else:
                        # Build a snippet from available knowledge graph data
                        snippet_parts = []
                        if "description" in kg:
                            snippet_parts.append(kg["description"])
                        if "type" in kg:
                            snippet_parts.append(f"Type: {kg['type']}")
                    
                        # Add any attributes as key-value pairs
                        for key, value in kg.items():
                            if isinstance(value, str) and key not in ["title", "description", "type", "website"]:
                                snippet_parts.append(f"{key}: {value}")
                    
                        snippet = " ".join(snippet_parts)
                    
                        if url:  # Only add if we have a URL
                            results.append(SearchResult(
                                title=title,
                                url=url,
                                snippet=snippet,
                                source='google_serp_kg',
                                credibility_score=0.7  # Knowledge graph is usually reliable
                            ))
                
                # Try top_stories
                if "top_stories" in data and not results:
                    for story in data["top_stories"]:
                        title = story.get("title", "")
                        url = story.get("link", "")
                        snippet = story.get("snippet", "") or story.get("source", "")

                        # Skip ignored URLs
                        if contain_ignore_domains(url):
                            logger.warning(f"Skipping ignored URL: {url}")
                            continue
                        
                        if url:
                            results.append(SearchResult(
                                title=title,
                                url=url,
                                snippet=snippet,
                                source='google_serp_top_stories',
                                credibility_score=calculate_credibility_score(url, title, snippet)
                            ))
                        
                        if len(results) >= 10:
                            break
                
                # Try answer_box
                if "answer_box" in data and not results:
                    answer = data["answer_box"]
                    title = answer.get("title", "Google Answer")
                    url = answer.get("link", "")

                    if contain_ignore_domains(url):
                        logger.warning(f"Skipping ignored URL: {url}")
                    else:
                        # Build snippet from answer box content
                        snippet_parts = []
                        if "snippet" in answer:
                            snippet_parts.append(answer["snippet"])
                        if "answer" in answer:
                            snippet_parts.append(answer["answer"])
                    
                        snippet = " ".join(snippet_parts)
                    
                        if url:
                            results.append(SearchResult(
                                title=title,
                                url=url,
                                snippet=snippet,
                                source='google_serp_answer_box',
                                credibility_score=0.8  # Answer boxes are usually reliable
                            ))
            
            # Fetch full content for the first few results
            for i, result in enumerate(results[:6]):  # Only fetch content for top results
                try:
                    # Skip content extraction for videos
                    result_data = data.get("organic_results", [])[i] if i < len(data.get("organic_results", [])) else None
                    if is_video_content(result.url, result_data=result_data):
                        result.content = "[This is a video result. Content extraction is not available for videos.]"
                        logger.warning(f"Skipping content extraction for video: {result.url}")
                        continue

                    # Skip ignored URLs
                    if contain_ignore_domains(result.url):
                        logger.warning(f"Skipping ignored URL: {result.url}")
                        continue
                        
                    page_content, content_type = fetch_webpage_content(result.url)
                    if page_content:
                        text_content = html_converter.handle(page_content)
                        result.content = text_content
                except Exception as e:
                    logger.warning(f"Error fetching content for {result.url}: {str(e)}")
            
            for i, result in enumerate(results):  # Only fetch content for top 2 results
                logger.info(f"Extracted result - {result.url} is:\n ${result.content}, title is ${result.title}, snippet is {result.snippet}")

            return results
        
        # Fall back to direct Google search if SerpAPI is not configured
        results = []
        for url in google_search(query, num_results=num_results):
            # Google search only returns URLs, so we need to fetch the page to get more info
            try:
                page_content, content_type = fetch_webpage_content(url)
                if not page_content:
                    continue
                
                soup = BeautifulSoup(page_content, 'html.parser')
                title = soup.title.string if soup.title else url
                
                # Extract a snippet from the content
                text_content = html_converter.handle(page_content)
                snippet = text_content[:200] + "..." if len(text_content) > 200 else text_content
                
                results.append(SearchResult(
                    title=title,
                    url=url,
                    snippet=snippet,
                    source='google',
                    credibility_score=calculate_credibility_score(url, title, snippet),
                    content=text_content
                ))
            except Exception as e:
                logger.warning(f"Error processing Google search result {url}: {str(e)}")
        
        return results
    except Exception as e:
        logger.error(f"Google search error: {str(e)}")
        return []


def is_video_content(url: str, result_data: dict = None) -> bool:
    """
    Determine if a search result is video content that can't be extracted.
    
    Args:
        url: The URL of the result
        result_data: Optional additional data about the result from SerpAPI
        
    Returns:
        True if the content is a video, False otherwise
    """
    # Check URL for common video domains
    video_domains = ['youtube.com', 'youtu.be', 'vimeo.com', 'dailymotion.com', 
                     'twitch.tv', 'tiktok.com', 'facebook.com/watch', 'rumble.com',
                     'bilibili.com', 'ted.com/talks', 'vevo.com']
    
    if any(domain in url.lower() for domain in video_domains):
        return True
    
    # Check for video-specific paths
    video_paths = ['/watch', '/video', '/embed', '/shorts', '/live', '/v/', '/media']
    parsed_url = urlparse(url)
    if any(path in parsed_url.path for path in video_paths):
        return True
    
    # Check for video file extensions
    video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv']
    if any(url.lower().endswith(ext) for ext in video_extensions):
        return True
    
    # Check SerpAPI metadata if available
    if result_data:
        # Check if SerpAPI has marked this as a video
        if result_data.get('type') == 'video':
            return True
        
        # Check for video-specific fields
        if any(key in result_data for key in ['video_link', 'duration', 'watch_page', 'video_id']):
            return True
        
        # Check if the title or snippet contains video indicators
        title = result_data.get('title', '').lower()
        snippet = result_data.get('snippet', '').lower()
        video_indicators = ['video', 'watch', 'youtube', 'stream', 'streaming', 'live']
        
        if any(indicator in title for indicator in video_indicators) and any(domain in url.lower() for domain in ['youtube', 'vimeo', 'dailymotion']):
            return True
    
    return False

def calculate_credibility_score(url: str, title: str, snippet: str) -> float:
    """
    Calculate a credibility score for a search result.
    
    Args:
        url: The URL of the result
        title: The title of the result
        snippet: The snippet of the result
        
    Returns:
        A credibility score between 0 and 1
    """
    score = 0.5  # Start with a neutral score
    
    # Domain-based scoring
    domain_scores = {
        '.edu': 0.2,
        '.gov': 0.2,
        '.org': 0.1,
        'wikipedia.org': 0.15,
        'github.com': 0.1,
        'stackoverflow.com': 0.1,
        'medium.com': 0.05,
        'nytimes.com': 0.1,
        'bbc.com': 0.1,
        'cnn.com': 0.05,
        'reuters.com': 0.1
    }
    
    for domain, domain_score in domain_scores.items():
        if domain in url:
            score += domain_score
            break
    
    # Content-based scoring
    if re.search(r'\d{4}', title):  # Contains a year, likely more current
        score += 0.05
    
    if any(word in snippet.lower() for word in ['study', 'research', 'analysis', 'data']):
        score += 0.05
    
    if any(word in snippet.lower() for word in ['opinion', 'commentary', 'editorial']):
        score -= 0.05
    
    # Cap the score between 0 and 1
    return max(0.0, min(1.0, score))

def fetch_webpage_content(url: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Fetch the content of a webpage and detect content type.
    
    Args:
        url: The URL to fetch
        
    Returns:
        Tuple of (content, content_type) where content is the HTML content of the page,
        and content_type is the MIME type of the content
    """
    # List of realistic user agents to rotate through
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:124.0) Gecko/20100101 Firefox/124.0',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPad; CPU OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
    ]
    
    # Extract domain for domain-specific handling
    domain = url.split('/')[2] if len(url.split('/')) > 2 else ''
    
    # Special handling for specific domains
    if 'reuters.com' in domain:
        return fetch_reuters_content(url)
    elif 'ncc-cma.net' in domain:
        return fetch_ncc_cma_content(url)
    elif 'ir.tesla.com' in domain and url.endswith('.pdf'):
        return fetch_tesla_pdf_content(url)
    elif 'barchart.com' in domain:
        return fetch_barchart_content(url)
    elif 'bloomberg.com' in domain:
        return fetch_bloomberg_content(url)
    
    # For other sites, use the standard approach with rotating user agents
    for attempt in range(3):  # Try up to 3 times with different user agents
        try:
            # Create a session to maintain cookies
            session = requests.Session()
            
            # Select a random user agent
            user_agent = user_agents[attempt % len(user_agents)]
            
            # Modern browser headers
            headers = {
                'User-Agent': user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Cache-Control': 'max-age=0',
                'DNT': '1',  # Do Not Track
            }
            
            # Add domain-specific headers
            if 'nytimes.com' in domain or 'wsj.com' in domain:
                headers['Referer'] = 'https://www.google.com/'
            elif 'barrons.com' in domain or 'investors.com' in domain:
                # Financial news sites often need special handling
                headers['Referer'] = 'https://www.google.com/finance'
                headers['Sec-Fetch-Site'] = 'cross-site'
            elif 'cleantechnica.com' in domain:
                # Add cookie consent for cleantechnica
                headers['Cookie'] = 'euconsent-v2=accepted'
            else:
                # For most sites, use a Google search referer
                headers['Referer'] = 'https://www.google.com/search?q=' + '+'.join(url.split('/')[-1].split('-'))
            
            # Add a small delay between attempts to avoid rate limiting
            if attempt > 0:
                time.sleep(1 + attempt)
            
            # Make the request with the session
            response = session.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            content_type = response.headers.get('Content-Type', '')
            html_content = response.text
            
            # Extract the main content using our content extraction module
            if html_content and 'html' in content_type.lower():
                # Get article metadata
                metadata = extract_article_metadata(html_content, url)
                
                # Extract the main content
                main_content, confidence = extract_main_content(html_content, url)
                
                # Check if the content is behind a paywall
                if is_paywall_content(html_content, main_content):
                    logger.warning(f"Detected paywall for {url}")
                
                # If we successfully extracted main content, return it
                if main_content and len(main_content) > 200:
                    logger.info(f"Successfully extracted main content from {url} with confidence {confidence:.2f}")
                    return main_content, "text/markdown"
            
            # If content extraction failed or it's not HTML, return the original content
            return html_content, content_type
            
        except requests.exceptions.HTTPError as e:
            logger.warning(f"HTTP error on attempt {attempt+1}: {str(e)}")
            if attempt == 2:  # Last attempt
                logger.warning(f"Failed to fetch {url} after multiple attempts")
                return None, None
        except Exception as e:
            logger.warning(f"Error fetching webpage {url} on attempt {attempt+1}: {str(e)}")
            if attempt == 2:  # Last attempt
                return None, None
    
    return None, None

def fetch_ncc_cma_content(url: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Special handler for NCC-CMA website which returns 412 Precondition Failed errors.
    
    Args:
        url: The NCC-CMA URL to fetch
        
    Returns:
        Tuple of (content, content_type)
    """
    try:
        # For sites with 412 errors, we need a completely fresh session without any headers
        # that might trigger conditional request validation
        
        # Try multiple user agents
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64; rv:124.0) Gecko/20100101 Firefox/124.0',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
        ]
        
        # Try each user agent
        for user_agent in user_agents:
            try:
                # Create a completely fresh session for each attempt
                session = requests.Session()
                
                # Disable automatic content decompression to avoid any automatic header additions
                session.headers.clear()
                
                # Minimal headers to avoid triggering 412
                headers = {
                    'User-Agent': user_agent,
                    'Accept': '*/*',
                    'Connection': 'close'  # Explicitly close connection to avoid keep-alive issues
                }
                
                # Make the request with minimal headers
                response = session.get(url, headers=headers, timeout=15)
                
                if response.status_code == 200:
                    content_type = response.headers.get('Content-Type', '')
                    return response.text, content_type
                
                # If we get a 412, try a direct request with urllib
                if response.status_code == 412:
                    logger.info(f"Attempting alternative method for NCC-CMA content: {url}")
                    
                    # Use urllib directly to avoid any request headers that requests might add
                    import urllib.request
                    
                    # Create a custom opener with minimal headers
                    opener = urllib.request.build_opener()
                    opener.addheaders = [('User-Agent', user_agent)]
                    
                    # Make the request
                    with opener.open(url, timeout=15) as response:
                        content = response.read().decode('utf-8', errors='ignore')
                        content_type = response.headers.get('Content-Type', '')
                        return content, content_type
            
            except Exception as inner_e:
                logger.warning(f"Error with user agent {user_agent} for NCC-CMA: {str(inner_e)}")
                continue
        
        # If all attempts fail, provide a fallback message
        fallback_content = f"""
        <html>
        <head><title>NCC-CMA Climate Prediction</title></head>
        <body>
        <h1>NCC-CMA Climate Prediction Data</h1>
        <p>The requested climate prediction data could not be accessed directly due to server restrictions.</p>
        <p>This data may be available through the National Climate Center website.</p>
        </body>
        </html>
        """
        
        return fallback_content, "text/html"
        
    except Exception as e:
        logger.warning(f"Error fetching NCC-CMA content {url}: {str(e)}")
        
        # Provide a fallback message even if all attempts fail
        fallback_content = f"""
        <html>
        <head><title>NCC-CMA Climate Prediction</title></head>
        <body>
        <h1>NCC-CMA Climate Prediction Data</h1>
        <p>The requested climate prediction data could not be accessed directly due to server restrictions.</p>
        <p>This data may be available through the National Climate Center website.</p>
        </body>
        </html>
        """
        
        return fallback_content, "text/html"

def perform_web_search(query: str) -> Tuple[List[SearchResult], str]:
    """
    Perform a web search using available search providers.
    
    Args:
        query: The search query
        
    Returns:
        Tuple of (search results, extracted content)
    """
    results = []
    content = ""
    
    # Use Google search
    if 'google' in search_providers:
        google_results = search_providers['google'](query)
        if google_results:
            results.extend(google_results)
            
            # Extract content from the first few results
            extracted_content = []
            for result in google_results[:3]:  # Only use top 3 results for content
                if result.content:
                    extracted_content.append(f"### {result.title}\n\n{result.content}\n\n")
            
            content = "\n".join(extracted_content)
    
    # Sort results by credibility score
    results.sort(key=lambda x: x.credibility_score, reverse=True)
    
    return results, content

def fetch_tesla_pdf_content(url: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Special handler for Tesla PDF files which have strong access controls.
    
    Args:
        url: The Tesla PDF URL to fetch
        
    Returns:
        Tuple of (content, content_type)
    """
    try:
        # Create a session with cookies enabled
        session = requests.Session()
        
        # First, visit the Tesla IR main page to get cookies
        ir_main_page = "https://ir.tesla.com/"
        
        # Headers for the initial request
        initial_headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
        }
        
        # Visit the IR main page first to get cookies
        session.get(ir_main_page, headers=initial_headers, timeout=10)
        
        # Short delay to mimic human behavior
        time.sleep(2)
        
        # Try to get the PDF file with the session cookies
        pdf_headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
            'Accept': 'application/pdf,*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': ir_main_page,
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
        }
        
        response = session.get(url, headers=pdf_headers, timeout=15)
        
        # If direct access fails, try to find an alternative source
        if response.status_code in (401, 403):
            # Extract the PDF filename from the URL
            pdf_filename = url.split('/')[-1]
            
            # Try to find the PDF through the SEC EDGAR database
            # Most Tesla filings are available on SEC EDGAR
            sec_search_url = f"https://www.sec.gov/edgar/search/#/q={pdf_filename}&dateRange=custom"
            
            # Log the fallback attempt
            logger.info(f"Attempting to find Tesla PDF through SEC EDGAR: {sec_search_url}")
            
            # Return a message about the alternative source
            alternative_content = f"""
            <html>
            <head><title>Tesla PDF Alternative Source</title></head>
            <body>
            <h1>Tesla PDF Document</h1>
            <p>The requested PDF document is not directly accessible due to access restrictions.</p>
            <p>This document may be available through the SEC EDGAR database:</p>
            <p><a href="{sec_search_url}">{sec_search_url}</a></p>
            </body>
            </html>
            """
            
            return alternative_content, "text/html"
        
        response.raise_for_status()
        content_type = response.headers.get('Content-Type', '')
        return response.content.decode('utf-8', errors='ignore'), content_type
        
    except Exception as e:
        logger.warning(f"Error fetching Tesla PDF content {url}: {str(e)}")
        
        # Provide a fallback message even if all attempts fail
        fallback_content = f"""
        <html>
        <head><title>Tesla PDF Alternative Source</title></head>
        <body>
        <h1>Tesla PDF Document</h1>
        <p>The requested PDF document is not directly accessible due to access restrictions.</p>
        <p>This document may be available through the Tesla Investor Relations website or SEC EDGAR database.</p>
        </body>
        </html>
        """
        
        return fallback_content, "text/html"

def perform_multi_provider_search(query: str, engines: List[str], num_results: int = 5) -> List[SearchResult]:
    """
    Perform a search using multiple search providers.
    
    Args:
        query: The search query
        engines: List of search engines to use (e.g., ['google'])
        num_results: Number of results to return per search engine
        
    Returns:
        List of search results from all providers
    """
    logger.info(f"Performing multi-provider search with query: '{query}', engines: {engines}, num_results: {num_results}")
    
    all_results = []
    
    # Use each specified search provider
    for engine in engines:
        if engine in search_providers:
            logger.info(f"Using search provider: {engine}")
            try:
                provider_results = search_providers[engine](query, num_results)
                if provider_results:
                    logger.info(f"Provider {engine} returned {len(provider_results)} results")
                    all_results.extend(provider_results)
                else:
                    logger.warning(f"Provider {engine} returned no results")
            except Exception as e:
                logger.error(f"Error using search provider {engine}: {str(e)}")
        else:
            logger.warning(f"Unknown search provider: {engine}")
    
    # Sort results by credibility score
    all_results.sort(key=lambda x: x.credibility_score, reverse=True)
    
    # Remove duplicates based on URL
    unique_results = []
    seen_urls = set()
    
    for result in all_results:
        if result.url not in seen_urls:
            unique_results.append(result)
            seen_urls.add(result.url)
    
    logger.info(f"Multi-provider search returned {len(unique_results)} unique results")
    return unique_results

def extract_content_from_results(results: List[SearchResult], extraction_level: str = "summary") -> str:
    """
    Extract and format content from search results.
    
    Args:
        results: List of search results
        extraction_level: Level of content extraction: 'metadata' (minimal), 'summary' (default), or 'full'
        
    Returns:
        Formatted content from search results
    """
    logger.info(f"Extracting content from {len(results)} search results with extraction level: {extraction_level}")
    
    extracted_content = []
    
    # Determine how many results to include based on extraction level
    num_results = len(results)
    if extraction_level == "metadata":
        # Only include metadata for all results
        for i, result in enumerate(results):
            extracted_content.append(f"### {i+1}. {result.title}\n")
            extracted_content.append(f"**URL**: {result.url}\n")
            extracted_content.append(f"**Source**: {result.source}\n")
            extracted_content.append(f"**Snippet**: {result.snippet}\n\n")
    
    elif extraction_level == "summary":
        # Include full content for top 3 results, metadata for the rest
        for i, result in enumerate(results):
            extracted_content.append(f"### {i+1}. {result.title}\n")
            extracted_content.append(f"**URL**: {result.url}\n")
            extracted_content.append(f"**Source**: {result.source}\n")
            
            if result.content:
                # Truncate content to a reasonable length for summary
                content = result.content[:2000] + "..." if len(result.content) > 2000 else result.content
                extracted_content.append(f"**Content**:\n{content}\n\n")
            else:
                extracted_content.append(f"**Snippet**: {result.snippet}\n\n")
    
    elif extraction_level == "full":
        # Include full content for all results that have it
        for i, result in enumerate(results):
            extracted_content.append(f"### {i+1}. {result.title}\n")
            extracted_content.append(f"**URL**: {result.url}\n")
            extracted_content.append(f"**Source**: {result.source}\n")
            
            if result.content:
                extracted_content.append(f"**Content**:\n{result.content}\n\n")
            else:
                extracted_content.append(f"**Snippet**: {result.snippet}\n\n")
    
    logger.info(f"Extracted content length: {sum(len(c) for c in extracted_content)} characters")
    return "".join(extracted_content)

def fetch_barchart_content(url: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Enhanced handler for Barchart.com content with advanced anti-scraping bypass.
    
    Args:
        url: The Barchart.com URL to fetch
        
    Returns:
        Tuple of (content, content_type)
    """
    try:
        # Create multiple sessions with different configurations
        sessions = []
        
        # Configuration 1: Desktop browser with Google referer
        session1 = requests.Session()
        session1.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://www.google.com/search?q=barchart+news',
            'sec-ch-ua': '"Chromium";v="122", "Google Chrome";v="122", "Not(A:Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
        })
        sessions.append(session1)
        
        # Configuration 2: Mobile browser
        session2 = requests.Session()
        session2.headers.update({
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.google.com/search?q=barchart+news',
        })
        sessions.append(session2)
        
        # Configuration 3: Firefox browser
        session3 = requests.Session()
        session3.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:124.0) Gecko/20100101 Firefox/124.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'cross-site',
            'Pragma': 'no-cache',
            'Cache-Control': 'no-cache',
        })
        sessions.append(session3)
        
        # Try each session with delays between attempts
        html_content = None
        
        for i, session in enumerate(sessions):
            try:
                # First visit the homepage to get cookies
                logger.info(f"Attempt {i+1}: Visiting Barchart homepage to get cookies")
                session.get('https://www.barchart.com/', timeout=10)
                
                # Short delay
                time.sleep(2)
                
                # Now try to access the article
                logger.info(f"Attempt {i+1}: Accessing article at {url}")
                response = session.get(url, timeout=15)
                
                if response.status_code == 200 and len(response.text) > 5000:
                    logger.info(f"Attempt {i+1}: Successfully retrieved content")
                    html_content = response.text
                    break
                else:
                    logger.warning(f"Attempt {i+1}: Failed with status code {response.status_code}")
            
            except Exception as e:
                logger.warning(f"Attempt {i+1}: Error - {str(e)}")
            
            # Longer delay between session attempts
            time.sleep(3)
        
        # If we have HTML content, try to extract the main content
        if html_content:
            # Use the extract_main_content function from content_extractor module
            main_content, confidence = extract_main_content(html_content, url)
            
            # Check for common indicators of failed extraction
            failed_extraction_indicators = [
                "[Your browser of choice has not been tested",
                "Your browser of choice has not been tested",
                "FREE 30 Day Trial",
                "Sign up for Barchart",
                "Create a Free Account",
                "Log In",
                "Sign Up",
                "Subscribe Now",
                "Cookie Policy",
                "Privacy Policy"
            ]
            
            is_valid_content = main_content and len(main_content) > 200
            if is_valid_content:
                # Check if the content contains any of the failure indicators
                if not any(indicator in main_content for indicator in failed_extraction_indicators):
                    logger.info(f"Successfully extracted main content with confidence {confidence:.2f}")
                    return main_content, "text/markdown"
                else:
                    logger.warning(f"Extracted content contains navigation elements, not actual article content")
        
        # If all attempts fail, create a fallback message
        logger.warning("All extraction methods failed, returning fallback content")
        
        # Extract article title from URL for a more informative fallback message
        url_parts = url.split('/')
        article_slug = url_parts[-1] if len(url_parts) > 1 else ""
        article_title = article_slug.replace('-', ' ').title()
        
        fallback_content = f"""
        # {article_title}
        
        The requested article from Barchart.com could not be accessed directly due to website restrictions.
        
        You can try accessing this content through these alternative methods:
        
        - [Google Search for this article](https://www.google.com/search?q={article_slug.replace('-', '+')})
        - [Archive.is cached version](https://archive.is/{url})
        - [12ft.io paywall bypass](https://12ft.io/{url})
        
        Original URL: {url}
        """
        
        return fallback_content, "text/markdown"
        
    except Exception as e:
        logger.error(f"Error fetching Barchart.com content {url}: {str(e)}")
        
        # Provide a fallback message even if all attempts fail
        fallback_content = f"""
        # Barchart.com Article
        
        The requested article could not be accessed due to technical issues: {str(e)}
        
        You can try accessing this content through these alternative methods:
        
        - [Google Search for this article](https://www.google.com/search?q={url.split('/')[-1].replace('-', ' ')})
        - [Archive.is cached version](https://archive.is/{url})
        - [12ft.io paywall bypass](https://12ft.io/{url})
        
        Original URL: {url}
        """
        
        return fallback_content, "text/markdown"

def fetch_bloomberg_content(url: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Special handler for Bloomberg content which has strong anti-scraping measures.
    
    Args:
        url: The Bloomberg URL to fetch
        
    Returns:
        Tuple of (content, content_type)
    """
    try:
        # Create a session with cookies enabled
        session = requests.Session()
        
        # First, visit the Bloomberg homepage to get cookies
        homepage_url = "https://www.bloomberg.com/"
        homepage_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',  # Do Not Track
        }
        
        # Visit the homepage first to get cookies
        session.get(homepage_url, headers=homepage_headers, timeout=10)
        
        # Short delay to mimic human behavior
        time.sleep(1)
        
        # Now try to access the article with cookies from the homepage visit
        article_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': homepage_url,
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            # Add Google as referer to appear as if coming from search
            'Referer': 'https://www.google.com/search?q=bloomberg+news',
        }
        
        # Try to access the article directly
        response = session.get(url, headers=article_headers, timeout=15)
        logger.debug(f"Initial response status code: {response.status_code}")
        
        html_content = None
        
        # Check if we got a successful response
        if response.status_code == 200:
            html_content = response.text
        
        # If direct access failed, try with a different user agent (mobile)
        if html_content is None or response.status_code == 403:
            logger.info(f"Trying with a mobile user agent for Bloomberg article: {url}")
            
            # Create a fresh session
            mobile_session = requests.Session()
            
            # First visit the homepage with mobile user agent
            mobile_homepage_headers = {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
            
            # Visit the homepage first to get cookies
            mobile_session.get(homepage_url, headers=mobile_homepage_headers, timeout=10)
            
            # Short delay
            time.sleep(1)
            
            # Mobile browser headers for article
            mobile_headers = {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Referer': 'https://www.google.com/search?q=bloomberg+news',
                'Upgrade-Insecure-Requests': '1',
            }
            
            # Try to access with mobile browser emulation
            mobile_response = mobile_session.get(url, headers=mobile_headers, timeout=15)
            logger.debug(f"Mobile browser emulation response status code: {mobile_response.status_code}")
            
            if mobile_response.status_code == 200:
                logger.info("Successfully fetched with mobile browser emulation")
                html_content = mobile_response.text
        
        # If mobile approach failed, try with a different approach using archive.is
        if html_content is None or response.status_code == 403:
            logger.info(f"Trying archive.is for Bloomberg article: {url}")
            
            # Create a fresh session
            archive_session = requests.Session()
            
            # Try to access the archived version
            archive_url = f"https://archive.is/{url}"
            archive_headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Referer': 'https://www.google.com/',
            }
            
            try:
                archive_response = archive_session.get(archive_url, headers=archive_headers, timeout=20)
                if archive_response.status_code == 200:
                    logger.info("Successfully fetched from archive.is")
                    html_content = archive_response.text
            except Exception as archive_e:
                logger.warning(f"Error accessing archive.is: {str(archive_e)}")
        
        # If we have HTML content, extract the main content
        if html_content:
            # Extract the main content using our content extraction module
            main_content, confidence = extract_main_content(html_content, url)
            
            # If we successfully extracted main content, return it
            if main_content and len(main_content) > 200:
                logger.info(f"Successfully extracted main content from Bloomberg article with confidence {confidence:.2f}")
                return main_content, "text/markdown"
            else:
                logger.warning(f"Failed to extract main content from Bloomberg article, confidence: {confidence:.2f}")
        
        # If everything failed, create a fallback message with links to alternative sources
        logger.warning("All fetch methods failed, returning fallback content")
        
        # Extract article title from URL for a more informative fallback message
        url_parts = url.split('/')
        article_slug = url_parts[-1] if len(url_parts) > 1 else ""
        article_title = article_slug.replace('-', ' ').title()
        
        # Extract the date and topic from the URL if possible
        date_match = re.search(r'(\d{4}-\d{2}-\d{2})', url)
        date_str = date_match.group(1) if date_match else ""
        
        # Try to extract the main topic (usually after /news/ in the URL)
        topic = ""
        if '/news/' in url and len(url_parts) > 4:
            topic_index = url_parts.index('news') + 1
            if topic_index < len(url_parts) - 1:  # Ensure there's a topic after 'news'
                topic = url_parts[topic_index]
        
        # Create a more informative title if we have date or topic
        if date_str or topic:
            if date_str and topic:
                article_title = f"Bloomberg {topic.capitalize()} Article ({date_str}): {article_title}"
            elif date_str:
                article_title = f"Bloomberg Article ({date_str}): {article_title}"
            elif topic:
                article_title = f"Bloomberg {topic.capitalize()} Article: {article_title}"
        
        # Create a fallback message with alternative sources
        fallback_content = f"""
        # {article_title}
        
        The requested article from Bloomberg could not be accessed directly due to website restrictions.
        
        This appears to be about Tesla stock and sales performance. Bloomberg articles are typically behind a paywall and have strong anti-scraping measures.
        
        You can try accessing this content through these alternative methods:
        
        - [Google Search for this article](https://www.google.com/search?q={article_slug.replace('-', '+')})
        - [Archive.is cached version](https://archive.is/{url})
        - [12ft.io paywall bypass](https://12ft.io/{url})
        - [Yahoo Finance Tesla page](https://finance.yahoo.com/quote/TSLA/) (for general Tesla stock information)
        
        Original URL: {url}
        """
        
        return fallback_content, "text/markdown"
        
    except Exception as e:
        logger.error(f"Error fetching Bloomberg content {url}: {str(e)}")
        
        # Provide a fallback message even if all attempts fail
        fallback_content = f"""
        # Bloomberg Article
        
        The requested article could not be accessed due to technical issues: {str(e)}
        
        Bloomberg articles are typically behind a paywall and have strong anti-scraping measures.
        
        You can try accessing this content through these alternative methods:
        
        - [Google Search for this article](https://www.google.com/search?q={url.split('/')[-1].replace('-', '+')})
        - [Archive.is cached version](https://archive.is/{url})
        - [12ft.io paywall bypass](https://12ft.io/{url})
        - [Yahoo Finance Tesla page](https://finance.yahoo.com/quote/TSLA/) (for general Tesla stock information)
        
        Original URL: {url}
        """
        
        return fallback_content, "text/markdown"

def fetch_reuters_content(url: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Special handler for Reuters content which has strong anti-scraping measures.
    
    Args:
        url: The Reuters URL to fetch
        
    Returns:
        Tuple of (content, content_type)
    """
    try:
        # Create a session with cookies enabled
        session = requests.Session()
        
        # First, visit the Reuters homepage to get cookies
        homepage_url = "https://www.reuters.com/"
        homepage_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            # Explicitly request English content
            'Accept-Language': 'en-US,en;q=0.9',
        }
        
        # Visit the homepage first to get cookies
        session.get(homepage_url, headers=homepage_headers, timeout=10)
        
        # Short delay to mimic human behavior
        time.sleep(1)
        
        # Now try to access the article with cookies from the homepage visit
        article_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': homepage_url,
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            # Explicitly request English content
            'Accept-Language': 'en-US,en;q=0.9',
        }
        
        # Try to access the article directly first
        response = session.get(url, headers=article_headers, timeout=10)
        logger.debug(f"Initial response status code: {response.status_code}")
        
        html_content = None
        
        # Check if we got a redirect page (in Chinese or other language)
        if "重新導向通知" in response.text or "redirect" in response.text.lower():
            logger.info(f"Detected redirect page for Reuters article: {url}")
            
            # Try to extract the redirect URL from the page
            redirect_match = re.search(r'href=[\'"]([^\'"]+)[\'"]', response.text)
            if redirect_match:
                redirect_url = redirect_match.group(1)
                logger.info(f"Found redirect URL: {redirect_url}")
                
                # Follow the redirect with explicit English language headers
                redirect_headers = article_headers.copy()
                redirect_headers['Accept-Language'] = 'en-US,en;q=0.9'
                redirect_response = session.get(redirect_url, headers=redirect_headers, timeout=10)
                
                if redirect_response.status_code == 200:
                    logger.info("Successfully followed redirect")
                    html_content = redirect_response.text
        
        # If direct access with redirect handling didn't work, try the US edition explicitly
        if html_content is None and (response.status_code != 200 or "重新導向通知" in response.text or len(response.text) < 5000):
            logger.info(f"Trying US edition for Reuters article: {url}")
            
            # Clear cookies and start fresh
            session = requests.Session()
            
            # Set cookies to request US edition
            session.cookies.set('reuters-cc', 'US', domain='.reuters.com')
            session.cookies.set('reuters-geo', 'us', domain='.reuters.com')
            
            us_headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Referer': 'https://www.reuters.com/',
                'Origin': 'https://www.reuters.com',
                'DNT': '1',
            }
            
            us_response = session.get(url, headers=us_headers, timeout=10)
            logger.debug(f"US edition response status code: {us_response.status_code}")
            
            if us_response.status_code == 200 and len(us_response.text) > 5000:
                logger.info("Successfully fetched with US edition")
                html_content = us_response.text
        
        # If US edition didn't work, try mobile version with improved URL handling
        if html_content is None and (response.status_code != 200 or "重新導向通知" in response.text or len(response.text) < 5000):
            logger.info(f"Trying mobile version for Reuters article: {url}")
            
            # Extract article path and ID for better mobile URL construction
            url_parts = url.split('/')
            article_path = '/'.join(url_parts[3:]) if len(url_parts) > 3 else ''
            
            # Try multiple mobile URL formats
            mobile_urls = []
            
            # Standard mobile URL
            mobile_urls.append(url.replace("www.reuters.com", "mobile.reuters.com"))
            
            # AMP version for business articles
            if '/business/' in url:
                article_id = url_parts[-1].split('-')[0] if len(url_parts) > 3 else ''
                if article_id.isdigit():
                    mobile_urls.append(f"https://mobile.reuters.com/article/amp/{article_id}")
            
            # Try direct mobile path
            if article_path:
                mobile_urls.append(f"https://mobile.reuters.com/{article_path}")
                
            # Try mobile article format
            if article_path:
                mobile_urls.append(f"https://mobile.reuters.com/article/{article_path}")
            
            # Try each mobile URL format
            for mobile_url in mobile_urls:
                try:
                    logger.debug(f"Trying mobile URL: {mobile_url}")
                    
                    # Use a fresh session for each attempt
                    mobile_session = requests.Session()
                    
                    # Set cookies for US edition
                    mobile_session.cookies.set('reuters-cc', 'US', domain='.reuters.com')
                    mobile_session.cookies.set('reuters-geo', 'us', domain='.reuters.com')
                    
                    mobile_headers = {
                        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Referer': 'https://www.google.com/search?q=reuters',
                    }
                    
                    mobile_response = mobile_session.get(mobile_url, headers=mobile_headers, timeout=15)
                    logger.debug(f"Mobile URL {mobile_url} response status code: {mobile_response.status_code}")
                    
                    # Check if we got a successful response with content
                    if mobile_response.status_code == 200 and len(mobile_response.text) > 5000:
                        logger.info(f"Successfully fetched mobile content from: {mobile_url}")
                        html_content = mobile_response.text
                        break
                except Exception as mobile_e:
                    logger.warning(f"Error with mobile URL {mobile_url}: {str(mobile_e)}")
                    continue
        
        # Try with a desktop browser emulation approach
        if html_content is None and (response.status_code != 200 or response.status_code == 401 or response.status_code == 403):
            logger.info(f"Trying desktop browser emulation for Reuters article: {url}")
            
            # Create a completely fresh session
            desktop_session = requests.Session()
            
            # Set cookies for US edition
            desktop_session.cookies.set('reuters-cc', 'US', domain='.reuters.com')
            desktop_session.cookies.set('reuters-geo', 'us', domain='.reuters.com')
            
            # Modern Chrome browser headers
            desktop_headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Referer': 'https://www.google.com/search?q=reuters+news',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'cross-site',
                'Sec-Fetch-User': '?1',
                'sec-ch-ua': '"Chromium";v="122", "Google Chrome";v="122", "Not(A:Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
                'DNT': '1',
            }
            
            # Try to access with full browser emulation
            desktop_response = desktop_session.get(url, headers=desktop_headers, timeout=15)
            logger.debug(f"Desktop browser emulation response status code: {desktop_response.status_code}")
            
            if desktop_response.status_code == 200 and len(desktop_response.text) > 5000:
                logger.info("Successfully fetched with desktop browser emulation")
                html_content = desktop_response.text
        
        # If all the above methods failed but we got a 200 response from the original request, use it
        if html_content is None and response.status_code == 200:
            logger.info("Using original response as fallback")
            html_content = response.text
        
        # If we have HTML content, extract the main content
        if html_content:
            # Extract the main content using our content extraction module
            main_content, confidence = extract_main_content(html_content, url)
            
            # If we successfully extracted main content, return it
            if main_content and len(main_content) > 200:
                logger.info(f"Successfully extracted main content from Reuters article with confidence {confidence:.2f}")
                return main_content, "text/markdown"
            else:
                logger.warning(f"Failed to extract main content from Reuters article, confidence: {confidence:.2f}")
        
        # If everything failed, create a fallback message with links to alternative sources
        logger.warning("All fetch methods failed, returning fallback content")
        fallback_content = f"""
        # Reuters Article
        
        The requested article could not be accessed directly due to regional restrictions.
        
        You can try accessing this content through these alternative methods:
        
        - [Google Search for this article](https://www.google.com/search?q={url.split('/')[-1].replace('-', ' ')})
        - [Archive.is cached version](https://archive.is/{url})
        - [12ft.io paywall bypass](https://12ft.io/{url})
        
        Original URL: {url}
        """
        
        return fallback_content, "text/markdown"
        
    except Exception as e:
        logger.error(f"Error fetching Reuters content {url}: {str(e)}")
        
        # Provide a fallback message even if all attempts fail
        fallback_content = f"""
        # Reuters Article
        
        The requested article could not be accessed due to technical issues: {str(e)}
        
        You can try accessing this content through these alternative methods:
        
        - [Google Search for this article](https://www.google.com/search?q={url.split('/')[-1].replace('-', ' ')})
        - [Archive.is cached version](https://archive.is/{url})
        - [12ft.io paywall bypass](https://12ft.io/{url})
        
        Original URL: {url}
        """
        
        return fallback_content, "text/markdown"

def contain_ignore_domains(text):  
    domains = ["https://finance.yahoo.com", "tesla.com", "sec.gov", "pdf"]  
    return any(domain in text for domain in domains)

async def perform_gpt4o_search(query: str) -> List[SearchResult]:
    """
    Perform a web search using GPT-4o-search-preview's built-in web search capability.
    
    Args:
        query: The search query
        num_results: Number of results to return
        
    Returns:
        List of search results
    """
    try:
        client = AsyncOpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_API_URL')
        )
        
        logger.info(f"Performing GPT-4o search with query: '{query}'")
        
        # Create a completion with web search enabled
        completion = await client.chat.completions.create(
            model="gpt-4o-search-preview",
            web_search_options={},
            messages=[{
                "role": "user",
                "content": f"Search for: {query}. Extract and summarise all contents that you find"
            }],
        )
        
        # Extract search results from the response
        content = completion.choices[0].message.content
        logger.info(f"GPT-4o search response: {content}")
        
        return content
        # Parse the JSON response
        try:
            # Try to extract JSON from the response if it's not pure JSON
            json_match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL)
            if json_match:
                content = json_match.group(1)
                logger.debug(f"Extracted JSON from code block: {content}")
            
            # Another common format is with just the array
            json_array_match = re.search(r'\[\s*\{.*\}\s*\]', content, re.DOTALL)
            if json_array_match and not json_match:
                content = json_array_match.group(0)
                logger.debug(f"Extracted JSON array: {content}")
            
            results_data = json.loads(content)
            results = []
            
            # Handle both array and object with results field
            if isinstance(results_data, dict) and "results" in results_data:
                results_data = results_data["results"]
            
            for item in results_data[:num_results]:
                # Skip URLs defined in ignored domain
                if contain_ignore_domains(item.get("url", "")):
                    logger.warning(f"Skipping Ignored URL: {item.get('url', '')}")
                    continue
                
                result = SearchResult(
                    title=item.get("title", ""),
                    url=item.get("url", ""),
                    snippet=item.get("snippet", ""),
                    source="gpt4o_search",
                    credibility_score=calculate_credibility_score(
                        item.get("url", ""), 
                        item.get("title", ""), 
                        item.get("snippet", "")
                    ),
                )
                results.append(result)
            
            logger.info(f"GPT-4o search returned {len(results)} results")
            return results
        except json.JSONDecodeError as e:
            # If the response isn't valid JSON, try to extract URLs and titles using regex
            logger.warning(f"Failed to parse GPT-4o search results as JSON: {e}")
            logger.debug(f"Raw content: {content}")
            
            # Extract URLs and titles using regex
            results = []
            url_pattern = r'https?://[^\s"\')>]+'
            urls = re.findall(url_pattern, content)
            
            for i, url in enumerate(urls[:num_results]):
                # Skip URLs defined in ignored domain
                if contain_ignore_domains(url):
                    logger.warning(f"Skipping Ignored URL: {url}")
                    continue
                
                # Try to find a title near this URL
                title = f"Result {i+1}"
                title_pattern = r'"([^"]+)".*?' + re.escape(url)
                title_match = re.search(title_pattern, content, re.DOTALL)
                if title_match:
                    title = title_match.group(1)
                
                # Try to find a snippet near this URL
                snippet = ""
                snippet_pattern = re.escape(url) + r'.*?([^"<>\[\]]{20,200})'
                snippet_match = re.search(snippet_pattern, content, re.DOTALL)
                if snippet_match:
                    snippet = snippet_match.group(1).strip()
                
                result = SearchResult(
                    title=title,
                    url=url,
                    snippet=snippet,
                    source="gpt4o_search",
                    credibility_score=calculate_credibility_score(url, title, snippet),
                )
                results.append(result)
            
            logger.info(f"GPT-4o search returned {len(results)} results (extracted with regex)")
            return results
            
    except Exception as e:
        logger.error(f"GPT-4o search error: {str(e)}")
        logger.debug(f"GPT-4o search error traceback: {traceback.format_exc()}")
        return []
