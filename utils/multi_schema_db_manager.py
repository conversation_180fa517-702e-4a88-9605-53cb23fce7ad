"""
Multi-schema database manager utility.

This module provides a utility class for managing multiple database schemas.
It provides methods for creating schemas, getting database engines and session
factories for specific schemas, and executing SQL queries on specific schemas.
"""

import os
import logging
import random
import asyncio
from typing import Dict, List, Optional, Any, Tuple, AsyncGenerator

from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine, AsyncSession
from sqlalchemy.ext.asyncio import async_sessionmaker
from sqlalchemy.sql import text
from sqlalchemy.exc import SQLAlchemyError, OperationalError, DisconnectionError

from config.settings import get_database_url
from utils.logging import logger


class MultiSchemaDBManager:
    """
    Multi-schema database manager utility.
    
    This class provides methods for managing multiple database schemas.
    It maintains a registry of database engines and session factories for
    each schema, allowing for efficient reuse of connections.
    """
    
    def __init__(self):
        """
        Initialize the multi-schema database manager.
        """
        # Dictionary of database engines for each schema
        self.engines: Dict[Optional[str], AsyncEngine] = {}
        
        # Dictionary of session factories for each schema
        self.session_factories: Dict[Optional[str], async_sessionmaker] = {}
    
    async def create_schema_if_not_exists(self, schema_name: str) -> bool:
        """
        Create a schema if it doesn't exist.
        
        Args:
            schema_name: Name of the schema to create
            
        Returns:
            bool: True if the schema was created, False if it already existed
        """
        logger.info(f"Creating schema if not exists: {schema_name}")
        
        # Get a database connection
        engine = await self.get_db_engine(None)
        
        # Check if the schema exists
        async with engine.begin() as conn:
            result = await conn.execute(
                text(f"SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{schema_name}'")
            )
            schema_exists = result.fetchone() is not None
            
            if not schema_exists:
                # Create the schema
                await conn.execute(text(f"CREATE SCHEMA IF NOT EXISTS {schema_name}"))
                logger.info(f"Created schema: {schema_name}")
                return True
            else:
                logger.info(f"Schema already exists: {schema_name}")
                return False
    
    async def list_schemas(self) -> List[str]:
        """
        Get a list of all schemas in the database.
        
        Returns:
            List[str]: List of schema names
        """
        logger.info("Listing all schemas")
        
        # Get a database connection
        engine = await self.get_db_engine(None)
        
        # Get all schemas
        async with engine.begin() as conn:
            result = await conn.execute(
                text("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA")
            )
            schemas = [row[0] for row in result.fetchall()]
            
            return schemas
    
    async def get_db_engine(self, schema_name: Optional[str] = None) -> AsyncEngine:
        """
        Get a database engine for a specific schema.
        
        Args:
            schema_name: Name of the schema to get an engine for.
                If None, the default schema is used.
                
        Returns:
            AsyncEngine: SQLAlchemy async engine
        """
        # Check if an engine already exists for this schema
        if schema_name in self.engines:
            return self.engines[schema_name]
        
        # Create a new engine
        database_url = get_database_url(schema_name)
        engine = create_async_engine(database_url, echo=False, pool_pre_ping=True)
        
        # Store the engine for reuse
        self.engines[schema_name] = engine
        
        return engine
    
    async def get_db_session_factory(self, schema_name: Optional[str] = None) -> async_sessionmaker:
        """
        Get a database session factory for a specific schema.
        
        Args:
            schema_name: Name of the schema to get a session factory for.
                If None, the default schema is used.
                
        Returns:
            async_sessionmaker: SQLAlchemy async session factory
        """
        # Check if a session factory already exists for this schema
        if schema_name in self.session_factories:
            return self.session_factories[schema_name]
        
        # Get the engine for this schema
        engine = await self.get_db_engine(schema_name)
        
        # Create a new session factory
        session_factory = async_sessionmaker(engine, expire_on_commit=False, class_=AsyncSession)
        
        # Store the session factory for reuse
        self.session_factories[schema_name] = session_factory
        
        return session_factory
    
    async def get_db_session(self, schema_name: Optional[str] = None) -> AsyncSession:
        """
        Get a database session for a specific schema.
        
        Args:
            schema_name: Name of the schema to get a session for.
                If None, the default schema is used.
                
        Returns:
            AsyncSession: SQLAlchemy async session
        """
        # Get the session factory for this schema
        session_factory = await self.get_db_session_factory(schema_name)
        
        # Create a new session
        session = session_factory()
        
        return session
    
    async def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None, schema_name: Optional[str] = None) -> Any:
        """
        Execute a SQL query on a specific schema.
        
        Args:
            query: SQL query to execute
            params: Query parameters
            schema_name: Name of the schema to execute the query on.
                If None, the default schema is used.
                
        Returns:
            Any: Query result
        """
        logger.info(f"Executing query on schema '{schema_name}': {query}")
        
        # Get a database session
        async with await self.get_db_session(schema_name) as session:
            # Execute the query
            result = await session.execute(text(query), params or {})
            
            return result
    
    async def execute_query_with_retry(self, query: str, params: Optional[Dict[str, Any]] = None, schema_name: Optional[str] = None, max_retries: int = 3, base_delay: float = 1.0) -> Any:
        """
        Execute a SQL query with retry logic.
        
        Args:
            query: SQL query to execute
            params: Query parameters
            schema_name: Name of the schema to execute the query on
            max_retries: Maximum number of retry attempts
            base_delay: Base delay between retries in seconds
            
        Returns:
            Any: Query result
        """
        retry_count = 0
        last_exception = None
        
        while retry_count < max_retries:
            try:
                result = await self.execute_query(query, params, schema_name)
                return result  # Success, return the result
            except (OperationalError, DisconnectionError) as e:
                # These are connection-related errors that might be resolved with a retry
                last_exception = e
                retry_count += 1
                
                if "Lost connection" in str(e) or "Connection refused" in str(e) or "Connection timed out" in str(e):
                    logger.warning(f"Database connection error (attempt {retry_count}/{max_retries}): {str(e)}")
                    
                    if retry_count < max_retries:
                        # Calculate exponential backoff with jitter
                        delay = base_delay * (2 ** (retry_count - 1)) + random.uniform(0, 0.5)
                        logger.info(f"Retrying database operation in {delay:.2f} seconds...")
                        await asyncio.sleep(delay)
                else:
                    # Other SQLAlchemy operational errors that might not benefit from retrying
                    logger.error(f"Database operational error: {str(e)}")
                    raise
            except SQLAlchemyError as e:
                # Non-connection SQLAlchemy errors (like integrity errors) shouldn't be retried
                logger.error(f"Database error: {str(e)}")
                raise
            except Exception as e:
                # Other unexpected errors
                logger.error(f"Error in database operation: {str(e)}")
                raise
        
        # If we've exhausted all retries, raise the last exception
        if last_exception:
            logger.error(f"Database operation failed after {max_retries} attempts: {str(last_exception)}")
            raise last_exception
    
    async def close(self) -> None:
        """
        Close all database connections.
        """
        logger.info("Closing all database connections")
        
        # Close all engines
        for schema_name, engine in self.engines.items():
            await engine.dispose()
            logger.info(f"Closed database connection for schema: {schema_name}")
        
        # Clear the dictionaries
        self.engines = {}
        self.session_factories = {}


class AsyncSessionContext:
    """
    Async context manager for database sessions.
    """
    
    def __init__(self, schema_name: Optional[str] = None):
        self.schema_name = schema_name
        self.session = None
    
    async def __aenter__(self) -> AsyncSession:
        self.session = await db_manager.get_db_session(self.schema_name)
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            if exc_type is not None:
                # An exception occurred, rollback the transaction
                await self.session.rollback()
            else:
                # No exception, commit the transaction
                try:
                    await self.session.commit()
                except SQLAlchemyError as e:
                    logger.error(f"Error committing transaction: {str(e)}")
                    await self.session.rollback()
                    raise
            
            # Close the session
            await self.session.close()


# Create a singleton instance of the manager
db_manager = MultiSchemaDBManager()

# Export the context manager for easier imports
async_session_context = AsyncSessionContext

# Convenience function to get a database session
async def get_session(schema_name: Optional[str] = None) -> AsyncSession:
    """
    Convenience function to get a database session.
    """
    return await db_manager.get_db_session(schema_name)
