"""
Query classifier for financial queries.

This module provides functionality to classify user queries into different financial query types
using OpenAI's chat completion API. The classification is used to enhance queries with
additional context before sending them to the Assistant API.
"""

import json
from typing import Dict, Any, Optional
import asyncio
from openai import Async<PERSON>penA<PERSON>
from utils.logging import logger
from utils.async_cross_api_cache import Async<PERSON><PERSON><PERSON><PERSON><PERSON>
from config.settings import settings
from utils.ticker_db_functions import get_ticker_by_name

# Initialize the OpenAI client
client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)

# Initialize cache for query classifications
cache = AsyncCrossAPICache(namespace="query_classifier", ttl=3600)  # 1 hour TTL

# Query type contexts
QUERY_TYPE_CONTEXTS = {
    "Analysing Stocks": settings.QUERY_CONTEXT_ANALYSING_STOCKS,
    "Screening Stocks": settings.QUERY_CONTEXT_SCREENING_STOCKS,
    "Summarising Company Financial Filings": settings.QUERY_CONTEXT_SUMMARISING_FILINGS,
    "Analysing Market Events": settings.QUERY_CONTEXT_ANALYSING_MARKET,
    "Others": settings.QUERY_CONTEXT_OTHERS,
}

async def classify_query_type(query: str) -> Dict[str, str]:
    """
    Classify a user query into one of four financial query types using OpenAI's chat completion API.
    
    Args:
        query: The user's query text
        
    Returns:
        Dict containing the classification type
    """
    # Check cache first
    cache_key = f"classify:{query}"
    cached_result = await cache.get(cache_key)
    if cached_result:
        logger.info(f"Using cached classification for query: {query[:50]}...")
        return json.loads(cached_result)
    
    logger.info(f"Classifying query type: {query[:100]}...")
    
    try:
        # Create the prompt for classification
        system_prompt = settings.QUERY_CONTEXT_SYSTEM_PROMPT
        
        # Make the API call to classify the query
        response = await client.chat.completions.create(
            model="gpt-4.1-mini",  # Using a smaller model for classification to save costs
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": query}
            ],
            temperature=0.1,  # Low temperature for more deterministic results
            max_tokens=200,    # We only need a short response
            response_format={"type": "json_object"}  # Ensure JSON response
        )
        
        # Extract the classification from the response
        classification_text = response.choices[0].message.content.strip()
        classification = json.loads(classification_text)
        logger.info(f"classification is {classification}")
        
        # Validate the classification
        if "type" not in classification and "finance" in classification and classification["finance"] is not False:
            logger.warning(f"Invalid classification response: {classification_text}")
            classification["type"] = "Others"  # Default to Others
        
        # Ensure the type is one of our defined types
        if classification["type"] not in QUERY_TYPE_CONTEXTS and "finance" in classification and classification["finance"] is not False:
            logger.warning(f"Unknown classification type: {classification['type']}")
            classification["type"] = "Others"  # Default to Analysing Stocks
        
        if classification["context"] is False:
            # Only Cache the result if context is False
            # i.e if user ask 'Continue', there is no need to cache it
            await cache.set(cache_key, json.dumps(classification))
        
        if classification["type"] == 'Summarising Company Financial Filings':
            ticker = await get_ticker_by_name(classification["ticker"])
            if ticker != '':
                logger.info(f"Ticker found for {classification['ticker']}: {ticker}")
                classification["ticker"] = ticker
        
        logger.info(f"Query classified as: {classification['type']}")
        return classification
        
    except Exception as e:
        logger.error(f"Error classifying query: {str(e)}")
        # Return default classification in case of error
        return {"type": "Analysing Stocks"}

async def enhance_query_with_context(query: str) -> tuple[str, Dict[str, str], str]:
    """
    Enhance a user query with additional context based on its classification.
    
    Args:
        query: The user's query text
        
    Returns:
        A tuple containing:
        - Enhanced query with additional context
        - Classification dictionary with query type
    """
    try:
        # Classify the query
        classification = await classify_query_type(query)
        logger.info(f"Classification: {classification}")
        query_type = classification.get("type", "Others")
        
        # Get the appropriate context for this query type
        additional_context = QUERY_TYPE_CONTEXTS.get(query_type, "")
        
        # Enhance the query with the additional context
        if additional_context:
            enhanced_query = f"{query}\n: {additional_context}"
            logger.info(f"Enhanced query with context for type: {query_type}")
            return enhanced_query, classification, additional_context
        else:
            return query, classification, ""
            
    except Exception as e:
        logger.error(f"Error enhancing query with context: {str(e)}")
        default_classification = {"type": "Others"}
        return query, default_classification  # Return original query and default classification in case of error
