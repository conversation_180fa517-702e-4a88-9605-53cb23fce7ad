import json
import re
import time
from typing import List, Dict, Any, Optional, Tuple
from utils.logging import logger
from utils.search_utils import perform_web_search as basic_perform_web_search
from utils.search_utils import extract_search_keywords, should_perform_search, refine_search_query
from utils.enhanced_search import perform_web_search as enhanced_perform_web_search
from utils.enhanced_search import SearchResult
from config.constants import FACTUAL_PATTERNS, FOLLOW_UP_PATTERNS
from config.settings import settings

def analyze_query_for_search_need(query: str, current_round_messages: List[Dict[str, Any]], 
                                 previous_rounds_summary: Optional[str] = None) -> float:
    """
    Analyze a query to determine the likelihood that it needs web search.
    
    Args:
        query: The user's query
        current_round_messages: Messages from the current conversation round
        previous_rounds_summary: Optional summary of previous rounds
        
    Returns:
        float: Score between 0 and 1 indicating the likelihood that search is needed
    """
    score = 0.0
    
    # Check for explicit search requests
    if re.search(r'(search|look up|find|google|what is|who is|when did|where is|how to|latest|recent|news about|update on)', 
                query, re.IGNORECASE):
        score += 0.7
    
    # Check for factual or current events questions
    for pattern in FACTUAL_PATTERNS:
        if re.search(pattern, query, re.IGNORECASE):
            score += 0.5
            break
    
    # Check for follow-up questions
    for pattern in FOLLOW_UP_PATTERNS:
        if re.search(pattern, query, re.IGNORECASE):
            score += 0.3
            break
    
    # Check for temporal indicators (suggesting need for current information)
    if re.search(r'(today|now|current|latest|recent|update|news)', query, re.IGNORECASE):
        score += 0.4
    
    # Check for specific entities that might need factual information
    entities = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', query)
    if entities:
        score += min(0.3, len(entities) * 0.1)  # Cap at 0.3
    
    # Check for numerical queries that might need factual verification
    if re.search(r'\b\d+\b', query):
        score += 0.2
    
    # Consider previous rounds summary if available
    if previous_rounds_summary:
        # If the summary doesn't seem to contain information relevant to the query,
        # increase the search likelihood
        query_keywords = extract_search_keywords(query)
        keywords_in_summary = sum(1 for kw in query_keywords if kw.lower() in previous_rounds_summary.lower())
        if query_keywords and keywords_in_summary / len(query_keywords) < 0.5:
            score += 0.2
    
    # Cap the score between 0 and 1
    return min(1.0, score)

def get_relevant_context_from_rounds(query: str, current_round_messages: List[Dict[str, Any]], 
                                    previous_rounds_summary: Optional[str] = None) -> str:
    """
    Extract relevant context from conversation rounds to improve search queries.
    
    Args:
        query: The user's query
        current_round_messages: Messages from the current conversation round
        previous_rounds_summary: Optional summary of previous rounds
        
    Returns:
        str: Relevant context that can be used to enhance the search query
    """
    context = ""
    
    # Extract keywords from the query
    query_keywords = extract_search_keywords(query)
    
    # Look for relevant context in the current round
    if current_round_messages:
        for message in current_round_messages:
            if message.get('role') == 'user':
                # Skip the current query
                if message.get('content') == query:
                    continue
                
                # Look for messages with similar keywords
                msg_content = message.get('content', '')
                msg_keywords = extract_search_keywords(msg_content)
                
                # Check for keyword overlap
                common_keywords = set(query_keywords).intersection(set(msg_keywords))
                if common_keywords and len(common_keywords) >= min(2, len(query_keywords) // 2):
                    context += f"{msg_content} "
    
    # Add relevant information from previous rounds summary
    if previous_rounds_summary:
        # Extract sentences from the summary that contain query keywords
        sentences = re.split(r'[.!?]+', previous_rounds_summary)
        for sentence in sentences:
            if any(kw.lower() in sentence.lower() for kw in query_keywords):
                context += f"{sentence.strip()}. "
    
    return context.strip()

def enhance_search_query_with_context(query: str, context: str) -> str:
    """
    Enhance a search query with relevant context.
    
    Args:
        query: The original search query
        context: Relevant context from the conversation
        
    Returns:
        str: Enhanced search query
    """
    if not context:
        return query
    
    # Extract the most relevant keywords from the context
    context_keywords = extract_search_keywords(context)
    
    # Filter out keywords that are already in the query
    query_lower = query.lower()
    new_keywords = [kw for kw in context_keywords if kw.lower() not in query_lower]
    
    # Add up to 3 most relevant keywords to the query
    if new_keywords:
        enhanced_query = f"{query} {' '.join(new_keywords[:3])}"
        return enhanced_query
    
    return query

def perform_round_aware_search(query: str, current_round_messages: List[Dict[str, Any]], 
                              previous_rounds_summary: Optional[str] = None, 
                              auto_detect: bool = True) -> Tuple[List[SearchResult], str]:
    """
    Perform a web search that is aware of conversation rounds.
    
    Args:
        query: The user's query
        current_round_messages: Messages from the current conversation round
        previous_rounds_summary: Optional summary of previous rounds
        auto_detect: Whether to automatically detect if search is needed
        
    Returns:
        Tuple of (search results, extracted content)
    """
    # Determine if search is needed
    if auto_detect:
        search_score = analyze_query_for_search_need(query, current_round_messages, previous_rounds_summary)
        logger.info(f"Search need score for query '{query}': {search_score}")
        
        if search_score < 0.5:  # Threshold can be adjusted
            logger.info(f"Skipping web search for query '{query}' (score: {search_score})")
            return [], ""
    
    # Get relevant context from conversation rounds
    context = get_relevant_context_from_rounds(query, current_round_messages, previous_rounds_summary)
    
    # Enhance the search query with context
    enhanced_query = enhance_search_query_with_context(query, context)
    logger.info(f"Enhanced search query: '{enhanced_query}' (original: '{query}')")
    
    # For debugging, check if web search is enabled
    logger.info(f"Search configuration: USE_SERP_API={settings.USE_SERP_API}, SERP_API_KEY={'configured' if settings.SERP_API_KEY else 'not configured'}")
    
    # Only skip if no search providers are configured
    if not settings.SERP_API_KEY and not settings.GOOGLE_SEARCH_API_KEY:
        logger.warning("No search API keys configured. Web search will be skipped.")
        return [], ""
    
    # Perform the search with the enhanced query
    try:
        # Skip search if query is too short or empty
        if len(enhanced_query.strip()) < 3:
            logger.warning(f"Query too short for search: '{enhanced_query}'")
            return [], ""
            
        logger.info(f"Starting search with query: '{enhanced_query}'")
        
        # Try basic search first as it's more reliable
        logger.info("Attempting basic search first")
        basic_results = basic_perform_web_search(enhanced_query)
        if basic_results:
            logger.info(f"Basic search returned {len(basic_results)} results")
            return basic_results, ""
            
        # Fall back to enhanced search if basic search fails
        logger.info("Basic search returned no results, trying enhanced search")
        results, content = enhanced_perform_web_search(enhanced_query)
        if results:
            logger.info(f"Enhanced search returned {len(results)} results")
            return results, content
            
        logger.warning(f"No search results found for query: '{enhanced_query}'")
        return [], ""
    except Exception as e:
        logger.error(f"Error performing round-aware search: {str(e)}")
        # Return empty results to avoid hanging
        return [], ""

def enrich_messages_with_round_aware_search(query: str, messages: List[Dict[str, Any]], 
                                          current_round_messages: List[Dict[str, Any]], 
                                          previous_rounds_summary: Optional[str] = None, 
                                          auto_detect: bool = True) -> List[Dict[str, Any]]:
    """
    Enrich messages with round-aware web search results.
    
    Args:
        query: The user's query
        messages: The current message list to enrich
        current_round_messages: Messages from the current conversation round
        previous_rounds_summary: Optional summary of previous rounds
        auto_detect: Whether to automatically detect if search is needed
        
    Returns:
        list: Updated message list with search results in context
    """
    # Perform round-aware search
    results, content = perform_round_aware_search(
        query, 
        current_round_messages, 
        previous_rounds_summary, 
        auto_detect
    )
    
    if not results and not content:
        return messages
    
    # Add search results as a system message
    if content:
        messages.append({
            "role": "system",
            "content": f"Web search results for '{query}':\n\n{content}"
        })
    elif results:
        # Format search results if we have results but no extracted content
        search_context = "Web search results:\n\n"
        for i, result in enumerate(results, 1):
            search_context += f"{i}. {result.title}\n"
            search_context += f"   URL: {result.url}\n"
            search_context += f"   {result.snippet}\n\n"
        
        messages.append({
            "role": "system",
            "content": f"{search_context}\nPlease use the above information to help answer the user's query. Cite sources when appropriate."
        })
    
    return messages
