"""
Sample functions for the function calling feature.
These functions are automatically registered with the function registry.
"""

import datetime
import json
import uuid
import traceback
from typing import Dict, Any, List, Optional, Set, Union
from utils.function_registry import register_function
from utils.enhanced_search import perform_multi_provider_search, extract_content_from_results, SearchResult, perform_gpt4o_search
from utils.round_aware_search import get_relevant_context_from_rounds
from utils.logging import logger
from utils.redis_client import redis_client
from utils.async_redis_client import async_redis
import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
import asyncio
from cachetools import TTLCache

from utils.ticker_db_functions import get_current_ticker

# Key prefix for storing search history
SEARCH_HISTORY_KEY_PREFIX = "search_history:"
# Key prefix for storing seen URLs
SEEN_URLS_KEY_PREFIX = "seen_urls:"

def compress_text(data, track_stats=True):
    """
    Compress text data by removing unnecessary whitespace and track compression statistics.

    Args:
        data: The data to compress (can be string, list, or dictionary)
        track_stats: Whether to track and log compression statistics

    Returns:
        The compressed data with the same structure but normalized whitespace
    """
    if data is None:
        return None

    # Initialize counters for statistics
    original_chars = 0
    compressed_chars = 0
    original_words = 0
    compressed_words = 0

    # Function to process a single string
    def process_string(text):
        nonlocal original_chars, compressed_chars, original_words, compressed_words

        if not isinstance(text, str):
            return text

        # Count original stats
        if track_stats:
            original_chars += len(text)
            original_words += len(text.split())

        # Compress by normalizing whitespace
        compressed = ' '.join(text.split())

        # Count compressed stats
        if track_stats:
            compressed_chars += len(compressed)
            compressed_words += len(compressed.split())

        return compressed

    # Process different data types
    if isinstance(data, str):
        result = process_string(data)
    elif isinstance(data, list):
        result = [compress_text(item, track_stats=False) for item in data]
    elif isinstance(data, dict):
        result = {key: compress_text(value, track_stats=False) for key, value in data.items()}
    else:
        # Return other data types unchanged
        return data

    # Log compression statistics
    if track_stats and original_chars > 0:
        chars_saved = original_chars - compressed_chars
        char_percentage = (chars_saved / original_chars) * 100 if original_chars > 0 else 0

        words_saved = original_words - compressed_words
        word_percentage = (words_saved / original_words) * 100 if original_words > 0 else 0

        logger.info(f"Text compression statistics:")
        logger.info(f"  Original size: {original_chars} chars, {original_words} words")
        logger.info(f"  Compressed size: {compressed_chars} chars, {compressed_words} words")
        logger.info(f"  Saved: {chars_saved} chars ({char_percentage:.2f}%), {words_saved} words ({word_percentage:.2f}%)")

    return result

@register_function(
    description="Search for information on a topic using web search",
    parameter_descriptions={
        "query": "The search query",
        "current_date": "The current date in YYYY-MM-DD format to help determine the latest timeframe.",
    }
)
async def search(
    query: str,
    current_date: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Search for information on a topic using web search with conversation context awareness.

    Args:
        query: The search query
        current_date: The current date in YYYY-MM-DD format to help determine the latest timeframe

    Returns:
        dict: Search results
    """
    logger.debug(f"Function call: search(query='{query}', current_date={current_date})")

    try:
        # Generate a unique search ID
        search_id = str(uuid.uuid4())
        logger.debug(f"Generated search ID: {search_id}")

        # Enhanced query with conversation context if session_id is provided
        enhanced_query = query

        # Use current_date if provided to enhance search
        if current_date:
            logger.info(f"Using current date: {current_date} to enhance search")
            try:
                # Validate date format (YYYY-MM-DD)
                import re
                if re.match(r'^\d{4}-\d{2}-\d{2}$', current_date):
                    # Add date context to search metadata
                    logger.debug(f"Adding date context to search: {current_date}")

                    # Could add date-specific logic here, such as adding date constraints to the query
                    # For example, adding "after:[date]" for certain search engines
                    # This implementation just logs the date for now
                else:
                    logger.warning(f"Invalid date format: {current_date}, expected YYYY-MM-DD")
            except Exception as e:
                logger.warning(f"Error processing current_date parameter: {str(e)}")

        # Determine whether to use GPT-4o search
        from config.settings import settings
        # If not explicitly specified, use the setting from config
        use_gpt4o_search = settings.USE_GPT4O_SEARCH
        logger.info(f"Using GPT-4o search setting from config: {use_gpt4o_search}")

        # Perform search using the appropriate method
        logger.info(f"Performing GPT-4o search with query: '{enhanced_query}'")
        result = await perform_gpt4o_search(enhanced_query)

        # Compress the result to remove unnecessary whitespace
        logger.info("Compressing search results to remove unnecessary whitespace")
        result = compress_text(result)
        logger.info(f"Search returned {len(result)} characters")

        return {
            "search_id": search_id,
            "query": query,
            "enhanced_query": enhanced_query if enhanced_query != query else None,
            "content": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error in search function: {str(e)}")
        logger.debug(f"Search function error traceback: {traceback.format_exc()}")
        return {
            "query": query,
            "error": str(e),
            "results": [],
            "timestamp": datetime.now().isoformat()
        }

@register_function(
    description="Refine a previous search with additional criteria",
    parameter_descriptions={
        "search_id": "The ID of a previous search to refine",
        "additional_terms": "Additional search terms to add to the original query",
        "exclude_terms": "Terms to exclude from the search",
        "num_results": "The number of results to return per search engine (max 10)",
        "include_content": "Whether to include full content extraction from web pages",
        "session_id": "Session ID for conversation context (should match the original search session)",
        "content_extraction_level": "Level of content extraction: 'metadata' (minimal), 'summary' (default), or 'full'"
        # Note: current_date is automatically added internally using the current date
    }
)
async def refine_search(
    search_id: str,
    additional_terms: Optional[str] = None,
    exclude_terms: Optional[str] = None,
    num_results: int = 5,
    include_content: bool = False,
    session_id: Optional[str] = None,
    content_extraction_level: str = "summary"
) -> Dict[str, Any]:
    """
    Refine a previous search with additional criteria.

    Args:
        search_id: The ID of a previous search to refine
        additional_terms: Additional search terms to add to the original query
        exclude_terms: Terms to exclude from the search
        num_results: Number of results to return per search engine (max 10)
        include_content: Whether to include full content extraction from web pages
        session_id: Session ID for conversation context (should match the original search session)

    Returns:
        dict: Refined search results

    Note:
        This function automatically uses the current date in YYYY-MM-DD format
        to help determine the latest timeframe for search results.
    """
    logger.info(f"Function call: refine_search(search_id='{search_id}', additional_terms='{additional_terms}', exclude_terms='{exclude_terms}', num_results={num_results}, include_content={include_content}, session_id={session_id})")

    try:
        if not session_id:
            logger.error("Session ID is required for refining searches")
            return {
                "error": "Session ID is required for refining searches",
                "results": [],
                "timestamp": datetime.now().isoformat()
            }

        # Find the original search in the search history
        logger.info(f"Looking for original search with ID {search_id} in session {session_id}")
        original_query = None
        search_history_key = f"{SEARCH_HISTORY_KEY_PREFIX}{session_id}"

        search_history = await async_redis.lrange(search_history_key, 0, -1) or []
        logger.debug(f"Retrieved {len(search_history)} search history entries")

        for entry in search_history:
            entry_data = json.loads(entry)
            if entry_data.get("id") == search_id:
                original_query = entry_data.get("query")
                logger.info(f"Found original search: '{original_query}'")
                break

        if not original_query:
            logger.error(f"Could not find original search with ID {search_id}")
            return {
                "error": f"Could not find original search with ID {search_id}",
                "results": [],
                "timestamp": datetime.now().isoformat()
            }

        # Build the refined query
        refined_query = original_query
        logger.info(f"Building refined query from original: '{original_query}'")

        if additional_terms:
            refined_query = f"{refined_query} {additional_terms}"
            logger.debug(f"Added additional terms: '{additional_terms}'")

        if exclude_terms:
            # Add exclusion operators for each term
            exclude_parts = " ".join([f"-{term.strip()}" for term in exclude_terms.split()])
            refined_query = f"{refined_query} {exclude_parts}"
            logger.debug(f"Added exclusion terms: '{exclude_parts}'")

        logger.info(f"Final refined query: '{refined_query}'")

        # Perform the refined search
        logger.info(f"Performing refined search with query: '{refined_query}'")

        # Get current date in YYYY-MM-DD format
        current_date = datetime.now().strftime("%Y-%m-%d")
        logger.debug(f"Using current date for refined search: {current_date}")

        return await search(
            query=refined_query,
            num_results=num_results,
            include_content=include_content,
            session_id=session_id,
            exclude_seen=False,  # Don't exclude seen URLs for refined searches
            current_date=current_date,  # Pass current date to search function
            content_extraction_level=content_extraction_level  # Pass content extraction level
        )
    except Exception as e:
        logger.error(f"Error in refine_search function: {str(e)}")
        logger.debug(f"Refine search error traceback: {traceback.format_exc()}")
        return {
            "error": str(e),
            "results": [],
            "timestamp": datetime.now().isoformat()
        }

@register_function(
    description="Get search history for the current conversation",
    parameter_descriptions={
        "session_id": "Session ID for the conversation",
        "limit": "Maximum number of search history entries to return"
    }
)
async def get_search_history(
    session_id: str,
    limit: int = 5
) -> Dict[str, Any]:
    """
    Get search history for the current conversation.

    Args:
        session_id: Session ID for the conversation
        limit: Maximum number of search history entries to return

    Returns:
        dict: Search history entries
    """
    logger.info(f"Function call: get_search_history(session_id='{session_id}', limit={limit})")

    try:
        if not session_id:
            logger.error("Session ID is required for getting search history")
            return {
                "error": "Session ID is required",
                "history": [],
                "timestamp": datetime.now().isoformat()
            }

        search_history_key = f"{SEARCH_HISTORY_KEY_PREFIX}{session_id}"
        logger.info(f"Retrieving search history for session {session_id}")

        search_history = await async_redis.lrange(search_history_key, 0, -1) or []
        logger.info(f"Retrieved {len(search_history)} search history entries")

        # Parse and format the search history
        history_entries = []
        recent_entries = search_history[-limit:] if limit < len(search_history) else search_history
        logger.debug(f"Processing {len(recent_entries)} most recent entries (limit: {limit})")

        for entry in recent_entries:  # Get the most recent entries
            entry_data = json.loads(entry)
            history_entries.append({
                "id": entry_data.get("id"),
                "query": entry_data.get("query"),
                "enhanced_query": entry_data.get("enhanced_query"),
                "timestamp": entry_data.get("timestamp"),
                "num_results": entry_data.get("num_results")
            })
            logger.debug(f"Processed history entry: {entry_data.get('id')} - '{entry_data.get('query')}'")

        logger.info(f"Returning {len(history_entries)} search history entries")
        return {
            "session_id": session_id,
            "history": history_entries,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error in get_search_history function: {str(e)}")
        logger.debug(f"Get search history error traceback: {traceback.format_exc()}")
        return {
            "error": str(e),
            "history": [],
            "timestamp": datetime.now().isoformat()
        }

# Enhanced caching for S&P 500 data
# Cache for S&P 500 tickers and sectors (7 day TTL since this rarely changes)
SP500_TICKERS_CACHE = TTLCache(maxsize=5, ttl=7*86400)  # 7 days

# Cache for S&P 500 stock data (3 day TTL)
SP500_DATA_CACHE = TTLCache(maxsize=20, ttl=3*86400)  # 3 days

# Cache for sector-specific data (2 day TTL)
SP500_SECTOR_CACHE = TTLCache(maxsize=20, ttl=2*86400)  # 2 days

# Cache version to handle schema changes
SP500_CACHE_VERSION = "v1.2"  # Updated for database version

# Redis cache keys for persistent caching
SP500_REDIS_KEY = "sp500_data_cache"
SP500_REDIS_SECTOR_PREFIX = "sp500_sector_"

@register_function(
    description="Get dividend information for stocks filtered by index type",
    parameter_descriptions={
        "limit": "Maximum number of stocks to return (default: all 500)",
        "min_dividend_yield": "Filter for stocks with minimum dividend yield percentage (e.g., 1.5)",
        "min_dividend_rate": "Filter for stocks with minimum dividend rate in dollars per share (e.g., 2.0)",
        "sort_by": "Sort results by: 'yield' (highest yield first), 'name' (alphabetical), or 'sector'",
        "sector": "Filter by sector (e.g., 'Technology', 'Healthcare', etc.)",
        "indice": "Filter tickers according to type of indices (e.g., 'SPX' for S&P 500, 'IXIC' for Nasdaq Composite)"
    }
)
async def get_tickers_dividend(
    limit: int = 500,
    min_dividend_yield: float = 0,
    min_dividend_rate: float = 0,
    sort_by: str = "yield",
    sector: Optional[str] = None,
    indice: Optional[str] = "SPX"
) -> Dict[str, Any]:
    """
    Get dividend information for stocks filtered by index type, considering both dividend rate and yield.

    Args:
        limit: Maximum number of stocks to return (default: all 500)
        min_dividend_yield: Filter for stocks with minimum dividend yield percentage
        min_dividend_rate: Filter for stocks with minimum dividend rate in dollars per share
        sort_by: Sort results by 'yield', 'name', or 'sector'
        sector: Filter by sector (e.g., 'Technology', 'Healthcare', etc.)
        indice: Filter tickers according to type of indices (e.g., 'SPX' for S&P 500, 'IXIC' for Nasdaq Composite)

    Returns:
        dict: Dividend data for stocks matching the specified criteria
    """
    logger.info(f"Function call: get_tickers_dividend(limit={limit}, min_dividend_yield={min_dividend_yield}, min_dividend_rate={min_dividend_rate}, sort_by='{sort_by}', sector={sector}, indice='{indice}')")

    # Import the database-backed function
    from utils.sp500_db_functions import get_tickers_dividend_from_db

    # Call the database-backed function
    return await get_tickers_dividend_from_db(
        limit=limit,
        min_dividend_yield=min_dividend_yield,
        min_dividend_rate=min_dividend_rate,
        sort_by=sort_by,
        sector=sector,
        indice=indice
    )

@register_function(
    parameter_schema={
        "name": "query_stocks",
        "description": "Query stocks by multiple conditions with flexible comparison operators. It can be used when we need to filter stocks according to some conditions",
        "parameters": {
            "type": "object",
            "properties": {
                "conditions": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string", "enum": ["market", "market_cap", "PE", "EPS", "dividend_yield", "sector", "ticker"]},
                            "expr": {"type": "string", "enum": ["gt", "gte", "lt", "lte", "eq"]},
                            "val": {"type": "string"}
                        },
                        "required": ["name", "expr", "val"]
                    },
                    "description": "List of conditions, each containing a field name, comparison operator, and value."
                },
                "limit": {
                    "type": "integer",
                    "default": 100,
                    "description": "Maximum number of stocks to return."
                }
            },
            "required": ["conditions"]
        }
    }
)
async def query_stocks(
    conditions: List[Dict[str, Any]],
    limit: int = 100,
) -> Dict[str, Any]:
    """
    Query stocks using multiple conditions with flexible comparison operators.

    Args:
        conditions: List of dictionaries, each containing:
            - name: Field name to compare (e.g., market_cap, pe_ratio)
            - expr: Comparison operator (gt, gte, lt, lte, eq)
            - val: Value to compare against
        limit: Maximum number of stocks to return

    Returns:
        dict: Matching stocks with their data
    """
    market_value = "United States"
    ticker_value = None
    for condition in conditions:
        if condition['name'] == 'market':
            market_value = condition['val']
        if condition['name'] == 'ticker':
            ticker_value = condition['val']
    ticker_value = await get_current_ticker(ticker_value, market_value)
    for condition in conditions:
        if condition['name'] == 'ticker':
            condition['val'] = ticker_value

    logger.info(f"Function call: query_stocks(conditions={conditions}, limit={limit}")
    limit = int(limit)
    try:
        # Import the database-backed function
        from utils.stocks_db_functions import query_stocks_from_db

        # Validate conditions format
        for condition in conditions:
            if not all(k in condition for k in ["name", "expr", "val"]):
                raise ValueError("Each condition must have 'name', 'expr', and 'val' fields")
            if condition["expr"] not in ["gt", "gte", "lt", "lte", "eq"]:
                raise ValueError("Expression must be one of: gt, gte, lt, lte, eq")

        # Call database function with validated parameters
        results = await query_stocks_from_db(
            conditions=conditions,
            limit=min(max(1, limit), 500),  # Limit between 1 and 500
        )

        logger.info(f"Query returned {len(results.get('stocks', []))} results")
        return results

    except Exception as e:
        logger.error(f"Error in query_stocks function: {str(e)}")
        logger.info(f"Query stocks error traceback: {traceback.format_exc()}")
        return {
            "error": str(e),
            "stocks": [],
            "timestamp": datetime.now().isoformat()
        }

@register_function(
    parameter_schema={
        "name": "query_ticker_concepts",
        "description": "Query a ticker's historical concept data in terms of Income statement, balance sheet, cashflow, dividend and other important metrics. Currently supports US market companies with SEC filings.",
        "parameters": {
            "type": "object",
            "properties": {
                "ticker": {
                    "type": "string",
                    "description": "The stock ticker symbol (e.g., 'AAPL', 'MSFT')"
                },
                "market": {
                    "type": "string",
                    "enum": ["United States", "Japan", "South Korea", "Taiwan", "Hong Kong"],
                    "description": "Market to query (currently only United States is supported for SEC concept data)",
                    "default": "United States"
                }
            },
            "required": ["ticker"]
        }
    }
)
async def query_ticker_concepts(
    ticker: str,
    market: Optional[str] = "United States",
    *args, **kwargs
) -> Dict[str, Any]:
    """
    Query historical concept data for a specific ticker from SEC filings.

    Args:
        ticker: The stock ticker symbol (e.g., 'AAPL', 'MSFT')
        market: Market to query (currently only United States is supported for SEC concept data)

    Returns:
        dict: Historical concept data for the specified ticker
    """
    ticker = await get_current_ticker(ticker, market)
    logger.info(f"Function call: query_ticker_concepts(ticker='{ticker}', market='{market}')")

    try:
        # Validate inputs
        if not ticker:
            raise ValueError("Ticker symbol is required")

        ticker = ticker.upper().strip()

        # Validate market parameter
        if market and market not in ["United States", "Japan", "South Korea", "Taiwan", "Hong Kong"]:
            raise ValueError("Market must be one of: United States, Japan, South Korea, Taiwan, Hong Kong")

        # Call the database function to get the ticker concept data
        from utils.sec_db_functions import query_ticker_concepts_from_db2
        return await query_ticker_concepts_from_db2(
            market=market,
            ticker=ticker,
        )

    except Exception as e:
        logger.error(f"Error in query_ticker_concepts function: {str(e)}")
        logger.debug(f"Query ticker concepts error traceback: {traceback.format_exc()}")
        return {
            "error": str(e),
            "ticker": ticker if 'ticker' in locals() else None,
            "data": {},
            "timestamp": datetime.now().isoformat()
        }

@register_function(
    parameter_schema={
        "name": "query_sec_filings",
        "description": '''
            Query SEC filings for a specific ticker symbol, it can be used to get earning report(10-K or 10-Q) for specific time or latest time per one company,
            Then we can further fetch filing extraction based on this filing details
        ''',
        "parameters": {
            "type": "object",
            "properties": {
                "ticker": {
                    "type": "string",
                    "description": "The stock ticker symbol (e.g., 'AAPL', 'MSFT')"
                },
                "market": {
                    "type": "string",
                    "enum": ["United States", "Japan", "South Korea", "Taiwan", "Hong Kong"],
                    "description": "Market to query (currently only United States is supported for SEC concept data)",
                    "default": "United States"
                },
                "filing_type": {
                    "type": "string",
                    "description": "Type of SEC filing to query (e.g., '10-K', '10-Q', '8-K')",
                    "default": "all"
                },
                "start_date": {
                    "type": "string",
                    "description": "Start date for filings in YYYY-MM-DD format",
                    "default": ""
                },
                "end_date": {
                    "type": "string",
                    "description": "End date for filings in YYYY-MM-DD format (defaults to current date if empty)",
                    "default": ""
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of filings to return",
                    "default": 10
                }
            },
            "required": ["ticker"]
        }
    }
)
async def query_sec_filings(
    ticker: str,
    market: Optional[str] = "United States",
    filing_type: str = "",
    start_date: str = "",
    end_date: str = "",
    limit: int = 10
) -> Dict[str, Any]:
    """
    Query SEC filings for a specific company by ticker symbol.

    Args:
        ticker: The stock ticker symbol (e.g., 'AAPL', 'MSFT')
        filing_type: Type of SEC filing to query (e.g., '10-K', '10-Q', '8-K', '' for all types)
        start_date: Start date for filings in YYYY-MM-DD format (empty for no start limit)
        end_date: End date for filings in YYYY-MM-DD format (empty for current date)
        limit: Maximum number of filings to return

    Returns:
        dict: SEC filing data for the specified ticker
    """
    ticker = await get_current_ticker(ticker, market)
    logger.info(f"Function call: query_sec_filings(ticker='{ticker}', filing_type='{filing_type}', start_date='{start_date}', end_date='{end_date}', limit={limit})")

    try:
        # Validate inputs
        if not ticker:
            raise ValueError("Ticker symbol is required")

        ticker = ticker.upper().strip()

        # Validate dates if provided
        if start_date:
            try:
                datetime.strptime(start_date, "%Y-%m-%d")
            except ValueError:
                raise ValueError("start_date must be in YYYY-MM-DD format")

        if end_date:
            try:
                datetime.strptime(end_date, "%Y-%m-%d")
            except ValueError:
                raise ValueError("end_date must be in YYYY-MM-DD format")

        # Use current date if end_date not specified
        if not end_date:
            end_date = datetime.now().strftime("%Y-%m-%d")
            logger.debug(f"Using current date as end_date: {end_date}")

        # Call the database function to get SEC filings
        from utils.sec_db_functions import query_sec_filings_from_db
        return await query_sec_filings_from_db(
            ticker=ticker,
            market=market,
            filing_type=filing_type,
            start_date=start_date,
            end_date=end_date,
            limit=min(limit, 100)  # Limit to maximum 100 results
        )

    except Exception as e:
        logger.error(f"Error in query_sec_filings function: {str(e)}")
        logger.debug(f"Query SEC filings error traceback: {traceback.format_exc()}")
        return {
            "error": str(e),
            "ticker": ticker if 'ticker' in locals() else None,
            "filings": [],
            "timestamp": datetime.now().isoformat()
        }

@register_function(
    parameter_schema={
        "name": "query_sec_filing_sections",
        "description": '''Query SEC filing section details for a specific ticker, filing type, and fiscal year.
        The section includes:
        Risk Factors, Management's Discussion and Analysis, Business Description, Financial Statements, Legal Proceedings and Properties.
        These section can be used to analyse the company's earning report, relevant filings and used to evaluate company fundamentals.
        ''',
        "parameters": {
            "type": "object",
            "properties": {
                "ticker": {
                    "type": "string",
                    "description": "The stock ticker symbol (e.g., 'AAPL', 'MSFT')"
                },
                "market": {
                    "type": "string",
                    "enum": ["United States", "Japan", "South Korea", "Taiwan", "Hong Kong"],
                    "description": "Market to query (currently only United States is supported for SEC concept data)",
                    "default": "United States"
                },
                "filing_type": {
                    "type": "string",
                    "description": "Type of SEC filing to query (e.g., '10-K', '10-Q')",
                    "default": "10-K"
                },
                "fiscal_year": {
                    "type": "string",
                    "description": "Fiscal year for the filing in YYYY format",
                },
                "section_types": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Optional list of specific section types to retrieve (e.g., ['MD&A', 'Risk Factors']). Empty means all sections.",
                    "default": []
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of sections to return",
                    "default": 20
                }
            },
            "required": ["ticker", "fiscal_year"]
        }
    }
)
async def query_sec_filing_sections(
    ticker: str,
    fiscal_year: str,
    market: Optional[str] = "United States",
    filing_type: str = "10-K",
    section_types: List[str] = None,
    limit: int = 20
) -> Dict[str, Any]:
    """
    Query SEC filing section details for a specific company by ticker, filing type and fiscal year.

    Args:
        ticker: The stock ticker symbol (e.g., 'AAPL', 'MSFT')
        fiscal_year: Fiscal year for the filing in YYYY format
        filing_type: Type of SEC filing to query (e.g., '10-K', '10-Q')
        section_types: Optional list of specific section types to retrieve (e.g., ['MD&A', 'Risk Factors'])
        limit: Maximum number of sections to return

    Returns:
        dict: SEC filing section details for the specified filing
    """
    ticker = await get_current_ticker(ticker, market)
    logger.info(f"Function call: query_sec_filing_sections(ticker='{ticker}', fiscal_year='{fiscal_year}', filing_type='{filing_type}', section_types={section_types}, limit={limit})")

    try:
        # Validate inputs
        if not ticker:
            raise ValueError("Ticker symbol is required")

        ticker = ticker.upper().strip()

        # Validate fiscal_year
        if not fiscal_year or not fiscal_year.isdigit() or len(fiscal_year) != 4:
            raise ValueError("fiscal_year must be in YYYY format")

        # Normalize section_types to list
        if section_types is None:
            section_types = []

        # Import the database function to query SEC filing sections
        from utils.sec_db_functions import query_sec_filing_sections_from_db

        return await query_sec_filing_sections_from_db(
            ticker=ticker,
            fiscal_year=fiscal_year,
            filing_type=filing_type,
            section_types=section_types,
            limit=min(limit, 100)  # Limit to maximum 100 results
        )

    except Exception as e:
        logger.error(f"Error in query_sec_filing_sections function: {str(e)}")
        logger.debug(f"Query SEC filing sections error traceback: {traceback.format_exc()}")
        return {
            "error": str(e),
            "ticker": ticker if 'ticker' in locals() else None,
            "filing_type": filing_type if 'filing_type' in locals() else None,
            "fiscal_year": fiscal_year if 'fiscal_year' in locals() else None,
            "sections": [],
            "timestamp": datetime.now().isoformat()
        }


@register_function(
    parameter_schema={
        "name": "query_stock_price_ratings",
        "description": "Query stock price and analyst ratings information when analysing one stock.",
        "parameters": {
            "type": "object",
            "properties": {
                "ticker": {
                    "type": "string",
                    "description": "The stock ticker symbol (e.g., 'AAPL', 'MSFT')",
                },
                "market": {
                    "type": "string",
                    "enum": ["United States", "Japan", "South Korea", "Taiwan", "Hong Kong"],
                    "description": "Market to query",
                },
                "limit": {
                    "type": "integer",
                    "default": 100,
                    "description": "Maximum number of stocks to return."
                }
            },
        }
    },
    parameter_descriptions={
        "response_fields": {
            "symbol": "The stock ticker symbol (e.g., 'AAPL', 'MSFT')",
            "company_name": "The full name of the company",
            "sector": "The industry sector the company belongs to (e.g., 'Technology', 'Healthcare')",
            "current_price": "The current trading price of the stock in USD",
            "target_low_price": "The lowest price target set by analysts in USD",
            "target_high_price": "The highest price target set by analysts in USD",
            "target_mean_price": "The average (mean) price target set by analysts in USD",
            "number_of_analyst_opinions": "The total number of analysts covering the stock",
            "stock_recommendation": "The textual representation of analyst consensus, value can be: 'Strong Buy', 'Buy', 'Hold', 'Sell', 'Strong Sell')",
            "technical_sentiment": "'Bullish' or 'Bearish' according to MA20, RSI and MACD",
            "valuation": "Value can be: 'Deeply Undervalued', 'Undervalued', 'Fairly Valued', 'Overvalued', 'Highly Overvalued'",
            "last_updated": "The timestamp when this data was last updated"
        }
    }
)
async def query_stock_price_ratings(
    market: Optional[str] = "United States",
    ticker: Optional[str] = None,
    limit: int = 10
) -> Dict[str, Any]:
    """
    Query stock price and analyst ratings information from the database.

    Args:
        ticker: Optional ticker symbol to filter by (if not provided, returns data for multiple stocks)
        limit: Maximum number of stocks to return if ticker is not specified

    Returns:
        dict: Stock price and ratings data
    """
    ticker = await get_current_ticker(ticker, market)
    logger.info(f"Function call: query_stock_price_ratings(ticker='{ticker}', limit={limit})")

    try:
        # Convert limit to integer if it's a string (defensive programming)
        if isinstance(limit, str):
            limit = int(limit)

        # Import the database-backed function
        from utils.stocks_db_functions import query_stock_price_ratings_from_db

        # Call the database function
        return await query_stock_price_ratings_from_db(
            market=market,
            ticker=ticker,
            limit=min(max(1, limit), 100)  # Limit between 1 and 100
        )

    except Exception as e:
        logger.error(f"Error in query_stock_price_ratings function: {str(e)}")
        logger.debug(f"Query stock price ratings error traceback: {traceback.format_exc()}")
        return {
            "error": str(e),
            "stocks": [],
            "timestamp": datetime.now().isoformat()
        }
