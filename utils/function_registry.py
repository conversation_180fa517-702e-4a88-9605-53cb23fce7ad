import inspect
import json
import traceback
from typing import Dict, Any, List, Callable, Optional, Union
from utils.logging import logger

class FunctionRegistry:
    """
    Registry for functions that can be called by the model.
    """
    
    def __init__(self):
        """
        Initialize the function registry.
        """
        self.functions = {}
        self.function_schemas = {}
        self.function_metadata = {}
    
    def register_function(self, func: Callable, description: str = None, parameter_descriptions: Dict[str, Dict] = None, parameter_schema=None):
        """
        Register a function with the registry.
        
        Args:
            func: The function to register
            description: Optional description of the function
            parameter_descriptions: Optional descriptions of the function parameters
        """
        # Get function name
        func_name = func.__name__
        
        logger.info(f"Registering function: {func_name}")
        
        # Get function signature
        sig = inspect.signature(func)
        
        # Get function docstring
        doc = inspect.getdoc(func) or ""
        
        # Use provided description or docstring
        description = description or doc
        
        # Build parameter schema
        parameters = {}
        required_params = []
        
        logger.debug(f"Function {func_name} signature: {sig}")
        
        for param_name, param in sig.parameters.items():
            # Skip self parameter for methods
            if param_name == 'self':
                continue
            
            # Get parameter type annotation
            param_type = param.annotation
            
            # Default to string if no type annotation
            if param_type is inspect.Parameter.empty:
                param_type = str
                logger.debug(f"Function {func_name}: Parameter {param_name} has no type annotation, defaulting to string")
            else:
                logger.debug(f"Function {func_name}: Parameter {param_name} has type {param_type.__name__}")
            
            # Get parameter description
            param_desc = parameter_descriptions.get(param_name, "") if parameter_descriptions else ""
            
            # Build parameter schema
            logger.debug(f"param type: ${param_type}")
            json_schema_type = self._get_json_schema_type(param_type)
            
            if isinstance(json_schema_type, dict):
                # If the type is a dictionary (e.g., for arrays with items), merge it with the parameter schema
                param_schema = json_schema_type
                param_schema["description"] = param_desc
            else:
                # If the type is a string, use it as the "type" field
                param_schema = {
                    "type": json_schema_type,
                    "description": param_desc
                }
            
            logger.debug(f"para_schema: ${param_schema}")
            
            # Add parameter to schema
            parameters[param_name] = param_schema
            
            # Check if parameter is required
            if param.default is inspect.Parameter.empty:
                required_params.append(param_name)
                logger.debug(f"Function {func_name}: Parameter {param_name} is required")
            else:
                logger.debug(f"Function {func_name}: Parameter {param_name} has default value: {param.default}")
        
        # Build function schema
        schema = {
            "name": func_name,
            "description": description,
            "parameters": {
                "type": "object",
                "properties": parameters,
                "required": required_params
            }
        }
        if parameter_schema:
            schema = parameter_schema
        
        # Store function and schema
        self.functions[func_name] = func
        self.function_schemas[func_name] = schema
        self.function_metadata[func_name] = {
            "description": description,
            "parameter_descriptions": parameter_descriptions or {}
        }
        
        # Add openai_schema attribute to the function for direct use with OpenAI Assistant API
        func.openai_schema = schema
        
        logger.info(f"Successfully registered function: {func_name} with {len(parameters)} parameters ({len(required_params)} required)")
        logger.debug(f"Function schema for {func_name}: {json.dumps(schema, indent=2)}")
        
        return func
    
    def _get_json_schema_type(self, python_type):
        """
        Convert Python type to JSON schema type.
        
        Args:
            python_type: The Python type
            
        Returns:
            str or dict: The JSON schema type definition
        """
        logger.debug(f"python type: ${python_type}")
        type_map = {
            str: "string",
            int: "integer",
            float: "number",
            bool: "boolean",
            list: "array",
            dict: "object",
            None: "null"
        }
        
        # Handle nested generic types like Optional[List[str]]
        def get_base_type_and_args(typ):
            if hasattr(typ, "__origin__"):
                # For Union/Optional types (e.g., Optional[List[str]])
                if typ.__origin__ is Union:
                    # Check each type in the union
                    for arg in typ.__args__:
                        if arg is not type(None):  # Skip NoneType in Optional
                            return get_base_type_and_args(arg)
                # For container types (e.g., List[str], Dict[str, int])
                return typ.__origin__, getattr(typ, "__args__", None)
            return typ, None
        
        # Get the base type and type arguments, handling nested generics
        base_type, type_args = get_base_type_and_args(python_type)
        
        # For string representations like 'list[str]' that might come from annotations
        if isinstance(python_type, str):
            if "Optional" in python_type or "Union" in python_type:
                # Extract the inner type from Optional/Union
                if "List[" in python_type or "list[" in python_type:
                    # Try to extract the item type from the string representation
                    item_type = "string"  # Default to string if we can't determine
                    return {"type": "array", "items": {"type": item_type}}
                elif "Dict[" in python_type or "dict[" in python_type:
                    return "object"
            elif python_type.startswith("list[") or python_type.startswith("List["):
                # Try to extract the item type from the string representation
                item_type = "string"  # Default to string if we can't determine
                return {"type": "array", "items": {"type": item_type}}
            elif python_type.startswith("dict[") or python_type.startswith("Dict["):
                return "object"
        
        # Handle array types with type arguments
        if base_type is list or base_type == list:
            item_type = "string"  # Default to string if we can't determine
            if type_args and len(type_args) > 0:
                # Get the type of the first type argument (the item type)
                item_schema_type = self._get_json_schema_type(type_args[0])
                if isinstance(item_schema_type, dict):
                    # If the item type is already a schema dict, use it directly
                    return {"type": "array", "items": item_schema_type}
                else:
                    # Otherwise, create a schema dict for the item type
                    item_type = item_schema_type
            return {"type": "array", "items": {"type": item_type}}
        
        # For other types, return the simple type string
        return type_map.get(base_type, "string")
    
    def get_function(self, name: str) -> Optional[Callable]:
        """
        Get a function by name.
        
        Args:
            name: The function name
            
        Returns:
            The function or None if not found
        """
        return self.functions.get(name)
    
    def has_function(self, name: str) -> bool:
        """
        Check if a function exists in the registry.
        
        Args:
            name: The function name
            
        Returns:
            bool: True if the function exists, False otherwise
        """
        return name in self.functions
    
    def get_function_schema(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get the schema for a function.
        
        Args:
            name: The function name
            
        Returns:
            The function schema or None if not found
        """
        return self.function_schemas.get(name)
    
    def get_function_schemas(self) -> List[Dict[str, Any]]:
        """
        Get all function schemas.
        
        Returns:
            List of function schemas
        """
        return list(self.function_schemas.values())
    
    def get_function_names(self) -> List[str]:
        """
        Get all function names.
        
        Returns:
            List of function names
        """
        return list(self.functions.keys())
    
    async def execute_function(self, name: str, **kwargs) -> Any:
        """
        Execute a function by name.
        
        Args:
            name: The function name
            **kwargs: The function arguments
            
        Returns:
            The function result
            
        Raises:
            ValueError: If the function is not found
        """
        logger.info(f"Executing function: {name} with arguments: {json.dumps(kwargs, default=str)}")
        
        func = self.get_function(name)
        
        if func is None:
            error_msg = f"Function {name} not found"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        # Log the function signature
        sig = inspect.signature(func)
        logger.debug(f"Function {name} signature: {sig}")
        
        # Validate arguments against function signature
        try:
            # This will raise TypeError if the arguments don't match the signature
            sig.bind(**kwargs)
            logger.debug(f"Arguments for {name} validated successfully against signature")
        except TypeError as e:
            logger.error(f"Argument validation error for function {name}: {str(e)}")
            # Continue execution anyway, as the error might be due to extra parameters
            # that will be ignored by Python's **kwargs mechanism
        
        try:
            # Check if function is async
            if inspect.iscoroutinefunction(func):
                logger.debug(f"Executing async function: {name}")
                result = await func(**kwargs)
            else:
                logger.debug(f"Executing sync function: {name}")
                result = func(**kwargs)
            
            # Log the result (truncate if too large)
            result_str = str(result)
            if len(result_str) > 1000:
                result_str = result_str[:1000] + "... [truncated]"
            logger.info(f"Function {name} executed successfully. Result: {result_str}")
            
            return result
        except Exception as e:
            error_msg = f"Error executing function {name}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Function execution traceback: {traceback.format_exc()}")
            raise

# Create a global function registry
function_registry = FunctionRegistry()

def register_function(description: str = None, parameter_descriptions: Dict[str, Dict] = None, parameter_schema=None):
    """
    Decorator to register a function with the registry.
    
    Args:
        description: Optional description of the function
        parameter_descriptions: Optional descriptions of the function parameters
        
    Returns:
        The decorated function
    """
    def decorator(func):
        return function_registry.register_function(func, description, parameter_descriptions, parameter_schema)
    return decorator
