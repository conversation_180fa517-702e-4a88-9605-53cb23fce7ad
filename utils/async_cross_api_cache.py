"""
Async cross-API cache for caching responses across different API providers.

This module provides a caching mechanism for responses from different API providers,
allowing for efficient reuse of responses and reduced API calls.
"""

import json
import time
import hashlib
from typing import Dict, Any, Optional, List, Union
from utils.async_redis_client import async_redis
from utils.logging import logger
from config.settings import settings

class AsyncCrossAPICache:
    """
    Async cache for cross-API responses.
    
    This class provides a caching mechanism for responses from different API providers,
    allowing for efficient reuse of responses and reduced API calls.
    """
    
    def __init__(self, namespace: str = "cross_api_cache", ttl: int = 3600):
        """
        Initialize the cross-API cache.
        
        Args:
            namespace: The namespace for this cache instance
            ttl: Default time-to-live for cache entries in seconds
        """
        self.namespace = namespace
        self.ttl = ttl
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'errors': 0
        }
    
    def _get_cache_key(self, key: str) -> str:
        """
        Get the full cache key with namespace.
        
        Args:
            key: The base key
            
        Returns:
            The full cache key with namespace
        """
        return f"{self.namespace}:{key}"
    
    async def get(self, key: str) -> Optional[str]:
        """
        Get a value from the cache.
        
        Args:
            key: The key to get
            
        Returns:
            The cached value, or None if not found
        """
        try:
            full_key = self._get_cache_key(key)
            value = await async_redis.get(full_key)
            
            if value is not None:
                self.cache_stats['hits'] += 1
                logger.debug(f"Cache hit for key: {full_key}")
                return value
            else:
                self.cache_stats['misses'] += 1
                logger.debug(f"Cache miss for key: {full_key}")
                return None
                
        except Exception as e:
            self.cache_stats['errors'] += 1
            logger.error(f"Error getting from cache: {str(e)}")
            return None
    
    async def set(self, key: str, value: str, ttl: Optional[int] = None) -> bool:
        """
        Set a value in the cache.
        
        Args:
            key: The key to set
            value: The value to cache
            ttl: Optional time-to-live in seconds (overrides default)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            full_key = self._get_cache_key(key)
            ttl_value = ttl if ttl is not None else self.ttl
            
            result = await async_redis.setex(full_key, ttl_value, value)
            
            if result:
                self.cache_stats['sets'] += 1
                logger.debug(f"Cached value for key: {full_key}, TTL: {ttl_value}s")
                return True
            else:
                logger.warning(f"Failed to cache value for key: {full_key}")
                return False
                
        except Exception as e:
            self.cache_stats['errors'] += 1
            logger.error(f"Error setting cache: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """
        Delete a value from the cache.
        
        Args:
            key: The key to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            full_key = self._get_cache_key(key)
            result = await async_redis.delete(full_key)
            
            if result:
                logger.debug(f"Deleted cache key: {full_key}")
                return True
            else:
                logger.warning(f"Failed to delete cache key: {full_key}")
                return False
                
        except Exception as e:
            self.cache_stats['errors'] += 1
            logger.error(f"Error deleting from cache: {str(e)}")
            return False
    
    async def exists(self, key: str) -> bool:
        """
        Check if a key exists in the cache.
        
        Args:
            key: The key to check
            
        Returns:
            True if the key exists, False otherwise
        """
        try:
            full_key = self._get_cache_key(key)
            return await async_redis.exists(full_key)
                
        except Exception as e:
            self.cache_stats['errors'] += 1
            logger.error(f"Error checking cache existence: {str(e)}")
            return False
    
    async def clear_namespace(self) -> int:
        """
        Clear all keys in this cache namespace.
        
        Returns:
            Number of keys deleted
        """
        try:
            pattern = f"{self.namespace}:*"
            client = await async_redis.get_async_redis_client()
            keys = await client.keys(pattern)
            
            if not keys:
                logger.info(f"No keys found in namespace: {self.namespace}")
                return 0
            
            count = 0
            for key in keys:
                key_str = key.decode('utf-8') if isinstance(key, bytes) else key
                await client.delete(key_str)
                count += 1
            
            logger.info(f"Cleared {count} keys from namespace: {self.namespace}")
            return count
                
        except Exception as e:
            self.cache_stats['errors'] += 1
            logger.error(f"Error clearing cache namespace: {str(e)}")
            return 0
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Dict containing cache statistics
        """
        try:
            pattern = f"{self.namespace}:*"
            client = await async_redis.get_async_redis_client()
            keys = await client.keys(pattern)
            
            stats = {
                'namespace': self.namespace,
                'total_keys': len(keys),
                'hits': self.cache_stats['hits'],
                'misses': self.cache_stats['misses'],
                'sets': self.cache_stats['sets'],
                'errors': self.cache_stats['errors'],
                'ttl': self.ttl
            }
            
            # Calculate hit rate
            total_requests = stats['hits'] + stats['misses']
            if total_requests > 0:
                stats['hit_rate'] = round(stats['hits'] / total_requests, 2)
            else:
                stats['hit_rate'] = 0
            
            return stats
                
        except Exception as e:
            logger.error(f"Error getting cache stats: {str(e)}")
            return {
                'namespace': self.namespace,
                'error': str(e),
                **self.cache_stats
            }
    
    @staticmethod
    def hash_key(data: Any) -> str:
        """
        Create a hash key from any data.
        
        Args:
            data: The data to hash
            
        Returns:
            A hash string
        """
        try:
            # Convert to JSON string and hash
            if isinstance(data, (dict, list, tuple)):
                data_str = json.dumps(data, sort_keys=True)
            else:
                data_str = str(data)
            
            return hashlib.md5(data_str.encode('utf-8')).hexdigest()
                
        except Exception as e:
            logger.error(f"Error creating hash key: {str(e)}")
            # Fallback to a timestamp-based key
            return f"error_key_{int(time.time())}"
