"""
Response metadata utilities for the Assistant API.

This module defines the function schema for providing structured metadata
in Assistant API responses, such as reference URLs and follow-up questions.
"""

# Function schema for providing response metadata
provide_response_metadata = {
    "name": "provide_response_metadata",
    "description": "Provide metadata about the response including reference URLs and follow-up questions",
    "parameters": {
        "type": "object",
        "properties": {
            "reference_urls": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "url": {
                            "type": "string",
                            "description": "The URL of the reference"
                        },
                        "title": {
                            "type": "string",
                            "description": "The title of the referenced page"
                        },
                        "snippet": {
                            "type": "string",
                            "description": "A brief snippet or description of the content"
                        }
                    },
                    "required": ["url"]
                },
                "description": "URLs referenced to answer the question (up to 3)"
            },
            "follow_up_questions": {
                "type": "array",
                "items": {"type": "string"},
                "description": "Related questions for further exploration (up to 3)"
            }
        },
        "required": ["follow_up_questions"]
    }
}

# Instructions for the assistant to use the metadata function
metadata_instructions = """
At the end of your response, call the provide_response_metadata function to include:
1. Reference URLs that you used to answer the question (if any), including:
   - The URL itself
   - The title of the page
   - A brief snippet or description of the content
2. 2-3 follow-up questions related to the topic that the user might be interested in

Example function call:
provide_response_metadata({
  "reference_urls": [
    {
      "url": "https://example.com/article1",
      "title": "Example Article 1",
      "snippet": "This article discusses the key points about the topic."
    },
    {
      "url": "https://example.com/article2",
      "title": "Example Article 2",
      "snippet": "Additional information about related aspects."
    }
  ],
  "follow_up_questions": [
    "What are the best practices for implementing this solution?",
    "How does this compare to alternative approaches?",
    "What are the performance implications of this method?"
  ]
})

This metadata will be displayed separately from your main response.
"""
