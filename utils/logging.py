import logging
import sys

# ANSI color codes
COLORS = {
    'RESET': '\033[0m',
    'BLACK': '\033[30m',
    'RED': '\033[31m',
    'GREEN': '\033[32m',
    'YELLOW': '\033[33m',
    'BLUE': '\033[34m',
    'MAGENTA': '\033[35m',
    'CYAN': '\033[36m',
    'WHITE': '\033[37m',
    'BOLD': '\033[1m',
    'UNDERLINE': '\033[4m',
    'BACKGROUND_RED': '\033[41m',
    'BACKGROUND_GREEN': '\033[42m',
    'BACKGROUND_YELLOW': '\033[43m',
}

# Color mapping for different log levels
LEVEL_COLORS = {
    logging.DEBUG: COLORS['BLUE'],
    logging.INFO: COLORS['GREEN'],
    logging.WARNING: COLORS['YELLOW'],
    logging.ERROR: COLORS['RED'],
    logging.CRITICAL: COLORS['BACKGROUND_RED'] + COLORS['WHITE'] + COLORS['BOLD'],
}

class ColoredFormatter(logging.Formatter):
    """
    Custom formatter that adds colors to log messages based on their level.
    """
    def format(self, record):
        # Get the original formatted message
        formatted_message = super().format(record)
        
        # Add color based on log level
        if record.levelno in LEVEL_COLORS:
            color_code = LEVEL_COLORS[record.levelno]
            return f"{color_code}{formatted_message}{COLORS['RESET']}"
        
        return formatted_message

def setup_logging(level=logging.INFO, enable_colors=True):
    """
    Configure logging for the application.
    
    Args:
        level: The logging level to use
        enable_colors: Whether to enable colored output
    """
    # Define log format
    log_format = '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d:%(funcName)s] - %(message)s'
    
    # Create handler
    handler = logging.StreamHandler(sys.stdout)
    
    # Set formatter based on color preference
    if enable_colors:
        formatter = ColoredFormatter(log_format)
    else:
        formatter = logging.Formatter(log_format)
    
    handler.setFormatter(formatter)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # Remove existing handlers to avoid duplicate logs
    for hdlr in root_logger.handlers:
        root_logger.removeHandler(hdlr)
    
    root_logger.addHandler(handler)
    
    # Create logger for this application
    logger = logging.getLogger('go-gpt-backend')
    logger.setLevel(level)
    
    return logger

# Create a default logger instance
logger = setup_logging()
