import re
import json
import time
import requests
from bs4 import BeautifulSoup
import html2text
from threading import BoundedSemaphore
from .redis_client import redis_client
from .logging import logger
from config.settings import settings
from config.constants import (
    SEARCH_HISTORY_KEY_PREFIX,
    SEARCH_RESULTS_KEY_PREFIX,
    FACTUAL_PATTERNS,
    FOLLOW_UP_PATTERNS,
    STOP_WORDS
)

# Semaphore to limit concurrent web searches
search_semaphore = BoundedSemaphore(settings.MAX_CONCURRENT_SEARCHES)

def perform_web_search(query, num_results=3):
    """
    Perform a web search using either Google Custom Search API or SerpAPI
    
    Args:
        query: The search query
        num_results: Number of results to return
        
    Returns:
        list: List of dictionaries with search results (title, link, snippet)
    """
    # Skip search if query is too short or empty
    if len(query.strip()) < 3:
        logger.warning(f"Query too short for search: '{query}'")
        return []
        
    with search_semaphore:
        try:
            # Default to using SerpAPI if configured
            if settings.USE_SERP_API and settings.SERP_API_KEY:
                # Use SerpAPI
                logger.info(f"Using SerpAPI for search: {query}")
                params = {
                    "engine": "google",
                    "q": query,
                    "api_key": settings.SERP_API_KEY,
                    "num": num_results
                }
                # Add timeout to prevent hanging
                response = requests.get("https://serpapi.com/search", params=params, timeout=10)
                response.raise_for_status()
                data = response.json()
                
                results = []
                if "organic_results" in data:
                    for result in data["organic_results"][:num_results]:
                        # Skip Yahoo Finance URLs
                        if "https://finance.yahoo.com" in result.get("link", ""):
                            logger.info(f"Skipping Yahoo Finance URL: {result.get('link', '')}")
                            continue
                        
                        results.append({
                            "title": result.get("title", ""),
                            "link": result.get("link", ""),
                            "snippet": result.get("snippet", "")
                        })
                    logger.info(f"SerpAPI returned {len(results)} results")
                    return results
                else:
                    logger.warning("SerpAPI returned no organic results")
                    
            # Fall back to Google Custom Search API if SerpAPI is not configured or failed
            if settings.GOOGLE_SEARCH_API_KEY and settings.GOOGLE_SEARCH_ENGINE_ID:
                # Use Google Custom Search API
                logger.info(f"Using Google Custom Search API for: {query}")
                params = {
                    "key": settings.GOOGLE_SEARCH_API_KEY,
                    "cx": settings.GOOGLE_SEARCH_ENGINE_ID,
                    "q": query,
                    "num": num_results
                }
                # Add timeout to prevent hanging
                response = requests.get("https://www.googleapis.com/customsearch/v1", params=params, timeout=10)
                response.raise_for_status()
                data = response.json()
                
                results = []
                if "items" in data:
                    for item in data["items"][:num_results]:
                        # Skip Yahoo Finance URLs
                        if "https://finance.yahoo.com" in item.get("link", ""):
                            logger.info(f"Skipping Yahoo Finance URL: {item.get('link', '')}")
                            continue
                            
                        results.append({
                            "title": item.get("title", ""),
                            "link": item.get("link", ""),
                            "snippet": item.get("snippet", "")
                        })
                    logger.info(f"Google Custom Search API returned {len(results)} results")
                    return results
                else:
                    logger.warning("Google Custom Search API returned no items")
            
            # If we get here, no search providers are configured or all failed
            logger.warning("No search API keys configured or all search providers failed. Web search disabled.")
            return []
                
        except requests.exceptions.Timeout:
            logger.error(f"Search request timed out for query: {query}")
            return []
        except Exception as e:
            logger.error(f"Web search error: {str(e)}")
            return []

def fetch_webpage_content(url, max_length=8000):
    """
    Fetch and extract the main content from a webpage
    
    Args:
        url: The URL to fetch
        max_length: Maximum length of content to return
        
    Returns:
        str: Extracted text content
    """
    # Skip content extraction for Yahoo Finance URLs
    if "https://finance.yahoo.com" in url:
        logger.info(f"Skipping content extraction for Yahoo Finance URL: {url}")
        return ""
        
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        # Add a shorter timeout for webpage fetching to prevent hanging
        response = requests.get(url, headers=headers, timeout=5)
        response.raise_for_status()
        
        # Parse HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.extract()
        
        # Convert to plain text
        h = html2text.HTML2Text()
        h.ignore_links = False
        h.ignore_images = True
        text = h.handle(str(soup))
        
        # Truncate if too long
        if len(text) > max_length:
            text = text[:max_length] + "..."
            
        return text
    except requests.exceptions.Timeout:
        logger.warning(f"Timeout fetching webpage: {url}")
        return ""
    except Exception as e:
        logger.error(f"Error fetching webpage: {str(e)}")
        return ""

def extract_search_keywords(query):
    """
    Extract key search terms from a query
    
    Args:
        query: The user's query
        
    Returns:
        list: List of important keywords for search
    """
    # Extract words, remove punctuation, and filter stop words
    words = re.findall(r'\b\w+\b', query.lower())
    keywords = [word for word in words if word not in STOP_WORDS and len(word) > 2]
    
    # Prioritize named entities and important terms (simplified approach)
    important_terms = []
    for word in keywords:
        # Check if word starts with uppercase (potential named entity)
        if word in query and query.find(word) > 0 and word[0].isupper():
            important_terms.append(word)
    
    # Combine important terms with other keywords
    result = important_terms + [k for k in keywords if k not in important_terms]
    
    # Return up to 5 most relevant keywords
    return result[:5]

def should_perform_search(query, conversation_history=None):
    """
    Determine if a web search should be performed for this query
    
    Args:
        query: The user's query
        conversation_history: Previous messages in the conversation
        
    Returns:
        bool: True if search should be performed
    """
    # Check for factual or current events questions
    for pattern in FACTUAL_PATTERNS:
        if re.search(pattern, query, re.IGNORECASE):
            return True
    
    # Check for specific search requests
    if re.search(r'(search|look up|find|google)', query, re.IGNORECASE):
        return True
    
    # Check for follow-up questions if we have conversation history
    if conversation_history and len(conversation_history) >= 2:
        # Look for follow-up patterns
        for pattern in FOLLOW_UP_PATTERNS:
            if re.search(pattern, query, re.IGNORECASE):
                return True
        
        # Check for very short queries that might be follow-ups
        if len(query.split()) <= 5:
            # Get the last assistant response
            for i in range(len(conversation_history) - 1, -1, -1):
                msg = conversation_history[i]
                if json.loads(msg).get('role') == 'assistant':
                    # If the last response mentioned search results, this might be a follow-up
                    if 'search' in json.loads(msg).get('content', '').lower():
                        return True
                    break
    
    # Default to not searching to avoid unnecessary API calls
    return False

def get_search_history(session_id):
    """
    Get the search history for a session
    
    Args:
        session_id: The conversation session ID
        
    Returns:
        list: List of previous search queries and timestamps
    """
    history_key = f"{SEARCH_HISTORY_KEY_PREFIX}{session_id}"
    history = redis_client.lrange(history_key, 0, -1) or []
    return [json.loads(item) for item in history]

def add_to_search_history(session_id, query, results=None):
    """
    Add a search query to the session's search history
    
    Args:
        session_id: The conversation session ID
        query: The search query
        results: Optional search results to store
    """
    history_key = f"{SEARCH_HISTORY_KEY_PREFIX}{session_id}"
    search_entry = {
        'query': query,
        'timestamp': time.time(),
        'keywords': extract_search_keywords(query)
    }
    
    # Store the search history
    redis_client.rpush(history_key, json.dumps(search_entry))
    redis_client.expire(history_key, 60 * 60 * 24)  # Expire after 24 hours
    
    # If results provided, store them separately
    if results:
        results_key = f"{SEARCH_RESULTS_KEY_PREFIX}{session_id}:{int(time.time())}"
        redis_client.set(results_key, json.dumps(results))
        redis_client.expire(results_key, 60 * 60 * 24)  # Expire after 24 hours

def is_similar_to_previous_search(query, search_history, threshold=0.6):
    """
    Check if the current query is similar to a previous search
    
    Args:
        query: The current query
        search_history: List of previous searches
        threshold: Similarity threshold (0-1)
        
    Returns:
        bool: True if similar to a previous search
    """
    if not search_history:
        return False
    
    # Extract keywords from current query
    current_keywords = set(extract_search_keywords(query))
    if not current_keywords:
        return False
    
    # Check similarity with previous searches (last 5)
    for entry in search_history[-5:]:
        previous_keywords = set(entry.get('keywords', []))
        if not previous_keywords:
            continue
        
        # Calculate Jaccard similarity (intersection over union)
        intersection = len(current_keywords.intersection(previous_keywords))
        union = len(current_keywords.union(previous_keywords))
        
        if union > 0 and intersection / union >= threshold:
            return True
    
    return False

def refine_search_query(query, conversation_history=None):
    """
    Refine the search query based on conversation context
    
    Args:
        query: The user's query
        conversation_history: Previous messages in the conversation
        
    Returns:
        str: Refined search query
    """
    if not conversation_history or len(conversation_history) < 2:
        return query
    
    # For very short queries that might be follow-ups, try to add context
    if len(query.split()) <= 3:
        context_terms = []
        
        # Look at the last few messages for context
        for i in range(min(6, len(conversation_history))):
            if i >= len(conversation_history):
                break
                
            msg = json.loads(conversation_history[-(i+1)])
            if msg.get('role') == 'user':
                # Extract keywords from previous user messages
                user_content = msg.get('content', '')
                if user_content and user_content != query:
                    keywords = extract_search_keywords(user_content)
                    for kw in keywords[:2]:  # Add up to 2 keywords for context
                        if kw not in query.lower() and kw not in context_terms:
                            context_terms.append(kw)
        
        # Add context terms to the query
        if context_terms:
            return f"{query} {' '.join(context_terms)}"
    
    return query

def enrich_with_web_search(query, messages, session_id, conversation_history=None):
    """
    Enrich the conversation with web search results
    
    Args:
        query: The user's query
        messages: The current message list
        session_id: The conversation session ID
        conversation_history: Previous messages in the conversation
        
    Returns:
        list: Updated message list with search results in context
    """
    # Get search history for this session
    search_history = get_search_history(session_id)
    
    # Check if we should perform a search
    if not should_perform_search(query, conversation_history):
        return messages
    
    # Check if this query is similar to a recent search
    if is_similar_to_previous_search(query, search_history):
        logger.info(f"Query '{query}' is similar to a recent search, skipping new search")
        # We could potentially retrieve and reuse the previous search results here
        # For now, we'll just skip the search to avoid redundancy
        return messages
    
    # Refine the search query based on conversation context
    refined_query = refine_search_query(query, conversation_history)
    logger.info(f"Refined search query: '{refined_query}' (original: '{query}')")
    
    # Perform web search with the refined query
    search_results = perform_web_search(refined_query)
    if not search_results:
        return messages
    
    # Add to search history
    add_to_search_history(session_id, query, search_results)
    
    # Format search results
    search_context = "Web search results:\n\n"
    
    for i, result in enumerate(search_results, 1):
        search_context += f"{i}. {result['title']}\n"
        search_context += f"   URL: {result['link']}\n"
        search_context += f"   {result['snippet']}\n\n"
        
        # Fetch full content for the first result
        if i == 1:
            content = fetch_webpage_content(result['link'])
            if content:
                search_context += f"Content from {result['title']}:\n{content}\n\n"
    
    # Add search results as a system message
    messages.append({
        "role": "system",
        "content": f"{search_context}\nPlease use the above information to help answer the user's query. Cite sources when appropriate."
    })
    
    return messages
