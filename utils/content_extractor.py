"""
Content extraction module for extracting main content from web pages.

This module provides functions to extract the main content from web pages
while ignoring navigation bars, advertisements, footers, and other non-essential elements.
"""

import re
import logging
from typing import Dict, Any, Optional, Tuple, List
from urllib.parse import urlparse

# Import content extraction libraries
import newspaper
from newspaper import Article
import trafilatura
from trafilatura.settings import use_config
from bs4 import BeautifulSoup
from readability import Document

# Import html2text for converting HTML to markdown
import html2text

from utils.logging import logger

# Configure html2text
html_converter = html2text.HTML2Text()
html_converter.ignore_links = False
html_converter.ignore_images = True
html_converter.ignore_tables = False
html_converter.ignore_emphasis = True
html_converter.body_width = 0  # No wrapping

# Configure trafilatura
trafilatura_config = use_config()
trafilatura_config.set("DEFAULT", "MIN_OUTPUT_SIZE", "100")
trafilatura_config.set("DEFAULT", "MIN_EXTRACTED_SIZE", "100")

# Domain-specific extractors
DOMAIN_EXTRACTORS = {
    "reuters.com": "trafilatura",  # Changed from newspaper to trafilatura for better Reuters extraction
    "nytimes.com": "readability",
    "wsj.com": "readability",
    "bloomberg.com": "newspaper",
    "cnn.com": "newspaper",
    "bbc.com": "newspaper",
    "theguardian.com": "newspaper",
    "ft.com": "readability",
    "forbes.com": "newspaper",
    "techcrunch.com": "newspaper",
    "medium.com": "readability",
    "github.com": "trafilatura",
    "stackoverflow.com": "readability",
    "barchart.com": "trafilatura",  # Use trafilatura for Barchart.com
    "whitehouse.gov": "trafilatura",  # Use trafilatura for White House pages
}

def extract_main_content(html_content: str, url: str, max_length: int = 10000) -> Tuple[str, float]:
    """
    Extract the main content from an HTML page using multiple extraction methods.
    
    Args:
        html_content: The HTML content of the page
        url: The URL of the page
        max_length: Maximum length of the extracted content (default: 10000 characters)
        
    Returns:
        Tuple of (extracted_content, confidence_score)
    """
    if not html_content:
        return "", 0.0
    
    # Parse the domain from the URL
    domain = urlparse(url).netloc
    if domain.startswith('www.'):
        domain = domain[4:]
    
    # Special handling for White House pages
    if "whitehouse.gov" in domain and "fact-sheet" in url:
        content, score = _extract_whitehouse_content(html_content, url)
        if content and len(content) > 200:
            logger.info(f"Used custom White House extractor for {url} with confidence {score:.2f}")
            # Apply length limit if needed
            if len(content) > max_length:
                logger.warning(f"Content from {url} exceeded maximum length ({len(content)} > {max_length})")
                half_length = max_length // 2
                content = content[:half_length] + "\n\n[...Content truncated...]\n\n" + content[-half_length:]
            return content, score
    
    # Get the preferred extractor for this domain
    preferred_extractor = None
    for domain_pattern, extractor in DOMAIN_EXTRACTORS.items():
        if domain_pattern in domain:
            preferred_extractor = extractor
            break
    
    # Try multiple extraction methods and select the best result
    extraction_results = []
    
    # If we have a preferred extractor for this domain, try it first
    if preferred_extractor:
        content, score = _extract_with_method(html_content, url, preferred_extractor)
        if content and len(content) > 200:  # Ensure we have meaningful content
            # Give a boost to the preferred extractor
            score += 0.1
            extraction_results.append((content, score, preferred_extractor))
    
    # Try all extraction methods
    for method in ["newspaper", "trafilatura", "readability", "beautifulsoup"]:
        # Skip if this was already the preferred extractor
        if method == preferred_extractor:
            continue
        
        content, score = _extract_with_method(html_content, url, method)
        if content and len(content) > 100:  # Ensure we have meaningful content
            extraction_results.append((content, score, method))
    
    # If no extraction method worked, return empty string
    if not extraction_results:
        logger.warning(f"No content extraction method worked for {url}")
        return "", 0.0
    
    # Sort by score and return the best result
    extraction_results.sort(key=lambda x: x[1], reverse=True)
    best_content, best_score, best_method = extraction_results[0]
    
    logger.info(f"Used {best_method} extractor for {url} with confidence {best_score:.2f}")
    
    # Clean up the extracted content
    cleaned_content = _clean_extracted_content(best_content, url)
    
    # Apply length limit if needed
    if len(cleaned_content) > max_length:
        logger.warning(f"Content from {url} exceeded maximum length ({len(cleaned_content)} > {max_length})")
        half_length = max_length // 2
        cleaned_content = cleaned_content[:half_length] + "\n\n[...Content truncated...]\n\n" + cleaned_content[-half_length:]
    
    return cleaned_content, best_score

def _extract_with_method(html_content: str, url: str, method: str) -> Tuple[str, float]:
    """
    Extract content using a specific extraction method.
    
    Args:
        html_content: The HTML content of the page
        url: The URL of the page
        method: The extraction method to use
        
    Returns:
        Tuple of (extracted_content, confidence_score)
    """
    try:
        if method == "newspaper":
            return _extract_with_newspaper(html_content, url)
        elif method == "trafilatura":
            return _extract_with_trafilatura(html_content, url)
        elif method == "readability":
            return _extract_with_readability(html_content)
        elif method == "beautifulsoup":
            return _extract_with_beautifulsoup(html_content)
        else:
            return "", 0.0
    except Exception as e:
        logger.warning(f"Error extracting content with {method}: {str(e)}")
        return "", 0.0

def _extract_with_newspaper(html_content: str, url: str) -> Tuple[str, float]:
    """
    Extract content using newspaper3k.
    
    Args:
        html_content: The HTML content of the page
        url: The URL of the page
        
    Returns:
        Tuple of (extracted_content, confidence_score)
    """
    try:
        article = Article(url)
        article.set_html(html_content)
        article.parse()
        
        # Build the content with title and text
        content_parts = []
        
        if article.title:
            content_parts.append(f"# {article.title}\n\n")
        
        if article.text:
            content_parts.append(article.text)
        
        content = "\n".join(content_parts)
        
        # Calculate confidence score based on content length and structure
        confidence = 0.7  # Base confidence for newspaper
        
        # Adjust confidence based on content length
        if len(content) > 1000:
            confidence += 0.1
        elif len(content) < 200:
            confidence -= 0.2
        
        # Adjust confidence based on presence of title
        if article.title:
            confidence += 0.05
        
        # Adjust confidence based on presence of publish date
        if article.publish_date:
            confidence += 0.05
        
        return content, min(1.0, confidence)
    
    except Exception as e:
        logger.warning(f"Error extracting with newspaper: {str(e)}")
        return "", 0.0

def _extract_with_trafilatura(html_content: str, url: str) -> Tuple[str, float]:
    """
    Extract content using trafilatura.
    
    Args:
        html_content: The HTML content of the page
        url: The URL of the page
        
    Returns:
        Tuple of (extracted_content, confidence_score)
    """
    try:
        # Extract the main content
        extracted_text = trafilatura.extract(
            html_content,
            url=url,
            include_comments=False,
            include_tables=True,
            include_images=False,
            include_links=True,
            output_format="markdown",
            config=trafilatura_config
        )
        
        if not extracted_text:
            return "", 0.0
        
        # Calculate confidence score
        confidence = 0.75  # Base confidence for trafilatura
        
        # Adjust confidence based on content length
        if len(extracted_text) > 1000:
            confidence += 0.1
        elif len(extracted_text) < 200:
            confidence -= 0.2
        
        # Adjust confidence based on structure (headers, paragraphs)
        if re.search(r'#{1,6}\s+\w+', extracted_text):  # Has headers
            confidence += 0.05
        
        if extracted_text.count('\n\n') > 3:  # Has multiple paragraphs
            confidence += 0.05
        
        return extracted_text, min(1.0, confidence)
    
    except Exception as e:
        logger.warning(f"Error extracting with trafilatura: {str(e)}")
        return "", 0.0

def _extract_with_readability(html_content: str) -> Tuple[str, float]:
    """
    Extract content using readability-lxml.
    
    Args:
        html_content: The HTML content of the page
        
    Returns:
        Tuple of (extracted_content, confidence_score)
    """
    try:
        doc = Document(html_content)
        title = doc.title()
        content = doc.summary()
        
        # Convert HTML to markdown
        markdown_content = html_converter.handle(content)
        
        # Add title if available
        if title:
            markdown_content = f"# {title}\n\n{markdown_content}"
        
        # Calculate confidence score
        confidence = 0.65  # Base confidence for readability
        
        # Adjust confidence based on content length
        if len(markdown_content) > 1000:
            confidence += 0.1
        elif len(markdown_content) < 200:
            confidence -= 0.2
        
        # Adjust confidence based on structure
        if title:
            confidence += 0.05
        
        if markdown_content.count('\n\n') > 3:  # Has multiple paragraphs
            confidence += 0.05
        
        return markdown_content, min(1.0, confidence)
    
    except Exception as e:
        logger.warning(f"Error extracting with readability: {str(e)}")
        return "", 0.0

def _extract_with_beautifulsoup(html_content: str) -> Tuple[str, float]:
    """
    Extract content using BeautifulSoup with heuristics.
    
    Args:
        html_content: The HTML content of the page
        
    Returns:
        Tuple of (extracted_content, confidence_score)
    """
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Remove common non-content elements
        for element in soup.select('nav, header, footer, aside, .sidebar, .menu, .navigation, .ad, .advertisement, script, style, [class*="nav-"], [class*="menu"], [class*="sidebar"], [class*="footer"], [class*="header"], [id*="nav-"], [id*="menu"], [id*="sidebar"], [id*="footer"], [id*="header"]'):
            element.decompose()
        
        # Try to find the main content container
        main_content = None
        
        # Look for common content containers
        for selector in ['article', 'main', '.content', '.post', '.article', '#content', '#main', '[role="main"]', '.main-content', '.post-content', '.article-content']:
            elements = soup.select(selector)
            if elements:
                # Choose the largest content container
                main_content = max(elements, key=lambda x: len(x.get_text()))
                break
        
        # If no main content container found, use the body
        if not main_content:
            main_content = soup.body
        
        # Extract title
        title = soup.title.string if soup.title else ""
        
        # Extract text from main content
        if main_content:
            # Convert to markdown
            content_html = str(main_content)
            markdown_content = html_converter.handle(content_html)
            
            # Add title if available
            if title:
                markdown_content = f"# {title}\n\n{markdown_content}"
            
            # Calculate confidence score
            confidence = 0.5  # Base confidence for BeautifulSoup (lower than specialized extractors)
            
            # Adjust confidence based on content length
            if len(markdown_content) > 1000:
                confidence += 0.1
            elif len(markdown_content) < 200:
                confidence -= 0.2
            
            # Adjust confidence based on structure
            if title:
                confidence += 0.05
            
            if markdown_content.count('\n\n') > 3:  # Has multiple paragraphs
                confidence += 0.05
            
            return markdown_content, min(1.0, confidence)
        
        return "", 0.0
    
    except Exception as e:
        logger.warning(f"Error extracting with BeautifulSoup: {str(e)}")
        return "", 0.0

def _clean_extracted_content(content: str, url: str = "") -> str:
    """
    Clean up extracted content by removing noise and normalizing formatting.
    
    Args:
        content: The extracted content
        url: The URL of the page (optional)
        
    Returns:
        Cleaned content
    """
    if not content:
        return ""
    
    # Remove excessive whitespace
    content = re.sub(r'\n{3,}', '\n\n', content)
    content = re.sub(r' {2,}', ' ', content)
    
    # Remove common noise patterns
    noise_patterns = [
        r'Share this article',
        r'Share on \w+',
        r'Follow us on \w+',
        r'Subscribe to our newsletter',
        r'Sign up for our newsletter',
        r'Read more:',
        r'Related articles:',
        r'Comments',
        r'Click here',
        r'Advertisement',
        r'Sponsored content',
        r'All rights reserved',
        r'Copyright ©',
        r'Terms of (use|service)',
        r'Privacy policy',
        # Reuters-specific patterns
        r'Our Standards:.*?Reuters.*?Trust Principles',
        r'Reporting by.*?Editing by.*',
        r'Reporting by.*?Additional reporting by.*',
        r'\(\$1 = .*?\)',  # Currency conversion notes
        r'.*?minute read$',  # Reading time indicator
        r'.*?min read$',
        r'Click here to read.*',
        r'Follow Reuters.*',
        r'Register now for.*',
        r'Register for.*',
        r'Sign up for.*',
        r'Our newsletter.*',
        r'Download the app.*',
        r'Reuters Graphics.*',
        r'Thomson Reuters.*',
        r'Reuters, the news and media division of Thomson Reuters.*',
    ]
    
    # White House-specific patterns
    if url and "whitehouse.gov" in url:
        whitehouse_patterns = [
            r'Share This:',
            r'FACT SHEET:',
            r'###\s*Share',
            r'###\s*Related Topics',
            r'Stay Connected',
            r'Sign up for updates',
            r'Subscribe to .* RSS feed',
            r'Follow us on social media',
            r'The White House',
            r'1600 Pennsylvania Ave NW',
            r'Washington, DC 20500',
            r'WH\.gov',
            r'Privacy Policy',
            r'Copyright Policy',
            r'Accessibility Statement',
            r'USA\.gov',
            r'!?\[.*?\]\(.*?\)',  # Remove image markdown
            r'\*\*\s*Contact Us\s*\*\*',
            r'\*\*\s*En Español\s*\*\*',
        ]
        noise_patterns.extend(whitehouse_patterns)
        
        # Remove navigation sections
        sections_to_remove = [
            "### Navigation", 
            "### Footer", 
            "### Menu",
            "### Search",
            "### Social Media"
        ]
        
        for section in sections_to_remove:
            if section in content:
                # Find the section and remove it and everything until the next section
                section_pattern = f"{re.escape(section)}.*?(?=###|$)"
                content = re.sub(section_pattern, '', content, flags=re.DOTALL)
    
    for pattern in noise_patterns:
        content = re.sub(pattern, '', content, flags=re.IGNORECASE)
    
    # Normalize headings
    content = re.sub(r'#{7,}', '######', content)  # Limit heading levels to 6
    
    # Ensure proper spacing around headings
    content = re.sub(r'(\n#{1,6}[^\n]+)(\n[^#\n])', r'\1\n\2', content)
    
    # Remove empty list items
    content = re.sub(r'[\*\-\+]\s*\n', '', content)
    
    # Trim leading/trailing whitespace
    content = content.strip()
    
    return content

def extract_article_metadata(html_content: str, url: str) -> Dict[str, Any]:
    """
    Extract metadata from an article.
    
    Args:
        html_content: The HTML content of the page
        url: The URL of the page
        
    Returns:
        Dictionary of metadata
    """
    metadata = {
        "title": "",
        "publish_date": None,
        "authors": [],
        "summary": "",
        "keywords": [],
        "source": url
    }
    
    try:
        # Use newspaper3k for metadata extraction
        article = Article(url)
        article.set_html(html_content)
        article.parse()
        
        if article.title:
            metadata["title"] = article.title
        
        if article.publish_date:
            metadata["publish_date"] = article.publish_date.isoformat()
        
        if article.authors:
            metadata["authors"] = article.authors
        
        if article.meta_keywords:
            metadata["keywords"] = article.meta_keywords
        
        # Try to extract summary
        try:
            article.nlp()
            if article.summary:
                metadata["summary"] = article.summary
        except:
            # NLP might fail, but we can continue without a summary
            pass
        
        return metadata
    
    except Exception as e:
        logger.warning(f"Error extracting metadata: {str(e)}")
        return metadata

def is_paywall_content(html_content: str, extracted_content: str) -> bool:
    """
    Detect if the content is behind a paywall.
    
    Args:
        html_content: The HTML content of the page
        extracted_content: The extracted main content
        
    Returns:
        True if paywall is detected, False otherwise
    """
    # Check for common paywall indicators in HTML
    paywall_patterns = [
        r'subscribe\s+to\s+continue\s+reading',
        r'subscribe\s+to\s+read\s+more',
        r'subscribe\s+now\s+to\s+read',
        r'subscribe\s+to\s+access',
        r'sign\s+in\s+to\s+continue\s+reading',
        r'sign\s+up\s+to\s+continue\s+reading',
        r'premium\s+content',
        r'premium\s+article',
        r'premium\s+subscriber',
        r'for\s+subscribers\s+only',
        r'subscribers\s+only',
        r'to\s+continue\s+reading\s+this\s+premium\s+content',
        r'paywall',
        r'pay\s+wall',
        r'subscription\s+required',
        r'continue\s+reading\s+by\s+subscribing',
        r'create\s+an\s+account\s+to\s+continue\s+reading',
    ]
    
    for pattern in paywall_patterns:
        if re.search(pattern, html_content, re.IGNORECASE):
            return True
    
    # Check if extracted content is too short compared to HTML size
    if len(extracted_content) < 200 and len(html_content) > 10000:
        return True
    
    # Check for truncated content
    truncation_markers = [
        r'continue\s+reading',
        r'read\s+more',
        r'to\s+continue\s+reading',
        r'to\s+read\s+the\s+full\s+article',
        r'to\s+read\s+the\s+rest',
    ]
    
    for marker in truncation_markers:
        if re.search(marker + r'\.?\s*$', extracted_content, re.IGNORECASE):
            return True
    
    return False

def _extract_whitehouse_content(html_content: str, url: str) -> Tuple[str, float]:
    """
    Custom extraction function specifically for White House pages.
    
    Args:
        html_content: The HTML content of the page
        url: The URL of the page
        
    Returns:
        Tuple of (extracted_content, confidence_score)
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Target the main content container
    main_content = (
        soup.select_one('.fact-sheet-content') or 
        soup.select_one('article') or 
        soup.select_one('.entry-content') or
        soup.select_one('main')
    )
    
    if main_content:
        # Remove navigation, footer, and other non-essential elements
        for element in main_content.select('nav, header, footer, aside, .sidebar, .menu, .navigation, .ad, .advertisement, script, style'):
            if element:
                element.decompose()
        
        # Convert to markdown
        content_html = str(main_content)
        markdown_content = html_converter.handle(content_html)
        
        # Clean up the content
        markdown_content = _clean_extracted_content(markdown_content, url)
        
        return markdown_content, 0.9
    
    # Fall back to trafilatura if specific container not found
    return _extract_with_trafilatura(html_content, url)

def get_domain_specific_selectors(domain: str) -> List[str]:
    """
    Get domain-specific CSS selectors for content extraction.
    
    Args:
        domain: The domain name
        
    Returns:
        List of CSS selectors for the main content
    """
    # Domain-specific selectors for common news sites
    domain_selectors = {
        "reuters.com": [
            "article",
            ".article-body",
            ".ArticleBody__content___3MtUN",
            ".article__body",
            ".article-body__content__3VtU3",
            ".paywall-article",
            ".article__content",
            ".article__main",
            "[data-testid='article-body']",
            "[data-testid='primary-content']",
            ".main-content",
            ".main__content",
        ],
        "nytimes.com": [
            "article",
            ".article-content",
            ".meteredContent",
            ".StoryBodyCompanionColumn",
        ],
        "wsj.com": [
            "article",
            ".article-content",
            ".wsj-snippet-body",
            ".article_sector",
        ],
        "bloomberg.com": [
            "article",
            ".body-content",
            ".body-copy",
            ".body-copy-v2",
        ],
        "cnn.com": [
            "article",
            ".article__content",
            ".article-body",
            ".zn-body__paragraph",
        ],
        "bbc.com": [
            "article",
            ".story-body",
            ".story-body__inner",
            ".article-body-content",
        ],
        "theguardian.com": [
            "article",
            ".content__article-body",
            ".article-body-content",
        ],
        "ft.com": [
            "article",
            ".article-body",
            ".article__content",
            ".n-content-body",
        ],
        "barchart.com": [
            "article",
            ".article-content",
            ".story-content",
            ".news-content",
            ".bc-article",
            ".bc-content",
            ".story-body",
            ".main-content",
            ".content-area",
            ".article-body",
            ".news-article-content",
            "#story-content",
            "#article-content",
            "#news-content",
            "[data-testid='article-body']",
            "[data-testid='story-content']",
        ],
    }
    
    # Default selectors for any domain
    default_selectors = [
        "article",
        "main",
        ".article",
        ".post",
        ".content",
        ".article-content",
        ".post-content",
        "#content",
        "#main",
        "[role='main']",
    ]
    
    # Find the best match for the domain
    for domain_pattern, selectors in domain_selectors.items():
        if domain_pattern in domain:
            return selectors
    
    return default_selectors
