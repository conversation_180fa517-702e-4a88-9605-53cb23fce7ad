import datetime
import json
from utils.async_redis_client import async_redis
from utils.logging import logger
from config.settings import settings

class AsyncQuotaManager:
    """
    Manages API usage quotas for users.
    """
    
    @staticmethod
    async def get_quota_key(user_id: str, endpoint: str) -> str:
        """
        Generate a Redis key for storing quota information.
        
        Args:
            user_id: The user identifier
            endpoint: The API endpoint
            
        Returns:
            The Redis key
        """
        # Get current date in YYYY-MM-DD format
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        return f"quota:{endpoint}:{user_id}:{today}"
    
    @staticmethod
    def is_whitelisted(user_id: str) -> bool:
        """
        Check if a user is in the whitelist.
        
        Args:
            user_id: The user identifier
            
        Returns:
            True if the user is whitelisted, False otherwise
        """
        return user_id in settings.ASSISTANT_WHITELIST
    
    @staticmethod
    async def check_quota(user_id: str, endpoint: str) -> bool:
        """
        Check if a user has exceeded their quota for an endpoint.
        
        Args:
            user_id: The user identifier
            endpoint: The API endpoint
            
        Returns:
            True if quota is available, False if exceeded
        """
        # Skip quota check for whitelisted users
        if AsyncQuotaManager.is_whitelisted(user_id):
            logger.info(f"User {user_id} is whitelisted, skipping quota check")
            return True
            
        if not settings.ASSISTANT_QUOTA_ENABLED:
            return True
            
        # Get the quota key
        quota_key = await AsyncQuotaManager.get_quota_key(user_id, endpoint)
        
        # Get current usage
        current_usage = await async_redis.get(quota_key)
        current_usage = int(current_usage) if current_usage else 0
        
        # Get quota limit
        quota_limit = settings.ASSISTANT_DAILY_QUOTA
        
        # Check if quota is exceeded
        logger.info(f"Quota check for {user_id}: {current_usage}/{quota_limit}")
        return current_usage < quota_limit
    
    @staticmethod
    async def increment_quota(user_id: str, endpoint: str) -> int:
        """
        Increment the quota usage for a user and endpoint.
        
        Args:
            user_id: The user identifier
            endpoint: The API endpoint
            
        Returns:
            The new usage count
        """
        if not settings.ASSISTANT_QUOTA_ENABLED:
            return 0
            
        # Get the quota key
        quota_key = await AsyncQuotaManager.get_quota_key(user_id, endpoint)
        
        # Increment usage
        new_usage = await async_redis.incr(quota_key)
        
        # Set expiration if this is the first increment (to ensure it expires after 24 hours)
        if new_usage == 1:
            # Set expiration to end of day (midnight)
            now = datetime.datetime.now()
            tomorrow = now.replace(hour=0, minute=0, second=0, microsecond=0) + datetime.timedelta(days=1)
            seconds_until_midnight = int((tomorrow - now).total_seconds())
            await async_redis.expire(quota_key, seconds_until_midnight)
        
        logger.info(f"Quota incremented for {user_id}: {new_usage}")
        return new_usage
