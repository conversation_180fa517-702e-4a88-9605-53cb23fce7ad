"""
Database functions for querying SEC company facts data.
"""

import traceback
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from decimal import Decimal, InvalidOperation
from config.settings import settings
from investpy_client import get_stock_financial_summary_formatted
from utils.assistant_utils import EARNINGS_ASSISTANT_VECTOR_STORE_KEY
from utils.async_redis_client import async_redis
from utils.logging import logger
from utils.multi_schema_db_manager import db_manager
from sqlalchemy.sql import text

from utils.ticker_db_functions import get_company_name_by_ticker, get_current_ticker, get_yf_symbol_by_ticker

# Use a constant for the schema name
DEFAULT_SCHEMA = "stock_data"

def get_past_five_years() -> List[int]:
    """
    Helper function to get the past five years, optionally excluding the current year.

    Args:
        include_current_year: Whether to include the current year in the result

    Returns:
        List[int]: List of the past five years in descending order (most recent first)
    """
    current_year = datetime.now().year

    years = [
        current_year,
        current_year - 1,
        current_year - 2,
        current_year - 3,
        current_year - 4,
    ]

    logger.info(f"Past five years: {years}")
    return years

def get_past_five_financial_quarters(ticker: str = None) -> List[Tuple[int, int]]:
    """
    Helper function to get the past five financial quarters which may
    differ from calendar quarters depending on the company.

    For most companies without specific fiscal info, this defaults to calendar quarters.

    Args:
        ticker: Optional ticker symbol to check if the company has a specific fiscal year end

    Returns:
        List[Tuple[int, int]]: List of (year, quarter) tuples for the past five financial quarters
    """
    # Start with current calendar-based date
    now = datetime.now()
    current_year = now.year
    current_month = now.month

    # TODO: If ticker is provided, we could look up the company's fiscal year end
    # and adjust the quarters accordingly. For now, we'll use calendar quarters.

    # Determine current quarter (1-4)
    current_quarter = (current_month - 1) // 3 + 1

    quarters = []
    for i in range(5):
        # Calculate quarter by moving backwards
        q = current_quarter - i - 1
        y = current_year

        # Adjust year and quarter if we go below Q1
        while q < 1:
            q += 4
            y -= 1

        quarters.append(f'{y}Q{q}')

    logger.info(f"Past five financial quarters (oldest first): {quarters}")
    return quarters

def is_data_empty(data):
    """
    Check if a nested dictionary structure contains only empty dictionaries.

    Args:
        data: The data structure to check

    Returns:
        bool: True if the data contains only empty nested dictionaries, False otherwise
    """
    if not isinstance(data, dict):
        return False

    for key, value in data.items():
        if isinstance(value, dict):
            if value and not is_data_empty(value):
                return False
        else:
            # If there's any non-dict value, it's not empty
            return False

    return True

def calculate_growth_rate(current, previous):
    """
    Calculate growth rate between two values.

    Args:
        current: The current period value
        previous: The previous period value

    Returns:
        Decimal: The growth rate as a decimal (e.g., 0.15 for 15% growth)
    """
    if previous == 0 or previous is None or current is None:
        return None

    # Convert to Decimal for precise division
    try:
        current_dec = Decimal(current)
        previous_dec = Decimal(previous)
        growth_rate = (current_dec / previous_dec) - Decimal('1')
        # Round to 5 decimal places
        return growth_rate.quantize(Decimal('0.00001'))
    except (TypeError, InvalidOperation):
        logger.debug(f"Error in growth rate calculation: {current} / {previous}")
        return None

def calculate_average_growth(years_data, concept, years):
    """
    Calculate the average growth rate for a concept over the available years.

    Args:
        years_data: Dictionary of yearly data
        concept: The financial concept to calculate growth for

    Returns:
        Decimal: The average growth rate as a decimal
    """
    if concept == 'CostOfGoodsAndServicesSold':
        pass

    growth_rates = []

    # Calculate year-over-year growth rates for up to 3 years
    for i in range(len(years) - 1):
        current_year = years[i]
        previous_year = years[i + 1]

        # Skip if either year doesn't have the concept or has empty data
        if (concept not in years_data[current_year] or
            concept not in years_data[previous_year] or
            'value' not in years_data[current_year][concept] or
            'value' not in years_data[previous_year][concept]):
            continue

        try:
            # Get raw values
            current_raw = years_data[current_year][concept]['value']
            previous_raw = years_data[previous_year][concept]['value']

            # Handle various input types using Decimal for precise calculations
            try:
                if current_raw is None or previous_raw is None:
                    continue

                # Convert to string first if not already a string
                current_str = str(current_raw).strip().replace(',', '')
                previous_str = str(previous_raw).strip().replace(',', '')

                # Convert to Decimal (handles int, float, and numeric strings with better precision)
                current_value = Decimal(current_str)
                previous_value = Decimal(previous_str)

                growth_rate = calculate_growth_rate(current_value, previous_value)
                if growth_rate is not None:
                    growth_rates.append(growth_rate)

            except (InvalidOperation, ValueError) as e:
                # Log the issue with the specific values for debugging
                logger.debug(f"Cannot convert to Decimal for {concept} between {current_year} and {previous_year}: {str(e)}")
                logger.debug(f"Current raw: {current_raw}, Previous raw: {previous_raw}")
                continue

        except Exception as e:
            # Log the issue with the specific values for debugging
            logger.debug(f"Error in growth calculation for {concept} between {current_year} and {previous_year}: {str(e)}")
            continue

    # Calculate average if we have any valid growth rates
    if len(growth_rates) == len(years) - 1:
        # Use Decimal for precise average calculation
        total = sum(growth_rates)
        count = Decimal(len(growth_rates))
        return total / count

    return None

def get_quarter_from_date(date_obj):
    """
    Helper function to determine the quarter from a date object.

    Args:
        date_obj: A datetime object

    Returns:
        int: Quarter number (1-4)
    """
    month = date_obj.month
    return (month - 1) // 3 + 1

async def query_ticker_concepts_from_db2(
    ticker: str,
    market: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Query historical concept data for a specific ticker from SEC filings database.

    Args:
        ticker: The stock ticker symbol (e.g., 'AAPL', 'MSFT')

    Returns:
        dict: Historical concept data for the specified ticker
    """
    ticker = await get_current_ticker(ticker, market)
    years = get_past_five_years()
    quarters = get_past_five_financial_quarters()
    past_3_years = years[0:3]
    past_3_quarters = quarters[0:3]

    logger.info(f"Database function call: query_ticker_concepts_from_db(market='{market}', tiker='{ticker}', years={years}, quarters={quarters})")

    try:
        # Get a database session for the default schema
        async with await db_manager.get_db_session(DEFAULT_SCHEMA) as session:

            # Step 1: Get the metadata_id for the ticker
            metadata_query_sql = f"""
            SELECT id, entity_name, cik
            FROM sec_company_fact_metadata
            WHERE ticker = {ticker}
            """
            sql_one_liner = ' '.join(metadata_query_sql.strip().split())
            logger.info(f"Executing SQL: {sql_one_liner}")

            metadata_query = """
            SELECT id, entity_name, cik
            FROM sec_company_fact_metadata
            WHERE ticker = :ticker
            """
            # Execute metadata query
            metadata_result = await session.execute(text(metadata_query), {"ticker": ticker})
            company_metadata = metadata_result.fetchone()

            if not company_metadata:
                data = get_stock_financial_summary_formatted(ticker, market)
                if is_data_empty(data):
                    logger.warning(f"No metadata found for ticker: {ticker}")
                    return {
                        "error": f"No data found for ticker: {ticker}",
                        "ticker": ticker,
                        "market": market,
                        "data": {},
                        "timestamp": datetime.now().isoformat()
                    }

                company_name = await get_company_name_by_ticker(ticker, market)
                yf_symbol =  await get_yf_symbol_by_ticker(ticker, market)
                return {
                    "company_info": {
                        "name": company_name,
                        "cik": '',
                    },
                    "ticker": yf_symbol,
                    "market": market,
                    "data": data,
                    "timestamp": datetime.now().isoformat()
                }

            # Access columns by name or index depending on how session.execute returns results
            try:
                # Try accessing by column name first
                metadata_id = company_metadata["id"]
                company_info = {
                    "name": company_metadata["entity_name"],
                    "cik": company_metadata["cik"],
                }
            except (TypeError, KeyError):
                # Fall back to index-based access if name-based access fails
                metadata_id = company_metadata[0]
                company_info = {
                    "name": company_metadata[1],  # entity_name should be the second column
                    "cik": company_metadata[2],   # cik should be the third column
                }

            logger.info(f"Found metadata for {ticker}, metadata_id: {metadata_id}")

            income_concepts = [
                'Revenues',
                'CostOfGoodsAndServicesSold',
                'GrossProfit',
                'ResearchAndDevelopmentExpense',
                'SellingGeneralAndAdministrativeExpense',
                'OperatingExpenses',
                'InterestIncomeOther',
                'InterestExpense',
                'DepreciationAndAmortization',
                'OperatingIncomeLoss',
                'NetIncomeLoss',
            ]
            balance_concepts = [
                'Assets',
                'Liabilities',
                'StockholdersEquity',
                'CashAndCashEquivalentsAtCarryingValue',
                'LongTermDebtCurrent',
                'ShortTermInvestments',
                'LongTermInvestments',
                'InventoryNet',
                'Goodwill',
                'IntangibleAssetsNetExcludingGoodwill',
                'RetainedEarningsAccumulatedDeficit',
                'AccumulatedOtherComprehensiveIncomeLossNetOfTax',
            ]
            cash_flow_concepts = [
                'NetIncomeLoss',
                'NetCashProvidedByUsedInOperatingActivities',
                'NetCashProvidedByUsedInInvestingActivities',
                'NetCashProvidedByUsedInFinancingActivities',
                'CapitalExpendituresIncurredButNotYetPaid',
                'PaymentsOfDividends',
                'CashAndCashEquivalentsPeriodIncreaseDecrease',
            ]
            income_fallback_concepts = [
                'RevenueFromContractWithCustomerExcludingAssessedTax',
                'CostOfRevenue',
                'SellingAndMarketingExpense',
                'GeneralAndAdministrativeExpense',
                'CostsAndExpenses',
                'DepreciationDepletionAndAmortization',
            ]
            balance_fallback_concepts = [
                'LongTermDebtNonCurrent',
            ]
            cash_flow_fallback_concepts = [
                'CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect',
            ]
            income_show_concepts = [
                "Revenue",
                "COGS",
                "Gross Profit",
                "R&D Expenses",
                "SG&A Expenses",
                "Op. Expenses",
                "Interest Inc.",
                "Interest Exp.",
                "D&A",
                "Op. Income",
                "Net Income",
            ]
            balance_show_concepts = [
                "Total Assets",
                "Total Liabilities",
                "Total Equity",
                "Cash&Cash Equiv",
                "Long-Term Debt",
                "Short-Term Inv.",
                "Long-Term Inv.",
                "Inventory",
                "Goodwill",
                "Intangible Assets",
                "Retained Earnings",
                "Accum.OCI",
            ]
            cash_flow_show_concepts = [
                "Net Income",
                "Op.Cash Flow",
                "Cash From Investing",
                "Cash From Financing",
                "CapEx",
                "Dividends Paid",
                "Net Cash Change",
            ]

            concepts = list(set(income_concepts + balance_concepts + cash_flow_concepts + income_fallback_concepts + balance_fallback_concepts + cash_flow_fallback_concepts))
            show_concepts = list(set(income_show_concepts + balance_show_concepts + cash_flow_show_concepts))


            facts_query_sql_i = f"""
            SELECT
                id,
                concept,
                value,
                frame,
                end_date,
                form,
                start_date
            FROM
                sec_company_facts
            WHERE
                metadata_id = '{metadata_id}'
                AND (form = '10-K' OR form = '20-F' OR form = '10-Q' OR form = '6-K')
                AND unit = 'USD'
            ORDER BY
                frame DESC, end_date DESC, start_date
            """
            sql_one_liner_i = ' '.join(facts_query_sql_i.strip().split())
            logger.info(f"Executing SQL: {sql_one_liner_i}")

            facts_query_i = """
            SELECT
                id,
                concept,
                value,
                frame,
                end_date,
                form,
                start_date
            FROM
                sec_company_facts
            WHERE
                metadata_id = :metadata_id
                AND (form = '10-K' OR form = '20-F' OR form = '10-Q' OR form = '6-K')
                AND unit = 'USD'
            ORDER BY
                frame DESC, end_date DESC, start_date
            """

            params = {
                "metadata_id": metadata_id,
            }

            facts_result = await session.execute(text(facts_query_i), params)
            rows_i = facts_result.fetchall()

            data1 = {}
            for row in rows_i:
                concept = row[1]
                if concept not in concepts:
                    continue
                val = row[2]
                frame = row[3]
                end_date = row[4]
                form = row[5]
                start_date = row[6]
                if form == '10-K' or form == '20-F':
                    if not end_date:
                        logger.warnning(f'10-K or 20-F data maybe missing something {row[0]}')
                        continue
                    year = end_date.year
                    if year not in years:
                        continue
                    if year in data1 and concept in data1[year]:
                        continue
                    if year not in data1:
                        data1[year] = {}
                    data1[year][concept] = int(val)
                elif form == '10-Q' or form == '6-K':
                    if frame:
                        if 'Q' not in frame:
                            continue
                        cy_part = frame.split('Q')[0]
                        if 'CY' in cy_part:
                            real_year = int(cy_part.split('CY')[1])

                        # Extract the quarter number
                        q_part = frame.split('Q')[1]
                        # Handle cases like Q3I where I is for interim
                        if 'I' in q_part:
                            real_quarter = int(q_part.split('I')[0])
                        else:
                            real_quarter = int(q_part)
                    else:
                        if not end_date:
                            continue
                        if start_date:
                            days = (end_date - start_date).days
                            if days >= 100:
                                continue
                        real_year = end_date.year
                        real_quarter = get_quarter_from_date(end_date)
                    quarter = f'{real_year}Q{real_quarter}'
                    if quarter not in quarters:
                        continue
                    if quarter not in data1:
                        data1[quarter] = {}
                    data1[quarter][concept] = int(val)

            # set default val = 0
            # for year in data1:
            #     for concept in concepts:
            #         if concept not in data1[year]:
            #             data1[year][concept] = 0

            for year in data1:
                for show_concept in show_concepts:
                    if show_concept not in data1[year]:
                        try:
                            i1 = income_show_concepts.index(show_concept)
                        except:
                            i1 = -1
                        try:
                            i2 = balance_show_concepts.index(show_concept)
                        except:
                            i2 = -1
                        try:
                            i3 = cash_flow_show_concepts.index(show_concept)
                        except:
                            i3 = -1
                        if i1 > -1 and show_concept not in data1[year] and income_concepts[i1] in data1[year]:
                            data1[year][show_concept] = data1[year][income_concepts[i1]]
                        if i2 > -1 and show_concept not in data1[year] and balance_concepts[i2] in data1[year]:
                            data1[year][show_concept] = data1[year][balance_concepts[i2]]
                        if i3 > -1 and show_concept not in data1[year] and cash_flow_concepts[i3] in data1[year]:
                            data1[year][show_concept] = data1[year][cash_flow_concepts[i3]]

            # process fallback data
            for year in data1:
                if 'RevenueFromContractWithCustomerExcludingAssessedTax' in data1[year]:
                    data1[year]['Revenue'] = data1[year]['RevenueFromContractWithCustomerExcludingAssessedTax']
                if 'CostOfGoodsAndServicesSold' not in data1[year] and 'CostOfRevenue' in data1[year]:
                    data1[year]['COGS'] = data1[year]['CostOfRevenue']
                if 'GrossProfit' not in data1[year] and 'Revenue' in data1[year] and 'COGS' in data1[year]:
                    data1[year]['Gross Profit'] = data1[year]['Revenue'] - data1[year]['COGS']
                if 'SellingGeneralAndAdministrativeExpense' not in data1[year]:
                    SellingAndMarketingExpense = data1[year]['SellingAndMarketingExpense'] if 'SellingAndMarketingExpense' in data1[year] else None
                    GeneralAndAdministrativeExpense = data1[year]['GeneralAndAdministrativeExpense'] if 'GeneralAndAdministrativeExpense' in data1[year] else None
                    if SellingAndMarketingExpense is not None and GeneralAndAdministrativeExpense is not None:
                        data1[year]['SG&A Expenses'] = SellingAndMarketingExpense + GeneralAndAdministrativeExpense
                    elif SellingAndMarketingExpense is not None:
                        data1[year]['SG&A Expenses'] = SellingAndMarketingExpense
                    elif GeneralAndAdministrativeExpense is not None:
                        data1[year]['SG&A Expenses'] = GeneralAndAdministrativeExpense
                if 'OperatingExpenses' not in data1[year] and 'CostsAndExpenses' in data1[year]:
                    data1[year]['Op. Expenses'] = data1[year]['CostsAndExpenses']
                if 'DepreciationAndAmortization' not in data1[year] and 'DepreciationDepletionAndAmortization' in data1[year]:
                    data1[year]['D&A'] = data1[year]['DepreciationDepletionAndAmortization']

                if 'LongTermDebtCurrent' in data1[year] and 'LongTermDebtNonCurrent' in data1[year]:
                    data1[year]['Long-Term Debt'] = data1[year]['LongTermDebtCurrent'] + data1[year]['LongTermDebtNonCurrent']
                if 'CashAndCashEquivalentsPeriodIncreaseDecrease' not in data1[year] and 'CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect' in data1[year]:
                    data1[year]['Net Cash Change'] = data1[year]['CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect']

            data2 = {}
            arr1 = ['Annual', 'Quarterly']
            arr2 = ['Income', 'Balance', 'CashFlow']
            for item1 in arr1:
                if item1 not in data2:
                    data2[item1] = {}
                if item1 == 'Quarterly':
                    for item2 in arr2:
                        if item2 not in data2[item1]:
                            data2[item1][item2] = {}
                        for quarter in quarters:
                            if quarter not in data2[item1][item2]:
                                data2[item1][item2][quarter] = {}
                            if item2 == 'Income':
                                for i1, display1 in enumerate(income_show_concepts):
                                    if display1 not in data2[item1][item2][quarter] and quarter in data1:
                                        if display1 in data1[quarter]:
                                            data2[item1][item2][quarter][display1] = {
                                                'concept': income_concepts[i1],
                                                'value': str(data1[quarter][display1]),
                                                'fiscal_year': int(quarter[:-2]),
                                                'fiscal_quarter': quarter,
                                            }
                                        else:
                                            data2[item1][item2][quarter][display1] = {}
                            if item2 == 'Balance':
                                for i2, display2 in enumerate(balance_show_concepts):
                                    if display2 not in data2[item1][item2][quarter] and quarter in data1:
                                        if display2 in data1[quarter]:
                                            data2[item1][item2][quarter][display2] = {
                                                'concept': balance_concepts[i2],
                                                'value': str(data1[quarter][display2]),
                                                'fiscal_year': int(quarter[:-2]),
                                                'fiscal_quarter': quarter,
                                            }
                                        else:
                                            data2[item1][item2][quarter][display2] = {}
                            if item2 == 'CashFlow':
                                for i3, display3 in enumerate(cash_flow_show_concepts):
                                    if display3 not in data2[item1][item2][quarter] and quarter in data1:
                                        if display3 in data1[quarter]:
                                            data2[item1][item2][quarter][display3] = {
                                                'concept': cash_flow_concepts[i3],
                                                'value': str(data1[quarter][display3]),
                                                'fiscal_year': int(quarter[:-2]),
                                                'fiscal_quarter': quarter,
                                            }
                                        else:
                                            data2[item1][item2][quarter][display3] = {}
                else:
                    for item2 in arr2:
                        if item2 not in data2[item1]:
                            data2[item1][item2] = {}
                        for year in years:
                            if year not in data2[item1][item2]:
                                data2[item1][item2][year] = {}
                            if item2 == 'Income':
                                for i1, display1 in enumerate(income_show_concepts):
                                    if display1 not in data2[item1][item2][year] and year in data1:
                                        if display1 in data1[year]:
                                            data2[item1][item2][year][display1] = {
                                                'concept': income_concepts[i1],
                                                'value': str(data1[year][display1]),
                                                'fiscal_year': year,
                                            }
                                        else:
                                            data2[item1][item2][year][display1] = {}
                            if item2 == 'Balance':
                                for i2, display2 in enumerate(balance_show_concepts):
                                    if display2 not in data2[item1][item2][year] and year in data1:
                                        if display2 in data1[year]:
                                            data2[item1][item2][year][display2] = {
                                                'concept': balance_concepts[i2],
                                                'value': str(data1[year][display2]),
                                                'fiscal_year': year,
                                            }
                                        else:
                                            data2[item1][item2][year][display2] = {}
                            if item2 == 'CashFlow':
                                for i3, display3 in enumerate(cash_flow_show_concepts):
                                    if display3 not in data2[item1][item2][year] and year in data1:
                                        if display3 in data1[year]:
                                            data2[item1][item2][year][display3] = {
                                                'concept': cash_flow_concepts[i3],
                                                'value': str(data1[year][display3]),
                                                'fiscal_year': year,
                                            }
                                        else:
                                            data2[item1][item2][year][display3] = {}
            arr3 = ['AvgGrowth3Y', 'AvgGrowth5Y', 'AvgGrowth3Q', 'AvgGrowth5Q']
            for item3 in arr3:
                if item3 not in data2:
                    data2[item3] = {}
                for item2 in arr2:
                    if item2 not in data2[item3]:
                        data2[item3][item2] = {}
                    if item3 == 'AvgGrowth3Y':
                        for i1, display1 in enumerate(show_concepts):
                            avg_growth_3 = calculate_average_growth(data2['Annual'][item2], display1, past_3_years)
                            if avg_growth_3 is not None:
                                # Round to 5 decimal places
                                formatted_growth = avg_growth_3.quantize(Decimal('0.00001'))
                                data2[item3][item2][display1] = str(formatted_growth)
                    if item3 == 'AvgGrowth5Y':
                        for i1, display1 in enumerate(show_concepts):
                            avg_growth_5 = calculate_average_growth(data2['Annual'][item2], display1, years)
                            if avg_growth_5 is not None:
                                # Round to 5 decimal places
                                formatted_growth = avg_growth_5.quantize(Decimal('0.00001'))
                                data2[item3][item2][display1] = str(formatted_growth)
                    if item3 == 'AvgGrowth3Q':
                        for i1, display1 in enumerate(show_concepts):
                            avg_growth_3 = calculate_average_growth(data2['Quarterly'][item2], display1, past_3_quarters)
                            if avg_growth_3 is not None:
                                # Round to 5 decimal places
                                formatted_growth = avg_growth_3.quantize(Decimal('0.00001'))
                                data2[item3][item2][display1] = str(formatted_growth)
                    if item3 == 'AvgGrowth5Q':
                        for i1, display1 in enumerate(show_concepts):
                            avg_growth_5 = calculate_average_growth(data2['Quarterly'][item2], display1, quarters)
                            if avg_growth_5 is not None:
                                # Round to 5 decimal places
                                formatted_growth = avg_growth_5.quantize(Decimal('0.00001'))
                                data2[item3][item2][display1] = str(formatted_growth)

            # Check if data2 contains only empty nested dictionaries
            if is_data_empty(data2):
                data2 = {}

            import json
            print(json.dumps(data2))
            yf_symbol =  await get_yf_symbol_by_ticker(ticker, market)
            # Return the formatted data
            return {
                "ticker": yf_symbol,
                "market": market,
                "company_info": company_info,
                "data": data2,
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error(f"Error in query_ticker_concepts_from_db function: {str(e)}")
        logger.error(f"Query ticker concepts database error traceback: {traceback.format_exc()}")
        return {
            "error": str(e),
            "market": market,
            "ticker": ticker,
            "data": {},
            "timestamp": datetime.now().isoformat()
        }

async def query_sec_filings_from_db(
    ticker: str,
    market: Optional[str] = None,
    filing_type: str = "",
    start_date: str = "",
    end_date: str = "",
    limit: int = 100
) -> Dict[str, Any]:
    """
    Query SEC filings for a specific company by ticker symbol from the database.

    Args:
        ticker: The stock ticker symbol (e.g., 'AAPL', 'MSFT')
        filing_type: Type of SEC filing to query (e.g., '10-K', '10-Q', '8-K', or '' for all types)
        start_date: Start date for filings in YYYY-MM-DD format (empty for no start limit)
        end_date: End date for filings in YYYY-MM-DD format (defaults to current date if empty)
        limit: Maximum number of filings to return

    Returns:
        dict: SEC filing data for the specified ticker
    """
    logger.info(f"Database function call: query_sec_filings_from_db(ticker='{ticker}', market='{market}', filing_type='{filing_type}', start_date='{start_date}', end_date='{end_date}', limit={limit})")

    try:
        # Get a database session for the default schema
        async with await db_manager.get_db_session(DEFAULT_SCHEMA) as session:

            # Build query parameters for where clause
            params = {"ticker": ticker, "limit": limit}

            # Start building the base query
            base_query = """
            SELECT
                id,
                ticker,
                cik,
                filing_type,
                filing_date,
                report_date,
                fiscal_year,
                fiscal_quarter,
                url,
                accession_number
            FROM
                sec_filings
            WHERE
                ticker = :ticker
            """

            # Add filing type filter if provided
            if filing_type:
                base_query += " AND filing_type = :filing_type"
                params["filing_type"] = filing_type

            # Add date filters if provided
            if start_date:
                base_query += " AND filing_date >= :start_date"
                params["start_date"] = start_date

            if end_date:
                base_query += " AND filing_date <= :end_date"
                params["end_date"] = end_date

            # Add sorting and limit
            base_query += " ORDER BY filing_date DESC LIMIT :limit"

            # Log the query for debugging
            sql_one_liner = ' '.join(base_query.strip().split())
            logger.info(f"Executing SQL: {sql_one_liner}")

            # Execute the query
            result = await session.execute(text(base_query), params)
            rows = result.fetchall()

            # Format the results
            filings = []
            for row in rows:
                try:
                    filing = {
                        "id": row["id"],
                        "ticker": row["ticker"],
                        "cik": row["cik"],
                        "filing_type": row["filing_type"],
                        "filing_date": row["filing_date"].isoformat() if row["filing_date"] else None,
                        "report_date": row["report_date"].isoformat() if row["report_date"] else None,
                        "fiscal_year": row["fiscal_year"],
                        "fiscal_quarter": row["fiscal_quarter"],
                        "url": row["url"],
                        "accession_number": row["accession_number"]
                    }
                except (TypeError, KeyError):
                    # Fall back to index-based access if name-based access fails
                    filing = {
                        "id": row[0],
                        "ticker": row[1],
                        "cik": row[2],
                        "filing_type": row[3],
                        "filing_date": row[4].isoformat() if row[4] else None,
                        "report_date": row[5].isoformat() if row[5] else None,
                        "fiscal_year": row[6],
                        "fiscal_quarter": row[7],
                        "url": row[8],
                        "accession_number": row[9]
                    }
                filings.append(filing)

            logger.info(f"Found {len(filings)} SEC filings for ticker {ticker}")

            import json
            print(json.dumps(filings))

            return {
                "ticker": ticker,
                "filing_type": filing_type if filing_type else "all",
                "start_date": start_date,
                "end_date": end_date,
                "count": len(filings),
                "filings": filings,
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error(f"Error in query_sec_filings_from_db function: {str(e)}")
        logger.debug(f"Query SEC filings database error traceback: {traceback.format_exc()}")
        return {
            "error": str(e),
            "ticker": ticker,
            "filings": [],
            "timestamp": datetime.now().isoformat()
        }

async def query_sec_filing_file_id_from_db(
    ticker: str,
    fiscal_year: Optional[str] = None,
    fiscal_quarter: Optional[str] = None,
    retrieve_file: bool = False,
) -> Optional[str]:
    """
    Query a single SEC filing for a specific company by ticker, fiscal year, and fiscal quarter.

    Args:
        ticker: The stock ticker symbol (e.g., 'AAPL', 'MSFT')
        fiscal_year: The fiscal year of the filing
        fiscal_quarter: The fiscal quarter of the filing (1-4), optional if querying 10-K filings

    Returns:
        dict: Details of the specific SEC filing, or error message if not found
    """
    logger.info(f"Database function call: query_sec_filing_file_id_from_db(ticker='{ticker}', fiscal_year={fiscal_year}, fiscal_quarter={fiscal_quarter}')")

    try:
        # Get a database session for the default schema
        async with await db_manager.get_db_session(DEFAULT_SCHEMA) as session:
            # Build query parameters for where clause
            params = {
                "ticker": ticker,
            }

            # Start building the base query
            base_query = """
            SELECT
                id,
                openai_file_id,
                html_url,
                fiscal_year,
                fiscal_quarter
            FROM
                sec_filings
            WHERE
                ticker = :ticker
            """

            latest = False
            if fiscal_year is None or fiscal_year == 'latest':
                latest = True
            else:
                base_query += " AND fiscal_year = :fiscal_year"
                params["fiscal_year"] = str(fiscal_year)

            if not latest and fiscal_quarter is not None:
                if fiscal_quarter == 'Q1':
                    fiscal_quarter = 1
                elif fiscal_quarter == 'Q2':
                    fiscal_quarter = 2
                elif fiscal_quarter == 'Q3':
                    fiscal_quarter = 3
                elif fiscal_quarter == 'Q4':
                    fiscal_quarter = 4
                base_query += " AND fiscal_quarter = :fiscal_quarter"
                params["fiscal_quarter"] = str(fiscal_quarter)

            # Add limit to ensure we get only one result
            base_query += " ORDER BY fiscal_year DESC LIMIT 1"

            # Log the query for debugging
            sql_one_liner = ' '.join(base_query.strip().split())
            logger.info(f"Executing SQL: {sql_one_liner}")
            logger.info(f"Latest: {latest}")

            # Execute the query
            result = await session.execute(text(base_query), params)
            row = result.fetchone()

            if not row:
                logger.warning(f"No filing found for {ticker} with fiscal_year={fiscal_year}, fiscal_quarter={fiscal_quarter}")
                return None

            db_id = row[0]
            db_file_id = row[1]
            db_html_url = row[2]
            db_fiscal_year = row[3]
            db_fiscal_quarter = row[4]
            logger.info(f"DB ID: {id}")
            if db_file_id is None:
                logger.warning(f"Never upload before, upload it")
                file_id = await reupload_sec_filing_file_id_from_db(db_id, db_html_url, ticker, db_fiscal_year, db_fiscal_quarter)
                return file_id

            if not retrieve_file:
                return db_file_id

            try:
                # Retrieve file details to verify it exists and is accessible
                import os
                from openai import AsyncOpenAI
                client = AsyncOpenAI(
                    api_key=os.getenv('OPENAI_API_KEY'),
                    base_url=os.getenv('OPENAI_API_URL'),
                    default_headers={"OpenAI-Beta": "assistants=v2"}
                )
                file_details = await client.files.retrieve(db_file_id)
                logger.info(f"Successfully verified file ID: {db_file_id}, filename: {file_details.filename}, purpose: {file_details.purpose}")
                return db_file_id
            except Exception as e:
                logger.error(f"Error retrieving file with ID {db_file_id}: {str(e)}")

                file_id = await reupload_sec_filing_file_id_from_db(db_id, db_html_url, ticker, db_fiscal_year, db_fiscal_quarter)
                return file_id
    except Exception as e:
        logger.error(f"Error in query_sec_filing_file_id_from_db function: {str(e)}")
        return None

async def reupload_sec_filing_file_id_from_db(id: str, html_url: str, ticker: str, fiscal_year: str, fiscal_quarter: str) -> Optional[str]:
    logger.info(f"Database function call: reupload_sec_filing_file_id_from_db(html_url='{html_url}')")

    try:
        # Get a database session for the default schema
        async with await db_manager.get_db_session(DEFAULT_SCHEMA) as session:
            headers = {
                "User-Agent": f"{settings.SEC_API_ORGANIZATION} {settings.SEC_API_EMAIL}",
            }
            import requests
            import html2text
            response = requests.get(html_url, headers=headers)
            response.raise_for_status()
            html_bytes = b""
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    html_bytes += chunk
            html_content = html_bytes.decode(response.encoding or "utf-8", errors="replace")
            html_text = html2text.html2text(html_content)
            html_text = html_text.strip()

            import os
            today_str = datetime.now().strftime("%Y-%m-%d")
            out_dir = f"/tmp/{today_str}"
            os.makedirs(out_dir, exist_ok=True)
            if fiscal_quarter:
                filename = f"{ticker}_{fiscal_year}_{fiscal_quarter}.txt"
            else:
                filename = f"{ticker}_{fiscal_year}.txt"
            file_path = os.path.join(out_dir, filename)

            # Write the HTML text to the file
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(html_text)

            logger.info(f"Successfully saved {html_url} filing text to {file_path}")

            # Upload file to OpenAI and get file_id
            vector_id = await async_redis.get(EARNINGS_ASSISTANT_VECTOR_STORE_KEY)
            file_id = upload_file_to_openai_with_vector(vector_id, file_path)
            if not file_id:
                return None

            # Update the database with the new file_id
            update_query = """
            UPDATE sec_filings
            SET openai_file_id = :file_id
            WHERE id = :id
            """

            update_params = {
                "file_id": file_id,
                "id": id
            }

            await session.execute(text(update_query), update_params)
            await session.commit()

            logger.info(f"Updated database with new OpenAI file_id: {file_id} for filing {id}")
            return file_id

    except Exception as e:
        logger.error(f"Error in reupload_sec_filing_file_id_from_db function: {str(e)}")
        return None

def upload_file_to_openai(file_path: str) -> Optional[str]:
    try:
        import os
        from openai import OpenAI
        client = OpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_API_URL'),
            default_headers={"OpenAI-Beta": "assistants=v2"}
        )
        with open(file_path, "rb") as f:
            response = client.files.create(
                file=f,
                purpose="assistants"
            )
        file_id = response.id if hasattr(response, "id") else response["id"]
        logger.info(f"Uploaded {file_path} to OpenAI. file_id={file_id}")
        return file_id
    except Exception as e:
        logger.error(f"Failed to upload {file_path} to OpenAI: {e}")
        return None

def upload_file_to_openai_with_vector(vector_id: str, file_path: str) -> Optional[str]:
    try:
        import os
        from openai import OpenAI
        client = OpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_API_URL'),
            default_headers={"OpenAI-Beta": "assistants=v2"}
        )
        with open(file_path, "rb") as f:
            response = client.vector_stores.files.upload_and_poll(
                vector_store_id=vector_id,
                file=f,
            )
        file_id = response.id if hasattr(response, "id") else response["id"]
        logger.info(f"Uploaded {file_path} to OpenAI. file_id={file_id}, vector_id={vector_id}")
        return file_id
    except Exception as e:
        logger.error(f"Failed to upload {file_path} to {vector_id} OpenAI: {e}")
        return None

async def query_sec_filing_sections_from_db(
    ticker: str,
    fiscal_year: str,
    filing_type: str,
    section_types: List[str] = None,
    limit: int = 20
) -> Dict[str, Any]:
    """
    Query SEC filing section details from the database.

    Process:
    1. First query the filing ID from sec_filings table using ticker, filing_type, and fiscal_year
    2. Then query the section details from sec_filling_section using that filing ID

    Args:
        ticker: The stock ticker symbol
        fiscal_year: Fiscal year for the filing in YYYY format
        filing_type: Type of SEC filing to query
        section_types: Optional list of specific section types to retrieve
        limit: Maximum number of sections to return

    Returns:
        dict: SEC filing section details
    """
    logger.info(f"Query SEC filing sections from DB: ticker={ticker}, fiscal_year={fiscal_year}, filing_type={filing_type}")

    try:
        # Get a database session for the default schema
        async with await db_manager.get_db_session(DEFAULT_SCHEMA) as session:

            # Step 1: Query the filing ID from sec_filings table
            filing_query_sql = f"""
            SELECT id
            FROM sec_filings
            WHERE ticker = :ticker
            AND filing_type = :filing_type
            AND fiscal_year = :fiscal_year
            LIMIT 1
            """
            sql_one_liner = ' '.join(filing_query_sql.strip().split())
            logger.info(f"Executing SQL: {sql_one_liner}")

            filing_query = """
            SELECT id
            FROM sec_filings
            WHERE ticker = :ticker
            AND filing_type = :filing_type
            AND fiscal_year = :fiscal_year
            LIMIT 1
            """

            params = {
                "ticker": ticker,
                "filing_type": filing_type,
                "fiscal_year": fiscal_year
            }

            filing_result = await session.execute(text(filing_query), params)
            filing_record = filing_result.fetchone()

            if not filing_record:
                logger.warning(f"No filing found for {ticker} with filing_type={filing_type} and fiscal_year={fiscal_year}")
                return {
                    "ticker": ticker,
                    "filing_type": filing_type,
                    "fiscal_year": fiscal_year,
                    "filing": None,
                    "sections": [],
                    "timestamp": datetime.now().isoformat()
                }

            # Extract filing data using dict-like access if possible, otherwise fall back to index
            try:
                filing_id = filing_record["id"]
            except (TypeError, KeyError):
                filing_id = filing_record[0]

            logger.info(f"Found filing ID: {filing_id} for {ticker} {filing_type} {fiscal_year}")

            # Step 2: Query the section details from sec_filling_section
            sections_query_base = """
            SELECT id, section_name, section_text
            FROM sec_filing_sections
            WHERE filing_id = :filing_id
            """

            sections_params = {"filing_id": filing_id, "limit": limit}

            # Add section_type filter if provided
            if section_types and len(section_types) > 0:
                placeholders = ', '.join([f":section_type_{i}" for i in range(len(section_types))])
                sections_query_base += f" AND section_name IN ({placeholders})"
                # Add section types to params
                for i, section_type in enumerate(section_types):
                    sections_params[f"section_type_{i}"] = section_type

            # Add order and limit
            sections_query_base += " LIMIT :limit"

            # Log query for debugging
            sections_query_sql = sections_query_base.replace(":filing_id", str(filing_id)).replace(":limit", str(limit))
            sql_one_liner = ' '.join(sections_query_sql.strip().split())
            logger.info(f"Executing SQL: {sql_one_liner}")

            # Execute query
            sections_result = await session.execute(text(sections_query_base), sections_params)
            sections_records = sections_result.fetchall()

            sections = []
            for record in sections_records:
                try:
                    section_data = {
                        "id": record["id"],
                        "section_name": record["section_name"],
                        "section_text": record["section_text"],
                    }
                except (TypeError, KeyError):
                    section_data = {
                        "id": record[0],
                        "section_name": record[1],
                        "section_text": record[2],
                    }
                sections.append(section_data)

            logger.info(f"Retrieved {len(sections)} sections for filing ID {filing_id}")

            # Print JSON for debugging
            import json
            print(json.dumps(sections))

            return {
                "ticker": ticker,
                "filing_type": filing_type,
                "fiscal_year": fiscal_year,
                "sections": sections,
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        logger.error(f"Error in query_sec_filing_sections_from_db: {str(e)}")
        logger.debug(f"Query SEC filing sections error traceback: {traceback.format_exc()}")
        return {
            "error": str(e),
            "ticker": ticker,
            "filing_type": filing_type,
            "fiscal_year": fiscal_year,
            "filing": None,
            "sections": [],
            "timestamp": datetime.now().isoformat()
        }