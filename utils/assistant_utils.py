"""
Utility functions for working with OpenAI Assistants.
"""
import os
import time
import tempfile
import asyncio
import json
from typing import Optional, List, Dict, Any, Tuple
from openai import OpenAI, AsyncOpenAI
from utils.sample_functions import search, refine_search, get_search_history, get_tickers_dividend, query_stocks, query_ticker_concepts, query_sec_filings, query_sec_filing_sections, query_stock_price_ratings
from utils.response_metadata import provide_response_metadata
from google.cloud import storage
from utils.logging import logger
from config.settings import settings
from utils.redis_client import redis_client
from utils.async_redis_client import async_redis

# Redis key for storing the assistant ID
ASSISTANT_ID_KEY = "openai_assistant_id"
ASSISTANT_VECTOR_STORE_KEY = "openai_vector_store_id"

# Redis key for storing the earnings report assistant ID
EARNINGS_ASSISTANT_ID_KEY = "openai_earnings_assistant_id"
EARNINGS_ASSISTANT_VECTOR_STORE_KEY = "openai_earnings_vector_store_id"

# Redis key for storing the assistant ID
STOCK_ANALYST_ASSISTANT_ID_KEY = "openai_stock_analyst_assistant_id"
STOCK_ANALYST_ASSISTANT_VECTOR_STORE_KEY = "openai_stock_analyst_vector_store_id"

def initialize_assistant(client: OpenAI, enable_file_search: bool = settings.ENABLE_FILE_SEARCH) -> str:
    """
    Initialize the OpenAI Assistant with configured tools and resources.
    
    Args:
        client: The OpenAI client
        
    Returns:
        str: The assistant ID
    """
    logger.info(f"Entering initialize_assistant with enable_file_search={enable_file_search}")
    
    # Try to get assistant ID from Redis
    assistant_id = redis_client.get(ASSISTANT_ID_KEY)
    
    vector_id = redis_client.get(ASSISTANT_VECTOR_STORE_KEY)
    if vector_id:
        logger.info(f"Found vector store ID in Redis: {vector_id}")
        try:
            # Verify vector store exists
            client.vector_stores.retrieve(vector_id)
            tool_resources = {"file_search": {"vector_store_ids": [vector_id]}}
            logger.info(f"Vector store {vector_id} verified and will be used for file_search")
        except Exception as e:
            logger.error(f"Vector store {vector_id} verification failed: {str(e)}")
            tool_resources = None
            vector_id = None
    else:
        logger.warning("No vector store ID found in Redis")
        tool_resources = None
    tool_resources = tool_resources if enable_file_search else None

    # Define base tools
    tools = [
        {"type": "function", "function": search.openai_schema},
        {"type": "function", "function": refine_search.openai_schema},
        {"type": "function", "function": get_search_history.openai_schema},
        {"type": "function", "function": get_tickers_dividend.openai_schema},
        {"type": "function", "function": query_stocks.openai_schema},
        {"type": "function", "function": query_ticker_concepts.openai_schema},
        {"type": "function", "function": provide_response_metadata},
        {"type": "function", "function": query_sec_filings.openai_schema},
        {"type": "function", "function": query_sec_filing_sections.openai_schema},
    ]

    # Add file_search tool if enabled
    if enable_file_search:
        tools.insert(0, {"type": "file_search"})
        logger.info(f"enable_file_search is set to True, adding file_search tool to assistant")

    if assistant_id:
        # Verify the assistant exists and update if needed
        try:
            assistant = client.beta.assistants.retrieve(assistant_id)
            logger.info(f"Using existing assistant from Redis: {assistant.name} ({assistant_id})")

            # Update assistant if configuration has changed
            if (assistant.name != settings.OPENAI_ASSISTANT_NAME or
                assistant.model != settings.OPENAI_ASSISTANT_MODEL or
                assistant.tool_resources != tool_resources or
                assistant.tools != tools or
                assistant.instructions != settings.OPENAI_ASSISTANT_INSTRUCTIONS):
                
                assistant = client.beta.assistants.update(
                    assistant_id=assistant_id,
                    name=settings.OPENAI_ASSISTANT_NAME,
                    instructions=settings.OPENAI_ASSISTANT_INSTRUCTIONS,
                    model=settings.OPENAI_ASSISTANT_MODEL,
                    tools=tools,
                    tool_resources=tool_resources,
                )
                logger.info(f"Updated assistant: {assistant.name} ({assistant_id})")
                
            return assistant_id
        except Exception as e:
            logger.warning(f"Failed to retrieve assistant {assistant_id} from Redis: {str(e)}")
            assistant_id = None
    
    # Create a new assistant if ID is not found in Redis or is invalid
    assistant = client.beta.assistants.create(
        name=settings.OPENAI_ASSISTANT_NAME,
        instructions=settings.OPENAI_ASSISTANT_INSTRUCTIONS,
        model=settings.OPENAI_ASSISTANT_MODEL,
        tools=tools,
        tool_resources=tool_resources,
    )
    
    assistant_id = assistant.id
    logger.info(f"Created new assistant: {assistant.name} ({assistant_id})")
    
    # Store assistant ID in Redis
    redis_client.set(ASSISTANT_ID_KEY, assistant_id)
    logger.info(f"Stored assistant ID in Redis: {assistant_id}")
    
    # Add tool resources if specified
    if settings.OPENAI_ASSISTANT_TOOL_RESOURCES:
        for resource in settings.OPENAI_ASSISTANT_TOOL_RESOURCES:
            client.beta.assistants.files.create(
                assistant_id=assistant_id,
                file_id=resource
            )
        logger.info(f"Added {len(settings.OPENAI_ASSISTANT_TOOL_RESOURCES)} resources to assistant")
    
    # Log the created assistant configuration
    logger.info(f"Assistant configuration: name={assistant.name}, tools={len(assistant.tools)}, tool_resources={tool_resources}")
    
    return assistant_id

async def initialize_assistant_async(client: AsyncOpenAI, enable_file_search: bool = settings.ENABLE_FILE_SEARCH) -> str:
    """
    Initialize the OpenAI Assistant with configured tools and resources (async version).
    
    Args:
        client: The AsyncOpenAI client
        enable_file_search: Whether to enable file search functionality

    Returns:
        str: The assistant ID
    """
    logger.info(f"Entering initialize_assistant_async with enable_file_search={enable_file_search}")
    
    # Try to get assistant ID from Redis
    assistant_id = await async_redis.get(ASSISTANT_ID_KEY)
    
    vector_id = await async_redis.get(ASSISTANT_VECTOR_STORE_KEY)
    if vector_id:
        logger.info(f"Found vector store ID in Redis: {vector_id}")
        try:
            # Verify vector store exists
            await client.vector_stores.retrieve(vector_id)
            tool_resources = {"file_search": {"vector_store_ids": [vector_id]}}
            logger.info(f"Vector store {vector_id} verified and will be used for file_search")
        except Exception as e:
            logger.error(f"Vector store {vector_id} verification failed: {str(e)}")
            tool_resources = None
            vector_id = None
    else:
        logger.warning("No vector store ID found in Redis")
        tool_resources = None
    tool_resources = tool_resources if enable_file_search else None

    # Define base tools
    tools = [
        {"type": "function", "function": search.openai_schema},
        {"type": "function", "function": query_stocks.openai_schema},
        {"type": "function", "function": query_ticker_concepts.openai_schema},
        {"type": "function", "function": provide_response_metadata},
        {"type": "function", "function": query_sec_filings.openai_schema},
        {"type": "function", "function": query_sec_filing_sections.openai_schema},
        {"type": "function", "function": query_stock_price_ratings.openai_schema}
    ]

    # Add file_search tool if enabled
    if enable_file_search:
        tools.insert(0, {"type": "file_search"})
        logger.info(f"enable_file_search is set to True, adding file_search tool to assistant")

    if assistant_id:
        # Verify the assistant exists and update if needed
        try:
            assistant = await client.beta.assistants.retrieve(assistant_id)
            logger.info(f"Using existing assistant from Redis: {assistant.name} ({assistant_id})")

            # Update assistant if configuration has changed
            if (assistant.name != settings.OPENAI_ASSISTANT_NAME or
                assistant.model != settings.OPENAI_ASSISTANT_MODEL or
                assistant.tool_resources != tool_resources or
                assistant.tools != tools or
                assistant.instructions != settings.OPENAI_ASSISTANT_INSTRUCTIONS,
                assistant.temperature != settings.OPENAI_ASSISTANT_TEMPERATURE,
                assistant.top_p != settings.OPENAI_ASSISTANT_TOP):
                
                assistant = await client.beta.assistants.update(
                    assistant_id=assistant_id,
                    name=settings.OPENAI_ASSISTANT_NAME,
                    instructions=settings.OPENAI_ASSISTANT_INSTRUCTIONS,
                    model=settings.OPENAI_ASSISTANT_MODEL,
                    tools=tools,
                    tool_resources=tool_resources,
                    temperature=settings.OPENAI_ASSISTANT_TEMPERATURE,
                    top_p=settings.OPENAI_ASSISTANT_TOP
                )
                logger.info(f"Updated assistant: {assistant.name} ({assistant_id}) temperature:{assistant.temperature} top_p:{assistant.top_p} model:{assistant.model}")
                logger.debug(f"assistant debug info {assistant.instructions}, {assistant.tools}")
                
            return assistant_id
        except Exception as e:
            logger.warning(f"Failed to retrieve assistant {assistant_id} from Redis: {str(e)}")
            assistant_id = None
    
    # Create a new assistant if ID is not found in Redis or is invalid
    assistant = await client.beta.assistants.create(
        name=settings.OPENAI_ASSISTANT_NAME,
        instructions=settings.OPENAI_ASSISTANT_INSTRUCTIONS,
        model=settings.OPENAI_ASSISTANT_MODEL,
        tools=tools,
        tool_resources=tool_resources,
        temperature=settings.OPENAI_ASSISTANT_TEMPERATURE,
        top_p=settings.OPENAI_ASSISTANT_TOP
    )
    
    assistant_id = assistant.id
    logger.info(f"Created new assistant:({assistant_id}) temperature:{assistant.temperature} top_p:{assistant.top_p} model:{assistant.model}")
    
    # Store assistant ID in Redis
    await async_redis.set(ASSISTANT_ID_KEY, assistant_id)
    logger.info(f"Stored assistant ID in Redis: {assistant_id}")
    
    # Add tool resources if specified
    if settings.OPENAI_ASSISTANT_TOOL_RESOURCES:
        for resource in settings.OPENAI_ASSISTANT_TOOL_RESOURCES:
            await client.beta.assistants.files.create(
                assistant_id=assistant_id,
                file_id=resource
            )
        logger.info(f"Added {len(settings.OPENAI_ASSISTANT_TOOL_RESOURCES)} resources to assistant")
    
    # Log the created assistant configuration
    logger.info(f"Assistant configuration: name={assistant.name}, tools={len(assistant.tools)}, tool_resources={tool_resources}")
    
    return assistant_id

async def initialize_assistant_async_for_earning_report(client: AsyncOpenAI) -> str:
    """
    Initialize the OpenAI Assistant specifically for earnings report analysis (async version).
    
    Args:
        client: The AsyncOpenAI client
        enable_file_search: Whether to enable file search functionality

    Returns:
        str: The assistant ID
    """
    logger.info(f"Entering initialize_assistant_async_for_earning_report")
    
    # Try to get assistant ID from Redis
    assistant_id = await async_redis.get(EARNINGS_ASSISTANT_ID_KEY)
    vector_id = await async_redis.get(EARNINGS_ASSISTANT_VECTOR_STORE_KEY)
    tool_resources = {"file_search": {"vector_store_ids": [vector_id]}} if vector_id else None

    # Define base tools focused on earnings report analysis
    tools = [{"type": "file_search"}]

    assistant_name = "Earnings Report Analysis Assistant"
    if assistant_id:
        # Verify the assistant exists and update if needed
        try:
            assistant = await client.beta.assistants.retrieve(assistant_id)
            logger.info(f"Using existing earnings assistant from Redis: {assistant.name} ({assistant_id})")

            # Update assistant if configuration has changed
            if (assistant.name != assistant_name or
                assistant.model != settings.OPENAI_ASSISTANT_MODEL or
                assistant.tools != tools or
                assistant.tool_resources != tool_resources or
                assistant.instructions != settings.OPENAI_ASSISTANT_EARNINGS_INSTRUCTIONS,
                assistant.temperature != settings.OPENAI_ASSISTANT_TEMPERATURE,
                assistant.top_p != settings.OPENAI_ASSISTANT_TOP):
                
                assistant = await client.beta.assistants.update(
                    assistant_id=assistant_id,
                    name=assistant_name,
                    instructions=settings.OPENAI_ASSISTANT_EARNINGS_INSTRUCTIONS,
                    model=settings.OPENAI_ASSISTANT_MODEL,
                    tools=tools,
                    tool_resources=tool_resources,
                    temperature=settings.OPENAI_ASSISTANT_TEMPERATURE,
                    top_p=settings.OPENAI_ASSISTANT_TOP
                )
                logger.info(f"Updated earnings assistant: ({assistant_id}) temperature:{assistant.temperature} top_p:{assistant.top_p} model:{assistant.model}")
                
            return assistant_id
        except Exception as e:
            logger.warning(f"Failed to retrieve earnings assistant {assistant_id} from Redis: {str(e)}")
            assistant_id = None
    
    # Create a new assistant if ID is not found in Redis or is invalid
    assistant = await client.beta.assistants.create(
        name=assistant_name,
        instructions=settings.OPENAI_ASSISTANT_EARNINGS_INSTRUCTIONS,
        model=settings.OPENAI_ASSISTANT_MODEL,
        tools=tools,
        tool_resources=tool_resources,
        temperature=settings.OPENAI_ASSISTANT_TEMPERATURE,
        top_p=settings.OPENAI_ASSISTANT_TOP
    )
    
    assistant_id = assistant.id
    logger.info(f"Created new earnings assistant: ({assistant_id}) temperature:{assistant.temperature} top_p:{assistant.top_p} model:{assistant.model}")
    
    # Store assistant ID in Redis
    await async_redis.set(EARNINGS_ASSISTANT_ID_KEY, assistant_id)
    logger.info(f"Stored earnings assistant ID in Redis: {assistant_id}")
    
    # Log the created assistant configuration
    logger.info(f"Earnings assistant configuration: name={assistant.name}, tools={len(assistant.tools)}")
    
    return assistant_id

async def initialize_assistant_async_for_stock_analyst(client: AsyncOpenAI, enable_file_search: bool = settings.ENABLE_FILE_SEARCH) -> str:
    """
    Initialize the OpenAI Assistant with configured tools and resources (async version).
    
    Args:
        client: The AsyncOpenAI client
        enable_file_search: Whether to enable file search functionality

    Returns:
        str: The assistant ID
    """
    logger.info(f"Entering initialize_assistant_async_for_stock_analyst with enable_file_search={enable_file_search}")
    
    # Try to get assistant ID from Redis
    assistant_id = await async_redis.get(STOCK_ANALYST_ASSISTANT_ID_KEY)
    
    vector_id = await async_redis.get(STOCK_ANALYST_ASSISTANT_VECTOR_STORE_KEY)
    if vector_id:
        logger.info(f"Found vector store ID in Redis: {vector_id}")
        try:
            # Verify vector store exists
            await client.vector_stores.retrieve(vector_id)
            tool_resources = {"file_search": {"vector_store_ids": [vector_id]}}
            logger.info(f"Vector store {vector_id} verified and will be used for file_search")
        except Exception as e:
            logger.error(f"Vector store {vector_id} verification failed: {str(e)}")
            tool_resources = None
            vector_id = None
    else:
        logger.warning("No vector store ID found in Redis")
        tool_resources = None
    tool_resources = tool_resources if enable_file_search else None

    # Define base tools
    tools = [
        {"type": "function", "function": search.openai_schema},
        {"type": "function", "function": query_stocks.openai_schema},
        {"type": "function", "function": query_ticker_concepts.openai_schema},
        {"type": "function", "function": provide_response_metadata},
        {"type": "function", "function": query_stock_price_ratings.openai_schema},
    ]

    # Add file_search tool if enabled
    if enable_file_search:
        tools.insert(0, {"type": "file_search"})
        logger.info(f"enable_file_search is set to True, adding file_search tool to assistant")

    if assistant_id:
        # Verify the assistant exists and update if needed
        try:
            assistant = await client.beta.assistants.retrieve(assistant_id)
            logger.info(f"Using existing assistant from Redis: {assistant.name} ({assistant_id})")

            # Update assistant if configuration has changed
            if (assistant.name != settings.OPENAI_STOCK_ANALYST_ASSISTANT_NAME or
                assistant.model != settings.OPENAI_ASSISTANT_MODEL or
                assistant.tool_resources != tool_resources or
                assistant.tools != tools or
                assistant.instructions != settings.OPENAI_ASSISTANT_STOCK_ANALYST_INSTRUCTIONS,
                assistant.temperature != settings.OPENAI_ASSISTANT_TEMPERATURE,
                assistant.top_p != settings.OPENAI_ASSISTANT_TOP):
                
                assistant = await client.beta.assistants.update(
                    assistant_id=assistant_id,
                    name=settings.OPENAI_STOCK_ANALYST_ASSISTANT_NAME,
                    instructions=settings.OPENAI_ASSISTANT_STOCK_ANALYST_INSTRUCTIONS,
                    model=settings.OPENAI_ASSISTANT_MODEL,
                    tools=tools,
                    tool_resources=tool_resources,
                    temperature=settings.OPENAI_ASSISTANT_TEMPERATURE,
                    top_p=settings.OPENAI_ASSISTANT_TOP
                )
                logger.info(f"Updated assistant:({assistant_id}) temperature:{assistant.temperature} top_p:{assistant.top_p} temperature:{assistant.temperature} top_p:{assistant.top_p} model:{assistant.model}")
                logger.debug(f"assistant debug info {assistant.instructions}, {assistant.tools}")
                
            return assistant_id
        except Exception as e:
            logger.warning(f"Failed to retrieve assistant {assistant_id} from Redis: {str(e)}")
            assistant_id = None
    
    # Create a new assistant if ID is not found in Redis or is invalid
    assistant = await client.beta.assistants.create(
        name=settings.OPENAI_STOCK_ANALYST_ASSISTANT_NAME,
        instructions=settings.OPENAI_ASSISTANT_STOCK_ANALYST_INSTRUCTIONS,
        model=settings.OPENAI_ASSISTANT_MODEL,
        tools=tools,
        tool_resources=tool_resources,
        temperature=settings.OPENAI_ASSISTANT_TEMPERATURE,
        top_p=settings.OPENAI_ASSISTANT_TOP
    )
    
    assistant_id = assistant.id
    logger.info(f"Created NEW stock analyst assistant:({assistant_id}) temperature:{assistant.temperature} top_p:{assistant.top_p} model:{assistant.model}")
    
    # Store assistant ID in Redis
    await async_redis.set(STOCK_ANALYST_ASSISTANT_ID_KEY, assistant_id)
    logger.info(f"Stored stock analyst assistant ID in Redis: {assistant_id}")
    
    # Add tool resources if specified
    if settings.OPENAI_ASSISTANT_TOOL_RESOURCES:
        for resource in settings.OPENAI_ASSISTANT_TOOL_RESOURCES:
            await client.beta.assistants.files.create(
                assistant_id=assistant_id,
                file_id=resource
            )
        logger.info(f"Added {len(settings.OPENAI_ASSISTANT_TOOL_RESOURCES)} resources to assistant")
    
    # Log the created assistant configuration
    logger.info(f"Assistant configuration: name={assistant.name}, tools={len(assistant.tools)}, tool_resources={tool_resources}")
    
    return assistant_id

def setup_vector_store(client: OpenAI, vector_name: str) -> bool:
    """
    Set up vector store ID in Redis before project initialization.
    """
    try:
        vector_store_id = None
        vectors = client.vector_stores.list(limit=100)
        for vector in vectors.data:
            if vector.name == vector_name:
                vector_store_id = vector.id
                break

        if not vector_store_id:
            logger.error(f"Cannot find vector store with name: {vector_name}")
            return False

        # Verify vector store exists and is accessible
        try:
            client.vector_stores.retrieve(vector_store_id)
        except Exception as e:
            logger.error(f"Failed to retrieve vector store {vector_store_id}: {str(e)}")
            return False
        
        # Store in Redis
        redis_client.set(ASSISTANT_VECTOR_STORE_KEY, vector_store_id)
        logger.info(f"Successfully stored vector store ID in Redis: {vector_store_id}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to set up vector store ID: {str(e)}")
        return False

async def setup_vector_store_async(client: AsyncOpenAI, vector_name: str) -> bool:
    """
    Set up vector store ID in Redis before project initialization (async version).
    """
    try:
        vector_store_id = None
        vectors = await client.vector_stores.list(limit=100)
        for vector in vectors.data:
            if vector.name == vector_name:
                vector_store_id = vector.id
                break

        if not vector_store_id:
            logger.warning(f"Cannot find vector store with name: {vector_name}")
            return False

        # Verify vector store exists and is accessible
        try:
            await client.vector_stores.retrieve(vector_store_id)
        except Exception as e:
            logger.warning(f"Failed to retrieve vector store {vector_store_id}: {str(e)}")
            return False
        
        # Store in Redis
        await async_redis.set(ASSISTANT_VECTOR_STORE_KEY, vector_store_id)
        logger.info(f"Successfully stored vector store ID in Redis: {vector_store_id}")
        return True
        
    except Exception as e:
        logger.warning(f"Failed to set up vector store ID: {str(e)}")
        return False

async def create_vector_store_if_not_exist(client: AsyncOpenAI, vector_name: str) -> bool:
    vector_store_id = None
    vectors = await client.vector_stores.list(limit=100)
    for vector in vectors.data:
        if vector.name == vector_name:
            vector_store_id = vector.id
            break

    if vector_store_id:
        return vector_store_id
    return await create_vector_store(client, vector_name)

async def create_vector_store(client: AsyncOpenAI, name: str) -> str:
    new_vector = await client.vector_stores.create(
        name=name,
    )
    return new_vector.id

def upload_file_to_openai(file_path: str, client: OpenAI) -> str:
    """
    Upload a file to OpenAI for use with assistants.
    
    Args:
        file_path: Path to the file
        client: OpenAI client
        
    Returns:
        str: The file ID
    """
    with open(file_path, "rb") as file:
        response = client.files.create(
            file=file,
            purpose="assistants"
        )
    return response.id

async def upload_file_to_openai_async(file_path: str, client: AsyncOpenAI) -> str:
    """
    Upload a file to OpenAI for use with assistants (async version).
    
    Args:
        file_path: Path to the file
        client: AsyncOpenAI client
        
    Returns:
        str: The file ID
    """
    with open(file_path, "rb") as file:
        response = await client.files.create(
            file=file,
            purpose="assistants"
        )
    return response.id

def upload_gcp_bucket_to_vector_store(bucket_name: str, client: OpenAI, vector_store_id: Optional[str] = None, 
                                      vector_store_name: Optional[str] = None, prefix: str = "", 
                                      file_types: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Upload all files from a specified GCP bucket to OpenAI's vector store.
    
    Args:
        bucket_name: Name of the GCP bucket
        client: OpenAI client
        vector_store_id: Optional ID of an existing vector store to use
        vector_store_name: Optional name for a new vector store (if vector_store_id is not provided)
        prefix: Optional prefix to filter files in the bucket (folder path)
        file_types: Optional list of file extensions to filter (e.g., ['.pdf', '.docx'])
        
    Returns:
        Dict: Information about the upload batch including file IDs and status
    """
    logger.info(f"Uploading files from GCP bucket '{bucket_name}' to OpenAI vector store")
    
    # Create or retrieve vector store
    if not vector_store_id:
        if not vector_store_name:
            vector_store_name = f"gcp-bucket-{bucket_name}-{int(time.time())}"
            
        logger.info(f"Creating new vector store with name: {vector_store_name}")
        vector_store = client.vector_store.create(
            name=vector_store_name,
            expires_after=None  # No expiration
        )
        vector_store_id = vector_store.id
        logger.info(f"Created new vector store with ID: {vector_store_id}")
    else:
        logger.info(f"Using existing vector store with ID: {vector_store_id}")
    
    # Initialize GCP storage client
    storage_client = storage.Client()
    
    try:
        # Get bucket
        bucket = storage_client.get_bucket(bucket_name)
        
        # List all blobs in the bucket with the given prefix
        blobs = list(bucket.list_blobs(prefix=prefix))
        
        if not blobs:
            logger.warning(f"No files found in bucket '{bucket_name}' with prefix '{prefix}'")
            return {"status": "completed", "file_ids": [], "message": "No files found in bucket"}
        
        # Filter files by extension if specified
        if file_types:
            file_types = [ft.lower() if ft.startswith('.') else f'.{ft.lower()}' for ft in file_types]
            blobs = [blob for blob in blobs if os.path.splitext(blob.name)[1].lower() in file_types]
            
            if not blobs:
                logger.warning(f"No files with specified extensions {file_types} found in bucket '{bucket_name}'")
                return {"status": "completed", "file_ids": [], "message": "No matching files found in bucket"}
        
        # Create a temporary directory to store downloaded files
        with tempfile.TemporaryDirectory() as temp_dir:
            file_paths = []
            
            # Download each file to the temporary directory
            for blob in blobs:
                # Skip directories (blobs that end with '/')
                if blob.name.endswith('/'):
                    continue
                    
                file_name = os.path.basename(blob.name)
                temp_file_path = os.path.join(temp_dir, file_name)
                
                logger.debug(f"Downloading '{blob.name}' to temporary location")
                blob.download_to_filename(temp_file_path)
                file_paths.append(temp_file_path)
            
            if not file_paths:
                logger.warning(f"No valid files found in bucket '{bucket_name}' with prefix '{prefix}'")
                return {"status": "completed", "file_ids": [], "message": "No valid files found in bucket"}
            
            logger.info(f"Uploading {len(file_paths)} files to OpenAI vector store")
            
            # Process files in batches to avoid memory issues with large numbers of files
            BATCH_SIZE = 10  # Process 10 files at a time
            all_file_ids = []
            batch_ids = []
            
            # Process files in batches
            for i in range(0, len(file_paths), BATCH_SIZE):
                batch_file_paths = file_paths[i:i + BATCH_SIZE]
                logger.info(f"Processing batch {i // BATCH_SIZE + 1} with {len(batch_file_paths)} files")
                
                # Create a file batch with the current batch of files
                if len(batch_file_paths) == 1:
                    # If only one file in batch, use simpler method
                    with open(batch_file_paths[0], "rb") as file:
                        batch = client.vector_store.file_batches.upload_and_poll(
                            file=file,
                            vector_store_id=vector_store_id
                        )
                else:
                    # For multiple files, create a batch
                    with open(batch_file_paths[0], "rb") as file:
                        batch = client.vector_store.file_batches.create(
                            vector_store_id=vector_store_id,
                            files=[("files", file)]
                        )
                    
                    # Add remaining files to the batch
                    for file_path in batch_file_paths[1:]:
                        with open(file_path, "rb") as file:
                            client.vector_store.files.upload(
                                file=file,
                                batch_id=batch.id,
                                vector_store_id=vector_store_id
                            )
                
                # Wait for the batch to complete processing
                while batch.status != "completed":
                    time.sleep(2)  # Poll every 2 seconds
                    batch = client.vector_store.file_batches.retrieve(batch.id)
                    
                    if batch.status == "error":
                        logger.error(f"Error processing file batch: {batch.error}")
                        return {"status": "error", "message": batch.error, "batch_id": batch.id}
                
                # Collect file IDs from this batch
                batch_file_ids = [file.id for file in batch.files]
                all_file_ids.extend(batch_file_ids)
                batch_ids.append(batch.id)
                
                logger.info(f"Batch {i // BATCH_SIZE + 1} completed with {len(batch_file_ids)} files")
            
            logger.info(f"Successfully uploaded {len(all_file_ids)} files to OpenAI vector store")
            return {
                "status": "completed",
                "batch_ids": batch_ids,
                "vector_store_id": vector_store_id,
                "file_ids": all_file_ids,
                "file_count": len(all_file_ids)
            }
            
    except Exception as e:
        logger.error(f"Error uploading files from GCP bucket to vector store: {str(e)}")
        return {"status": "error", "message": str(e)}

async def upload_gcp_bucket_to_vector_store_async(bucket_name: str, client: AsyncOpenAI, vector_store_id: Optional[str] = None, 
                                                 vector_store_name: Optional[str] = None, prefix: str = "", 
                                                 file_types: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Upload all files from a specified GCP bucket to OpenAI's vector store (async version).
    
    Args:
        bucket_name: Name of the GCP bucket
        client: AsyncOpenAI client
        vector_store_id: Optional ID of an existing vector store to use
        vector_store_name: Optional name for a new vector store (if vector_store_id is not provided)
        prefix: Optional prefix to filter files in the bucket (folder path)
        file_types: Optional list of file extensions to filter (e.g., ['.pdf', '.docx'])
        
    Returns:
        Dict: Information about the upload batch including file IDs and status
    """
    logger.info(f"Uploading files from GCP bucket '{bucket_name}' to OpenAI vector store (async)")
    
    # Create or retrieve vector store
    if not vector_store_id:
        if not vector_store_name:
            vector_store_name = f"gcp-bucket-{bucket_name}-{int(time.time())}"
            
        logger.info(f"Creating new vector store with name: {vector_store_name}")
        vector_store = await client.vector_store.create(
            name=vector_store_name,
            expires_after=None  # No expiration
        )
        vector_store_id = vector_store.id
        logger.info(f"Created new vector store with ID: {vector_store_id}")
    else:
        logger.info(f"Using existing vector store with ID: {vector_store_id}")
    
    # Initialize GCP storage client
    storage_client = storage.Client()
    
    try:
        # Get bucket
        bucket = storage_client.get_bucket(bucket_name)
        
        # List all blobs in the bucket with the given prefix
        blobs = list(bucket.list_blobs(prefix=prefix))
        
        if not blobs:
            logger.warning(f"No files found in bucket '{bucket_name}' with prefix '{prefix}'")
            return {"status": "completed", "file_ids": [], "message": "No files found in bucket"}
        
        # Filter files by extension if specified
        if file_types:
            file_types = [ft.lower() if ft.startswith('.') else f'.{ft.lower()}' for ft in file_types]
            blobs = [blob for blob in blobs if os.path.splitext(blob.name)[1].lower() in file_types]
            
            if not blobs:
                logger.warning(f"No files with specified extensions {file_types} found in bucket '{bucket_name}'")
                return {"status": "completed", "file_ids": [], "message": "No matching files found in bucket"}
        
        # Create a temporary directory to store downloaded files
        with tempfile.TemporaryDirectory() as temp_dir:
            file_paths = []
            
            # Download each file to the temporary directory
            for blob in blobs:
                # Skip directories (blobs that end with '/')
                if blob.name.endswith('/'):
                    continue
                    
                file_name = os.path.basename(blob.name)
                temp_file_path = os.path.join(temp_dir, file_name)
                
                logger.debug(f"Downloading '{blob.name}' to temporary location")
                blob.download_to_filename(temp_file_path)
                file_paths.append(temp_file_path)
            
            if not file_paths:
                logger.warning(f"No valid files found in bucket '{bucket_name}' with prefix '{prefix}'")
                return {"status": "completed", "file_ids": [], "message": "No valid files found in bucket"}
            
            logger.info(f"Uploading {len(file_paths)} files to OpenAI vector store")
            
            # Process files in batches to avoid memory issues with large numbers of files
            BATCH_SIZE = 10  # Process 10 files at a time
            all_file_ids = []
            batch_ids = []
            
            # Process files in batches
            for i in range(0, len(file_paths), BATCH_SIZE):
                batch_file_paths = file_paths[i:i + BATCH_SIZE]
                logger.info(f"Processing batch {i // BATCH_SIZE + 1} with {len(batch_file_paths)} files")
                
                # Create a file batch with the current batch of files
                if len(batch_file_paths) == 1:
                    # If only one file in batch, use simpler method
                    with open(batch_file_paths[0], "rb") as file:
                        batch = await client.vector_store.file_batches.upload_and_poll(
                            file=file,
                            vector_store_id=vector_store_id
                        )
                else:
                    # For multiple files, create a batch
                    with open(batch_file_paths[0], "rb") as file:
                        batch = await client.vector_store.file_batches.create(
                            vector_store_id=vector_store_id,
                            files=[("files", file)]
                        )
                    
                    # Add remaining files to the batch
                    for file_path in batch_file_paths[1:]:
                        with open(file_path, "rb") as file:
                            await client.vector_store.files.upload(
                                file=file,
                                batch_id=batch.id,
                                vector_store_id=vector_store_id
                            )
                
                # Wait for the batch to complete processing
                while batch.status != "completed":
                    await asyncio.sleep(2)  # Poll every 2 seconds
                    batch = await client.vector_store.file_batches.retrieve(batch.id)
                    
                    if batch.status == "error":
                        logger.error(f"Error processing file batch: {batch.error}")
                        return {"status": "error", "message": batch.error, "batch_id": batch.id}
                
                # Collect file IDs from this batch
                batch_file_ids = [file.id for file in batch.files]
                all_file_ids.extend(batch_file_ids)
                batch_ids.append(batch.id)
                
                logger.info(f"Batch {i // BATCH_SIZE + 1} completed with {len(batch_file_ids)} files")
            
            logger.info(f"Successfully uploaded {len(all_file_ids)} files to OpenAI vector store")
            return {
                "status": "completed",
                "batch_ids": batch_ids,
                "vector_store_id": vector_store_id,
                "file_ids": all_file_ids,
                "file_count": len(all_file_ids)
            }
            
    except Exception as e:
        logger.error(f"Error uploading files from GCP bucket to vector store: {str(e)}")
        return {"status": "error", "message": str(e)}
