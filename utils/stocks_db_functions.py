from typing import List, Dict, Any, Optional
import asyncio
import traceback
import pandas as pd
import numpy as np
from datetime import datetime
from utils.logging import logger
from utils.multi_schema_db_manager import db_manager
from sqlalchemy import text, DECIMAL, INTEGER
import random
import ta

# Global flag to control SQL logging
ENABLE_SQL_LOGGING = True

def set_sql_logging(enabled: bool):
    """Enable or disable SQL query logging."""
    global ENABLE_SQL_LOGGING
    ENABLE_SQL_LOGGING = enabled
    logger.info(f"SQL logging {'enabled' if enabled else 'disabled'}")

def log_sql_query(query: str, params: dict = None, query_type: str = "SQL"):
    """Log SQL query if logging is enabled."""
    if ENABLE_SQL_LOGGING:
        logger.info(f"Raw {query_type} Query:\n{query}")
        if params:
            logger.info(f"{query_type} Parameters: {params}")

        # Show compiled query
        try:
            compiled_query = text(query).bindparams(**(params or {})).compile(
                compile_kwargs={"literal_binds": True}
            )
            logger.info(f"Executing Compiled {query_type}:\n{compiled_query}")
        except Exception as e:
            logger.debug(f"Could not compile query for logging: {e}")

def get_ticker_table_name_for_market(market_value: Optional[str] = None) -> str:
    """
    Determine the appropriate table name based on market value.

    Args:
        market_value: The market identifier (e.g., 'US', 'Japan', 'JP')

    Returns:
        str: The table name to query
    """
    if not market_value:
        return "all_tickers"

    # Market to table mapping
    market_table_mapping = {
        'United States': 'all_tickers',
        'Japan': 'all_tickers_jp',
        'South Korea': 'all_tickers_kr',
        'Taiwan': 'all_tickers_tw',
        'Hong Kong': 'all_tickers_hk',
    }

    # Normalize market value for case-insensitive matching
    normalized_market = market_value.strip().title()

    # Return the mapped table name or default to 'stocks'
    return market_table_mapping.get(normalized_market, 'all_tickers')

def get_stock_table_name_for_market(market_value: Optional[str] = None) -> str:
    """
    Determine the appropriate table name based on market value.

    Args:
        market_value: The market identifier (e.g., 'US', 'Japan', 'JP')

    Returns:
        str: The table name to query
    """
    if not market_value:
        return "stocks"

    # Market to table mapping
    market_table_mapping = {
        'United States': 'stocks',
        'Japan': 'stock_jp',
        'South Korea': 'stock_kr',
        'Taiwan': 'stock_tw',
        'Hong Kong': 'stock_hk',
    }

    # Normalize market value for case-insensitive matching
    normalized_market = market_value.strip().title()

    # Return the mapped table name or default to 'stocks'
    return market_table_mapping.get(normalized_market, 'stocks')

def get_stock_price_table_name_for_market(market_value: Optional[str] = None) -> str:
    """
    Determine the appropriate table name based on market value.

    Args:
        market_value: The market identifier (e.g., 'US', 'Japan', 'JP')

    Returns:
        str: The table name to query
    """
    if not market_value:
        return "stock_prices"

    # Market to table mapping
    market_table_mapping = {
        'United States': 'stock_prices',
        'Japan': 'stock_jp_price',
        'South Korea': 'stock_kr_price',
        'Taiwan': 'stock_tw_price',
        'Hong Kong': 'stock_hk_price',
    }

    # Normalize market value for case-insensitive matching
    normalized_market = market_value.strip().title()

    # Return the mapped table name or default to 'stocks'
    return market_table_mapping.get(normalized_market, 'stock_prices')

async def calculate_technical_sentiment(market_value, ticker: str, session) -> str:
    """
    Calculate technical sentiment for a given ticker using technical indicators.

    Args:
        ticker: The ticker symbol
        session: Database session

    Returns:
        str: Technical sentiment ('bullish', 'bearish', or 'neutral')
    """
    try:
        table_name = get_stock_price_table_name_for_market(market_value)

        if table_name == 'stock_prices':
            ticker_field = 'ticker'
        else:
            ticker_field = 'symbol'

        # Query historical price data for the ticker
        price_query = f"""
            SELECT
                date,
                close,
                volume
            FROM {table_name}
            WHERE {ticker_field} = :ticker
            ORDER BY date DESC
            LIMIT 100
        """

        # Log the price query with parameters
        params = {'ticker': ticker}
        log_sql_query(price_query, params, "Price Query")

        result = await session.execute(text(price_query), params)
        rows = result.fetchall()

        if not rows or len(rows) < 20:  # Using 25 to provide enough data for MA20 and MACD
            logger.warning(f"Insufficient price data for {ticker} to calculate technical sentiment")
            return "neutral"

        # Convert to DataFrame for technical analysis
        df = pd.DataFrame([(row.date, row.close, row.volume) for row in rows],
                          columns=['date', 'close', 'volume'])
        df = df.sort_values('date')  # Sort by date ascending for calculations

        # Calculate technical indicators
        # Moving Averages
        df['MA_20'] = ta.trend.sma_indicator(df['close'], window=20)
        df['MA_50'] = ta.trend.sma_indicator(df['close'], window=50)

        # RSI
        df['RSI'] = ta.momentum.rsi(df['close'], window=14)

        # MACD
        macd = ta.trend.MACD(df['close'])
        df['MACD'] = macd.macd()
        df['MACD_signal'] = macd.macd_signal()

        # Get the most recent values
        latest = df.iloc[-1]

        # Determine sentiment based on indicators
        bullish_signals = 0
        bearish_signals = 0

        # Moving Average signals
        if latest['close'] > latest['MA_20']:
            bullish_signals += 1
        else:
            bearish_signals += 1

        # RSI signals
        if latest['RSI'] > 50:
            bullish_signals += 1
        else:
            bearish_signals += 1

        # MACD signals
        if latest['MACD'] > latest['MACD_signal']:
            bullish_signals += 1
        else:
            bearish_signals += 1

        # Determine overall sentiment
        if bullish_signals > bearish_signals:
            return "Bullish"
        elif bearish_signals > bullish_signals:
            return "Bearish"
        else:
            return "Neutral"

    except Exception as e:
        logger.error(f"Error calculating technical sentiment for {ticker}: {str(e)}")
        logger.debug(f"Traceback: {traceback.format_exc()}")
        return "neutral"

async def query_stock_price_ratings_from_db(
    market: Optional[str] = None,
    ticker: Optional[str] = None,
    limit: int = 10
) -> Dict[str, Any]:
    """
    Query stock price and analyst ratings information from the database.

    Args:
        ticker: Optional ticker symbol to filter by
        limit: Maximum number of stocks to return if ticker is not specified

    Returns:
        dict: Stock price and ratings data with technical sentiment
    """
    try:

        # Check if there's a market condition to determine table name
        market_value = market

        # Determine table name based on market
        table_name = get_stock_table_name_for_market(market_value)

        if table_name == 'stocks':
            select_ticker_field = 'ticker as symbol'
            ticker_field = 'ticker'
        else:
            select_ticker_field = 'symbol'
            ticker_field = 'symbol'

        # Build query
        query = f"""
            SELECT
                {select_ticker_field},
                name as company_name,
                sector,
                current_price,
                target_low_price,
                target_high_price,
                target_mean_price,
                recommendation_mean,
                recommendation_key,
                number_of_analyst_opinions,
                average_analyst_rating,
                last_updated
            FROM {table_name}
            WHERE 1=1
        """

        params = {}

        # Add ticker filter if provided
        if ticker:
            query += f" AND {ticker_field} = :ticker"
            params['ticker'] = ticker

        # Add order by clause
        query += " ORDER BY market_cap DESC"

        # Add limit
        query += " LIMIT :limit"
        params['limit'] = limit

        # Format results
        stocks = []

        valuation_categories = ["Deeply Undervalued", "Undervalued", "Fairly Valued", "Overvalued", "Highly Overvalued"]
        random_choice = random.choice(valuation_categories)

        async with await db_manager.get_db_session('stock_data') as session:
            # Log the query using the helper function
            log_sql_query(query, params, "Stock Price Ratings")

            result = await session.execute(text(query), params)
            rows = result.fetchall()

            # Process each stock and add technical sentiment
            for row in rows:
                # Calculate technical sentiment for this ticker
                technical_sentiment = await calculate_technical_sentiment(market_value, row.symbol, session)

                stock = {
                    "symbol": row.symbol,
                    "company_name": row.company_name,
                    "sector": row.sector,
                    "current_price": row.current_price,
                    "target_low_price": row.target_low_price,
                    "target_high_price": row.target_high_price,
                    "target_mean_price": row.target_mean_price,
                    "analyst_rating": row.recommendation_key,
                    "number_of_analyst_opinions": row.number_of_analyst_opinions,
                    "technical_sentiment": technical_sentiment,
                    "valuation": random_choice,
                    "last_updated": row.last_updated.isoformat() if row.last_updated else None
                }
                stocks.append(stock)

        logger.info(f"Query returned {stocks} with len {len(stocks)}")
        return {
            "stocks": stocks,
            "count": len(stocks),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Database error: {str(e)}")
        logger.debug(f"Traceback: {traceback.format_exc()}")
        return {
            "error": str(e),
            "stocks": [],
            "timestamp": datetime.now().isoformat()
        }

async def query_stocks_from_db(
    conditions: List[Dict[str, str]],
    limit: int = 100
) -> Dict[str, Any]:
    """
    Query stocks from MySQL database using flexible conditions.
    """
    # MySQL operator mapping
    operators = {
        'gt': '>',
        'gte': '>=',
        'lt': '<',
        'lte': '<=',
        'eq': '='
    }

    try:
        # Check if there's a market condition to determine table name
        market_value = None

        for condition in conditions:
            if condition['name'] == 'market':
                market_value = condition['val']
                break

        # Determine table name based on market
        table_name = get_stock_table_name_for_market(market_value)

        if table_name == 'stocks':
            select_ticker_field = 'ticker as symbol'
            ticker_field = 'ticker'
        else:
            select_ticker_field = 'symbol'
            ticker_field = 'symbol'

        # Build base query with dynamic table name
        query = f"""
            SELECT
                {select_ticker_field},
                name as company_name,
                city,
                sector,
                market_cap,
                dividend_yield,
                dividend_rate,
                trailing_pe,
                trailing_eps,
                volume,
                last_updated
            FROM {table_name}
            WHERE 1=1
        """

        # Build WHERE clause
        params = {}
        for idx, condition in enumerate(conditions, 1):
            field = condition['name']
            op = operators[condition['expr']]
            param_name = f"param_{idx}"

            if field == 'market':
                continue
            elif field == 'market_cap':
                query += f" AND CAST({field} AS UNSIGNED) {op} :{param_name}"
                params[param_name] = condition['val']
            elif field == 'PE':
                query += f" AND CAST(trailing_pe AS DECIMAL(15,5)) {op} :{param_name}"
                params[param_name] = condition['val']
            elif field in ['EPS', 'dividend_yield']:
                if field == 'EPS':
                    field = 'trailing_eps'
                query += f" AND CAST({field} AS DECIMAL(10,2)) {op} :{param_name}"
                params[param_name] = condition['val']
            elif field == 'sector':
                query += f" AND sector LIKE :{param_name}"
                params[param_name] = f"%{condition['val']}%"
            elif field == 'ticker':
                ticker = condition['val']
                query += f" AND {ticker_field} = :{param_name}"
                params[param_name] = ticker
            else:
                query += f" AND {field} {op} :{param_name}"
                params[param_name] = condition['val']

        # Add order by market_cap
        query += " ORDER BY CAST(market_cap AS UNSIGNED) DESC"

        # Add limit
        query += " LIMIT :limit"
        params['limit'] = min(int(limit), 500)  # Cap at 500

        async def execute_query():
            try:
                async with await db_manager.get_db_session('stock_data') as session:
                    # Log the query using the helper function
                    log_sql_query(query, params, "Query Stocks")

                    result = await session.execute(text(query), params)
                    rows = result.fetchall()
                    return rows
            except Exception as e:
                logger.error(f"Database error: {str(e)}")
                raise

        # Execute the query
        rows = await execute_query()

        # Format results
        stocks = []
        for row in rows:
            stock = {
                "symbol": row.symbol,
                "company_name": row.company_name,
                "city": row.city,
                "sector": row.sector,
                "market_cap": row.market_cap,
                "dividend_yield": row.dividend_yield,
                "dividend_rate": row.dividend_rate,
                "trailing_pe": row.trailing_pe,
                "trailing_eps": row.trailing_eps,
                "volume": row.volume,
                "last_updated": row.last_updated.isoformat() if row.last_updated else None
            }
            stocks.append(stock)

        logger.info(f"Query returned {len(stocks)} results")
        return {
            "stocks": stocks,
            "count": len(stocks),
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"Query error: {str(e)}")
        logger.debug(f"Traceback: {traceback.format_exc()}")
        return {
            "error": str(e),
            "stocks": [],
            "timestamp": datetime.now().isoformat()
        }
