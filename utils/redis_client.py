import redis
from redis.connection import ConnectionPool
from config.settings import settings

# Initialize Redis connection pool
redis_pool = ConnectionPool(
    host=settings.REDIS_HOST,
    port=settings.REDIS_PORT,
    db=settings.REDIS_DB,
    decode_responses=True,
    max_connections=settings.REDIS_MAX_CONNECTIONS
)

# Create a Redis client instance
redis_client = redis.Redis(connection_pool=redis_pool)

def get_redis_client():
    """
    Get the Redis client instance.
    
    Returns:
        Redis client instance
    """
    return redis_client
