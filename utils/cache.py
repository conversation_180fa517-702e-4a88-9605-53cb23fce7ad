import json
import time
import hashlib
import os
import zlib
from typing import Dict, Any, Optional, List, Tuple, Union, Set
from cachetools import LRUCache, TTLCache
from utils.redis_client import redis_client
from utils.logging import logger
from config.settings import settings

# Optional imports with fallbacks
try:
    import lz4.frame
    LZ4_AVAILABLE = True
except ImportError:
    LZ4_AVAILABLE = False
    logger.warning("lz4 module not available. Compression will fall back to zlib only.")

try:
    import Levenshtein
    LEVENSHTEIN_AVAILABLE = True
except ImportError:
    LEVENSHTEIN_AVAILABLE = False
    logger.warning("Levenshtein module not available. Using basic string comparison for similar query detection.")

try:
    from diskcache import Cache as DiskCache
    DISKCACHE_AVAILABLE = True
except ImportError:
    DISKCACHE_AVAILABLE = False
    logger.warning("diskcache module not available. Disk caching will be disabled.")

# Cache key prefixes
RESPONSE_CACHE_KEY_PREFIX = "response_cache:"
CACHE_TTL = int(settings.CACHE_TTL) if hasattr(settings, 'CACHE_TTL') else 3600  # Default: 1 hour
CACHE_MEMORY_TTL = int(settings.CACHE_MEMORY_TTL) if hasattr(settings, 'CACHE_MEMORY_TTL') else 300  # Default: 5 minutes
CACHE_MEMORY_MAX_ITEMS = int(settings.CACHE_MEMORY_MAX_ITEMS) if hasattr(settings, 'CACHE_MEMORY_MAX_ITEMS') else 1000
CACHE_SIMILARITY_THRESHOLD = float(settings.CACHE_SIMILARITY_THRESHOLD) if hasattr(settings, 'CACHE_SIMILARITY_THRESHOLD') else 0.9

# Initialize memory cache (LRU + TTL)
memory_cache = TTLCache(maxsize=CACHE_MEMORY_MAX_ITEMS, ttl=CACHE_MEMORY_TTL)

# Initialize disk cache if enabled and available
disk_cache = None
if DISKCACHE_AVAILABLE and hasattr(settings, 'CACHE_PERSISTENT_ENABLED') and settings.CACHE_PERSISTENT_ENABLED:
    cache_dir = settings.CACHE_PERSISTENT_PATH
    os.makedirs(cache_dir, exist_ok=True)
    disk_cache = DiskCache(cache_dir)

# Cache usage statistics
cache_stats = {
    'memory_hits': 0,
    'redis_hits': 0,
    'disk_hits': 0,
    'misses': 0,
    'compression_savings': 0,  # bytes saved through compression
    'similar_query_hits': 0,   # hits from similar but not identical queries
}

# Track frequently accessed keys for cache warming
key_access_count = {}
recently_accessed_keys = set()

def compress_data(data: str) -> bytes:
    """
    Compress data using available compression methods.
    
    Args:
        data: The data to compress
        
    Returns:
        bytes: The compressed data
    """
    if not hasattr(settings, 'CACHE_COMPRESSION') or not settings.CACHE_COMPRESSION:
        return data.encode('utf-8')
    
    try:
        data_bytes = data.encode('utf-8')
        compressed = None
        
        # Try LZ4 compression first if available (faster)
        if LZ4_AVAILABLE:
            try:
                compressed = lz4.frame.compress(data_bytes)
                
                # If LZ4 doesn't compress well, try zlib
                if len(compressed) > 0.9 * len(data_bytes):
                    compressed = zlib.compress(data_bytes)
            except Exception:
                compressed = None
        
        # Fall back to zlib if LZ4 is not available or failed
        if compressed is None:
            compressed = zlib.compress(data_bytes)
            
        # Only use compression if it actually saves space
        if len(compressed) < len(data_bytes):
            cache_stats['compression_savings'] += (len(data_bytes) - len(compressed))
            return compressed
        else:
            return data_bytes
    except Exception as e:
        logger.warning(f"Compression error: {str(e)}")
        return data.encode('utf-8')

def decompress_data(data: bytes) -> str:
    """
    Decompress data that may have been compressed with LZ4 or zlib.
    
    Args:
        data: The data to decompress
        
    Returns:
        str: The decompressed data
    """
    if not hasattr(settings, 'CACHE_COMPRESSION') or not settings.CACHE_COMPRESSION:
        return data.decode('utf-8')
    
    try:
        # Try to decompress with LZ4 first if available
        if LZ4_AVAILABLE:
            try:
                return lz4.frame.decompress(data).decode('utf-8')
            except Exception:
                pass  # Fall through to zlib
        
        # Try zlib
        try:
            return zlib.decompress(data).decode('utf-8')
        except zlib.error:
            # If both fail, it's probably not compressed
            return data.decode('utf-8')
    except Exception as e:
        logger.warning(f"Decompression error: {str(e)}")
        return data.decode('utf-8')

def find_similar_key(query_key: str, threshold: float = CACHE_SIMILARITY_THRESHOLD) -> Optional[str]:
    """
    Find a similar key in the cache.
    
    Args:
        query_key: The key to find similar keys for
        threshold: The similarity threshold (0-1)
        
    Returns:
        Optional[str]: A similar key if found, None otherwise
    """
    # Extract the hash part of the key (after the prefix)
    query_hash = query_key[len(RESPONSE_CACHE_KEY_PREFIX):]
    
    # Function to calculate similarity between two strings
    def calculate_similarity(s1: str, s2: str) -> float:
        if LEVENSHTEIN_AVAILABLE:
            return Levenshtein.ratio(s1, s2)
        else:
            # Simple character-based similarity when Levenshtein is not available
            # Count matching characters in the same positions
            matches = sum(c1 == c2 for c1, c2 in zip(s1, s2))
            total_len = max(len(s1), len(s2))
            return matches / total_len if total_len > 0 else 0
    
    # Check memory cache first
    for key in memory_cache.keys():
        key_hash = key[len(RESPONSE_CACHE_KEY_PREFIX):]
        similarity = calculate_similarity(query_hash, key_hash)
        if similarity >= threshold:
            return key
    
    # Check Redis cache
    pattern = f"{RESPONSE_CACHE_KEY_PREFIX}*"
    keys = redis_client.keys(pattern)
    
    best_match = None
    best_similarity = 0
    
    for key in keys:
        key_str = key.decode('utf-8') if isinstance(key, bytes) else key
        key_hash = key_str[len(RESPONSE_CACHE_KEY_PREFIX):]
        similarity = calculate_similarity(query_hash, key_hash)
        
        if similarity >= threshold and similarity > best_similarity:
            best_match = key_str
            best_similarity = similarity
    
    return best_match

def generate_cache_key(provider: str, model: str, messages: List[Dict[str, Any]], **kwargs) -> str:
    """
    Generate a cache key for a model request.
    
    Args:
        provider: The provider name
        model: The model name
        messages: The messages to send to the model
        **kwargs: Additional parameters that affect the response
        
    Returns:
        str: A cache key
    """
    # Create a dictionary with all the parameters that affect the response
    cache_dict = {
        'provider': provider,
        'model': model,
        'messages': messages
    }
    
    # Add any additional parameters that affect the response
    for key, value in kwargs.items():
        if key not in ['stream', 'session_id']:  # Exclude parameters that don't affect the response
            cache_dict[key] = value
    
    # Convert to a string and hash it
    cache_str = json.dumps(cache_dict, sort_keys=True)
    return f"{RESPONSE_CACHE_KEY_PREFIX}{hashlib.md5(cache_str.encode()).hexdigest()}"

def get_cached_response(provider: str, model: str, messages: List[Dict[str, Any]], **kwargs) -> Optional[Dict[str, Any]]:
    """
    Get a cached response for a model request.
    
    Args:
        provider: The provider name
        model: The model name
        messages: The messages to send to the model
        **kwargs: Additional parameters that affect the response
        
    Returns:
        Optional[Dict[str, Any]]: The cached response, or None if not found
    """
    # Check if caching is enabled
    if not getattr(settings, 'ENABLE_CACHE', True):
        return None
    
    # Generate the cache key
    cache_key = generate_cache_key(provider, model, messages, **kwargs)
    
    # Update access statistics
    if cache_key not in key_access_count:
        key_access_count[cache_key] = 0
    key_access_count[cache_key] += 1
    recently_accessed_keys.add(cache_key)
    
    # Check memory cache first (fastest)
    if settings.CACHE_MEMORY_ENABLED and cache_key in memory_cache:
        cache_stats['memory_hits'] += 1
        logger.info(f"Memory cache hit for {provider}/{model}")
        return memory_cache[cache_key]
    
    # Try to get the cached response from Redis
    cached_data = redis_client.get(cache_key)
    if cached_data:
        try:
            # Decompress if needed
            decompressed_data = decompress_data(cached_data)
            response = json.loads(decompressed_data)
            
            # Also store in memory cache for faster access next time
            if settings.CACHE_MEMORY_ENABLED:
                memory_cache[cache_key] = response
                
            cache_stats['redis_hits'] += 1
            logger.info(f"Redis cache hit for {provider}/{model}")
            return response
        except json.JSONDecodeError:
            logger.warning(f"Failed to decode cached response for {cache_key}")
    
    # Check disk cache if enabled
    if disk_cache is not None:
        disk_response = disk_cache.get(cache_key)
        if disk_response is not None:
            # Also store in Redis and memory for faster access next time
            try:
                redis_client.setex(
                    cache_key, 
                    CACHE_TTL, 
                    compress_data(json.dumps(disk_response))
                )
                
                if settings.CACHE_MEMORY_ENABLED:
                    memory_cache[cache_key] = disk_response
                
                cache_stats['disk_hits'] += 1
                logger.info(f"Disk cache hit for {provider}/{model}")
                return disk_response
            except Exception as e:
                logger.warning(f"Failed to store disk cache response in Redis: {str(e)}")
                return disk_response
    
    # Try to find a similar query if no exact match
    similar_key = find_similar_key(cache_key)
    if similar_key:
        # Get the cached response for the similar key
        similar_cached_data = redis_client.get(similar_key)
        if similar_cached_data:
            try:
                decompressed_data = decompress_data(similar_cached_data)
                response = json.loads(decompressed_data)
                
                # Also store in memory cache for faster access next time
                if settings.CACHE_MEMORY_ENABLED:
                    memory_cache[cache_key] = response
                
                # Store in Redis under the new key as well
                redis_client.setex(
                    cache_key, 
                    CACHE_TTL, 
                    similar_cached_data  # Use the original compressed data
                )
                
                cache_stats['similar_query_hits'] += 1
                logger.info(f"Similar query cache hit for {provider}/{model}")
                return response
            except json.JSONDecodeError:
                logger.warning(f"Failed to decode similar cached response for {similar_key}")
    
    cache_stats['misses'] += 1
    logger.info(f"Cache miss for {provider}/{model}")
    return None

def cache_response(provider: str, model: str, messages: List[Dict[str, Any]], response: Dict[str, Any], **kwargs) -> None:
    """
    Cache a response from a model.
    
    Args:
        provider: The provider name
        model: The model name
        messages: The messages sent to the model
        response: The response from the model
        **kwargs: Additional parameters that affect the response
    """
    # Check if caching is enabled
    if not getattr(settings, 'ENABLE_CACHE', True):
        return
    
    # Don't cache error responses
    if 'error' in response:
        return
    
    # Generate the cache key
    cache_key = generate_cache_key(provider, model, messages, **kwargs)
    
    # Store in memory cache (fastest access)
    if settings.CACHE_MEMORY_ENABLED:
        memory_cache[cache_key] = response
    
    # Compress the response for Redis storage
    compressed_response = compress_data(json.dumps(response))
    
    # Check if we need to enforce cache size limits
    if settings.CACHE_LRU_POLICY:
        enforce_cache_size_limits()
    
    # Cache the response in Redis
    try:
        redis_client.setex(cache_key, CACHE_TTL, compressed_response)
        logger.info(f"Cached response for {provider}/{model}")
        
        # Also cache to disk if enabled
        if disk_cache is not None:
            disk_cache.set(cache_key, response, expire=CACHE_TTL)
            logger.info(f"Cached response to disk for {provider}/{model}")
    except Exception as e:
        logger.warning(f"Failed to cache response: {str(e)}")

def enforce_cache_size_limits() -> None:
    """
    Enforce cache size limits by removing least recently used items.
    """
    try:
        # Get all cache keys
        pattern = f"{RESPONSE_CACHE_KEY_PREFIX}*"
        keys = redis_client.keys(pattern)
        
        # If we're under the limit, no need to do anything
        if len(keys) <= settings.CACHE_MAX_SIZE_MB * 100:  # Rough estimate: 100 keys per MB
            return
        
        # Sort keys by access count (least accessed first)
        sorted_keys = sorted(
            [k.decode('utf-8') if isinstance(k, bytes) else k for k in keys],
            key=lambda k: key_access_count.get(k, 0)
        )
        
        # Remove the least accessed keys
        keys_to_remove = sorted_keys[:int(len(sorted_keys) * 0.2)]  # Remove 20% of keys
        
        if keys_to_remove:
            redis_client.delete(*keys_to_remove)
            logger.info(f"Removed {len(keys_to_remove)} least recently used cache keys")
            
            # Also remove from memory cache and disk cache
            for key in keys_to_remove:
                if key in memory_cache:
                    del memory_cache[key]
                if disk_cache is not None:
                    disk_cache.delete(key)
    except Exception as e:
        logger.warning(f"Failed to enforce cache size limits: {str(e)}")

def warm_cache() -> None:
    """
    Warm the cache by preloading frequently accessed items into memory.
    """
    if not settings.CACHE_MEMORY_ENABLED:
        return
    
    try:
        # Get the most frequently accessed keys
        sorted_keys = sorted(
            key_access_count.items(),
            key=lambda x: x[1],
            reverse=True
        )[:CACHE_MEMORY_MAX_ITEMS]
        
        # Load them into memory cache
        for key, _ in sorted_keys:
            if key not in memory_cache:
                cached_data = redis_client.get(key)
                if cached_data:
                    try:
                        decompressed_data = decompress_data(cached_data)
                        response = json.loads(decompressed_data)
                        memory_cache[key] = response
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to decode cached response for {key} during cache warming")
        
        logger.info(f"Warmed memory cache with {len(memory_cache)} items")
    except Exception as e:
        logger.warning(f"Failed to warm cache: {str(e)}")

def invalidate_cache(provider: str = None, model: str = None) -> int:
    """
    Invalidate cached responses.
    
    Args:
        provider: Optional provider name to invalidate only responses from that provider
        model: Optional model name to invalidate only responses from that model
        
    Returns:
        int: Number of keys deleted
    """
    count = 0
    
    if provider and model:
        # Delete keys matching the provider and model
        pattern = f"{RESPONSE_CACHE_KEY_PREFIX}*"
        keys = redis_client.keys(pattern)
        
        for key in keys:
            key_str = key.decode('utf-8') if isinstance(key, bytes) else key
            cached_data = redis_client.get(key)
            if cached_data:
                try:
                    decompressed_data = decompress_data(cached_data)
                    response = json.loads(decompressed_data)
                    if (response.get('provider') == provider and 
                        response.get('model') == model):
                        redis_client.delete(key)
                        count += 1
                        
                        # Also remove from memory cache
                        if key_str in memory_cache:
                            del memory_cache[key_str]
                        
                        # Also remove from disk cache
                        if disk_cache is not None:
                            disk_cache.delete(key_str)
                except json.JSONDecodeError:
                    pass
    elif provider:
        # Delete keys matching the provider
        pattern = f"{RESPONSE_CACHE_KEY_PREFIX}*"
        keys = redis_client.keys(pattern)
        
        for key in keys:
            key_str = key.decode('utf-8') if isinstance(key, bytes) else key
            cached_data = redis_client.get(key)
            if cached_data:
                try:
                    decompressed_data = decompress_data(cached_data)
                    response = json.loads(decompressed_data)
                    if response.get('provider') == provider:
                        redis_client.delete(key)
                        count += 1
                        
                        # Also remove from memory cache
                        if key_str in memory_cache:
                            del memory_cache[key_str]
                        
                        # Also remove from disk cache
                        if disk_cache is not None:
                            disk_cache.delete(key_str)
                except json.JSONDecodeError:
                    pass
    else:
        # Delete all cached responses
        pattern = f"{RESPONSE_CACHE_KEY_PREFIX}*"
        keys = redis_client.keys(pattern)
        if keys:
            count = redis_client.delete(*keys)
            
            # Also clear memory cache
            memory_cache.clear()
            
            # Also clear disk cache
            if disk_cache is not None:
                disk_cache.clear()
    
    return count

def get_cache_stats() -> Dict[str, Any]:
    """
    Get statistics about the cache.
    
    Returns:
        Dict[str, Any]: Cache statistics
    """
    pattern = f"{RESPONSE_CACHE_KEY_PREFIX}*"
    keys = redis_client.keys(pattern)
    
    stats = {
        'total_keys': len(keys),
        'providers': {},
        'models': {},
        'provider_models': {},
        'memory_cache_size': len(memory_cache),
        'memory_cache_max_size': CACHE_MEMORY_MAX_ITEMS,
        'memory_hits': cache_stats['memory_hits'],
        'redis_hits': cache_stats['redis_hits'],
        'disk_hits': cache_stats.get('disk_hits', 0),
        'similar_query_hits': cache_stats['similar_query_hits'],
        'misses': cache_stats['misses'],
        'compression_savings_mb': round(cache_stats['compression_savings'] / (1024 * 1024), 2),
        'disk_cache_enabled': disk_cache is not None,
        'disk_cache_size': disk_cache.volume() if disk_cache is not None else 0,
        'cache_ttl': CACHE_TTL,
        'memory_cache_ttl': CACHE_MEMORY_TTL,
    }
    
    # Calculate hit rate
    total_requests = stats['memory_hits'] + stats['redis_hits'] + stats['disk_hits'] + stats['misses']
    if total_requests > 0:
        stats['hit_rate'] = round((stats['memory_hits'] + stats['redis_hits'] + stats['disk_hits']) / total_requests, 2)
    else:
        stats['hit_rate'] = 0
    
    for key in keys:
        cached_data = redis_client.get(key)
        if cached_data:
            try:
                decompressed_data = decompress_data(cached_data)
                response = json.loads(decompressed_data)
                provider = response.get('provider', 'unknown')
                model = response.get('model', 'unknown')
                
                # Update provider stats
                if provider not in stats['providers']:
                    stats['providers'][provider] = 0
                stats['providers'][provider] += 1
                
                # Update model stats
                if model not in stats['models']:
                    stats['models'][model] = 0
                stats['models'][model] += 1
                
                # Update provider/model stats
                provider_model = f"{provider}/{model}"
                if provider_model not in stats['provider_models']:
                    stats['provider_models'][provider_model] = 0
                stats['provider_models'][provider_model] += 1
            except json.JSONDecodeError:
                pass
    
    return stats

# Periodically warm the cache (can be called from a background task)
def periodic_cache_maintenance():
    """
    Perform periodic cache maintenance tasks.
    """
    # Warm the cache
    warm_cache()
    
    # Enforce cache size limits
    if settings.CACHE_LRU_POLICY:
        enforce_cache_size_limits()
    
    # Reset recently accessed keys
    recently_accessed_keys.clear()
    
    # Log cache stats
    stats = get_cache_stats()
    logger.info(f"Cache stats: {json.dumps(stats)}")
