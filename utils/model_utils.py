import re
import time
from threading import Lock
from .logging import logger
from config.constants import MODEL_METRICS, MODEL_CAPABILITIES

# Lock for updating metrics
metrics_lock = Lock()

def update_model_metrics(provider_model, latency=None, error=False):
    """
    Update performance metrics for a specific provider/model
    
    Args:
        provider_model: The provider/model identifier (e.g., 'openai/gpt-4')
        latency: Optional latency measurement to add
        error: Whether an error occurred with this model
    """
    with metrics_lock:
        if provider_model not in MODEL_METRICS:
            MODEL_METRICS[provider_model] = {'latency': [], 'errors': 0, 'cost_per_token': 0.0005, 'last_used': 0}
        
        metrics = MODEL_METRICS[provider_model]
        
        if latency is not None:
            metrics['latency'].append(latency)
            # Keep only the last 100 latency measurements
            if len(metrics['latency']) > 100:
                metrics['latency'] = metrics['latency'][-100:]
        
        if error:
            metrics['errors'] += 1
        
        metrics['last_used'] = time.time()

def select_provider_and_model(query, user_preference=None, cost_sensitive=False):
    """
    Intelligently select the best provider and model based on the query content,
    performance metrics, and user preferences.
    
    Args:
        query: The user's query text
        user_preference: Optional user-specified provider/model
        cost_sensitive: Whether to prioritize lower cost models
    
    Returns:
        tuple: (provider_name, model_name)
    """
    # If user specified a preference and it's valid, use it
    if user_preference:
        parts = user_preference.split('/')
        if len(parts) == 2:
            provider, model = parts
            if f"{provider}/{model}" in MODEL_METRICS:
                return provider, model
    
    # Analyze query to determine required capabilities
    required_capabilities = {}
    
    # Check for code-related content
    if re.search(r'(code|function|programming|algorithm|bug|error|syntax|compile)', query, re.IGNORECASE):
        required_capabilities['coding'] = 1.5  # Weight coding higher for code-related queries
    
    # Check for reasoning/logic tasks
    if re.search(r'(explain|why|how|reason|logic|analyze|solve|problem)', query, re.IGNORECASE):
        required_capabilities['reasoning'] = 1.2
    
    # Check for creative tasks
    if re.search(r'(create|generate|write|story|poem|creative|imagine)', query, re.IGNORECASE):
        required_capabilities['creativity'] = 1.3
    
    # Check for knowledge-intensive queries
    if re.search(r'(what is|who is|when|where|history|science|facts|information)', query, re.IGNORECASE):
        required_capabilities['knowledge'] = 1.4
    
    # If no specific capabilities detected, use balanced weights
    if not required_capabilities:
        required_capabilities = {
            'coding': 1.0,
            'reasoning': 1.0,
            'creativity': 1.0,
            'knowledge': 1.0
        }
    
    # Calculate scores for each model
    model_scores = {}
    for model_key, capabilities in MODEL_CAPABILITIES.items():
        # Capability score
        capability_score = sum(capabilities.get(cap, 0) * weight 
                              for cap, weight in required_capabilities.items())
        
        # Performance score based on latency and errors
        metrics = MODEL_METRICS[model_key]
        avg_latency = sum(metrics['latency']) / max(len(metrics['latency']), 1)
        latency_score = 1.0 / (1.0 + avg_latency / 5.0)  # Normalize latency impact
        error_rate = metrics['errors'] / 100.0  # Simple error rate calculation
        reliability_score = 1.0 - min(error_rate, 0.9)  # Cap at 0.9 to avoid zero
        
        # Cost factor (higher for cost-sensitive requests)
        cost_factor = 1.0 / (1.0 + metrics['cost_per_token'] * 1000) if cost_sensitive else 0.9
        
        # Load balancing factor (slightly prefer less recently used models)
        time_since_used = max(0, time.time() - metrics['last_used']) / 60.0  # minutes
        load_factor = min(1.0, time_since_used / 10.0)  # Max out at 10 minutes
        
        # Calculate final score
        model_scores[model_key] = (
            capability_score * 0.5 +
            latency_score * 0.2 +
            reliability_score * 0.15 +
            cost_factor * 0.1 +
            load_factor * 0.05
        )
    
    # Select the model with the highest score
    best_model = max(model_scores.items(), key=lambda x: x[1])[0]
    provider, model = best_model.split('/')
    
    logger.info(f"Selected provider: {provider}, model: {model} for query")
    return provider, model
