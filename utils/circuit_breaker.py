import pybreaker
import logging
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from functools import wraps
from utils.logging import logger

# Create circuit breakers for each provider
circuit_breakers = {
    'openai': pybreaker.CircuitBreaker(
        fail_max=5,
        reset_timeout=30,
        exclude=[KeyboardInterrupt, SystemExit],
        name='openai'
    ),
    'anthropic': pybreaker.CircuitBreaker(
        fail_max=5,
        reset_timeout=30,
        exclude=[KeyboardInterrupt, SystemExit],
        name='anthropic'
    ),
    'deepseek': pybreaker.CircuitBreaker(
        fail_max=5,
        reset_timeout=30,
        exclude=[KeyboardInterrupt, SystemExit],
        name='deepseek'
    )
}

# Set up listeners for circuit breaker events
class CircuitBreakerListener(pybreaker.CircuitBreakerListener):
    def __init__(self, provider_name):
        self.provider_name = provider_name
    
    def state_change(self, cb, old_state, new_state):
        logger.warning(f"Circuit breaker for {self.provider_name} changed from {old_state} to {new_state}")
    
    def failure(self, cb, exc):
        logger.error(f"Circuit breaker for {self.provider_name} recorded a failure: {str(exc)}")
    
    def success(self, cb):
        logger.info(f"Circuit breaker for {self.provider_name} recorded a success")

# Add listeners to circuit breakers
for provider, cb in circuit_breakers.items():
    cb.add_listener(CircuitBreakerListener(provider))

def with_circuit_breaker(provider_name):
    """
    Decorator to apply circuit breaker pattern to a function.
    
    Args:
        provider_name: The name of the provider to use for the circuit breaker
        
    Returns:
        Decorated function with circuit breaker
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            circuit_breaker = circuit_breakers.get(provider_name.lower())
            if not circuit_breaker:
                # If no circuit breaker exists for this provider, create one
                circuit_breaker = pybreaker.CircuitBreaker(
                    fail_max=5,
                    reset_timeout=30,
                    exclude=[KeyboardInterrupt, SystemExit],
                    name=provider_name.lower()
                )
                circuit_breaker.add_listener(CircuitBreakerListener(provider_name))
                circuit_breakers[provider_name.lower()] = circuit_breaker
            
            return circuit_breaker(func)(*args, **kwargs)
        return wrapper
    return decorator

def with_retry(max_attempts=3, min_wait=1, max_wait=10):
    """
    Decorator to apply retry pattern with exponential backoff to a function.
    
    Args:
        max_attempts: Maximum number of retry attempts
        min_wait: Minimum wait time between retries in seconds
        max_wait: Maximum wait time between retries in seconds
        
    Returns:
        Decorated function with retry logic
    """
    def decorator(func):
        @wraps(func)
        @retry(
            stop=stop_after_attempt(max_attempts),
            wait=wait_exponential(multiplier=1, min=min_wait, max=max_wait),
            retry=retry_if_exception_type((ConnectionError, TimeoutError)),
            before_sleep=lambda retry_state: logger.warning(
                f"Retrying {func.__name__} after {retry_state.outcome.exception()}, "
                f"attempt {retry_state.attempt_number}/{max_attempts}"
            )
        )
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        return wrapper
    return decorator

def with_fallback(fallback_function):
    """
    Decorator to apply fallback pattern to a function.
    
    Args:
        fallback_function: Function to call if the primary function fails
        
    Returns:
        Decorated function with fallback logic
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.warning(f"Primary function {func.__name__} failed: {str(e)}. Using fallback.")
                return fallback_function(*args, **kwargs)
        return wrapper
    return decorator
