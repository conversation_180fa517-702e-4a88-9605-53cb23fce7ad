"""
Thread pruning utilities for OpenAI Assistant API.

This module provides functions to manage thread message length and reduce token consumption
for OpenAI Assistant API threads. It implements various pruning strategies to keep threads
at an optimal size.
"""
import os
import time
import json
import asyncio
from typing import Dict, Any, List, Optional
from openai import AsyncOpenA<PERSON>
from utils.logging import logger
from utils.async_redis_client import get_async_redis_client

class ThreadPruningConfig:
    """Configuration for thread pruning."""
    
    def __init__(self):
        # Default values
        self.enabled = True
        self.max_messages = 8
        self.target_length = 6
        self.strategy = "simple"  # "simple", "role_based", "conversation", "content"
        self.pre_request_enabled = True
        self.post_response_enabled = False
        self.background_interval = 3600  # 1 hour
        self.min_time_between_prunes = 300  # 5 minutes
        
        # Load from environment variables
        if os.getenv("THREAD_PRUNING_ENABLED", "true").lower() == "false":
            self.enabled = False
            
        if os.getenv("THREAD_MAX_MESSAGES"):
            try:
                self.max_messages = int(os.getenv("THREAD_MAX_MESSAGES"))
            except ValueError:
                pass
                
        if os.getenv("THREAD_TARGET_LENGTH"):
            try:
                self.target_length = int(os.getenv("THREAD_TARGET_LENGTH"))
            except ValueError:
                pass
                
        if os.getenv("THREAD_PRUNING_STRATEGY"):
            self.strategy = os.getenv("THREAD_PRUNING_STRATEGY")
            
        if os.getenv("THREAD_PRE_REQUEST_PRUNE", "true").lower() == "false":
            self.pre_request_enabled = False
            
        if os.getenv("THREAD_POST_RESPONSE_PRUNE", "false").lower() == "true":
            self.post_response_enabled = True
            
        if os.getenv("THREAD_BACKGROUND_PRUNE_INTERVAL"):
            try:
                self.background_interval = int(os.getenv("THREAD_BACKGROUND_PRUNE_INTERVAL"))
            except ValueError:
                pass
                
        if os.getenv("THREAD_MIN_TIME_BETWEEN_PRUNES"):
            try:
                self.min_time_between_prunes = int(os.getenv("THREAD_MIN_TIME_BETWEEN_PRUNES"))
            except ValueError:
                pass
    
    def to_dict(self):
        """Convert config to dictionary."""
        return {
            "enabled": self.enabled,
            "max_messages": self.max_messages,
            "target_length": self.target_length,
            "strategy": self.strategy,
            "pre_request_enabled": self.pre_request_enabled,
            "post_response_enabled": self.post_response_enabled,
            "background_interval": self.background_interval,
            "min_time_between_prunes": self.min_time_between_prunes
        }


async def prune_thread_messages(thread_id: str, max_messages: int = 6, client: Optional[AsyncOpenAI] = None) -> Dict[str, Any]:
    """
    Prune a thread to keep only the most recent messages.
    
    Args:
        thread_id: The thread ID to prune
        max_messages: Maximum number of messages to keep (default: 6)
        client: Optional OpenAI client instance
        
    Returns:
        Dict containing pruning statistics
    """
    if client is None:
        client = AsyncOpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_API_URL', 'https://api.openai.com/v1'),
            default_headers={"OpenAI-Beta": "assistants=v2"}
        )
    
    # Get all messages in the thread
    try:
        messages = await client.beta.threads.messages.list(
            thread_id=thread_id,
            order="desc",  # Get newest first
            limit=100  # Adjust as needed
        )
        
        logger.info(f"Retrieved {len(messages.data)} messages from thread {thread_id}")
        
        # If we have more than max_messages, delete the oldest ones
        if len(messages.data) > max_messages:
            # Sort by created_at (oldest first)
            sorted_messages = sorted(messages.data, key=lambda x: x.created_at)
            
            # Get the messages to delete (oldest ones)
            messages_to_delete = sorted_messages[:-max_messages]
            
            # Calculate token usage before pruning (rough estimate)
            token_count_before = sum(len(msg.content[0].text.value.split()) * 1.3 
                                    for msg in messages.data if msg.content)
            
            # Track message types being deleted
            user_messages_deleted = 0
            assistant_messages_deleted = 0
            
            # Delete each message
            for message in messages_to_delete:
                try:
                    await client.beta.threads.messages.delete(
                        thread_id=thread_id,
                        message_id=message.id
                    )
                    
                    # Track message role
                    if message.role == "user":
                        user_messages_deleted += 1
                    elif message.role == "assistant":
                        assistant_messages_deleted += 1
                        
                    logger.info(f"Deleted message {message.content} from thread {thread_id}")
                except Exception as e:
                    logger.error(f"Error deleting message {message.id}: {str(e)}")
            
            # Get updated messages to calculate token savings
            updated_messages = await client.beta.threads.messages.list(
                thread_id=thread_id,
                order="desc"
            )
            
            token_count_after = sum(len(msg.content[0].text.value.split()) * 1.3 
                                   for msg in updated_messages.data if msg.content)
            
            tokens_saved = token_count_before - token_count_after
            
            return {
                "thread_id": thread_id,
                "messages_before": len(messages.data),
                "messages_after": len(updated_messages.data),
                "messages_deleted": len(messages_to_delete),
                "user_messages_deleted": user_messages_deleted,
                "assistant_messages_deleted": assistant_messages_deleted,
                "estimated_tokens_before": int(token_count_before),
                "estimated_tokens_after": int(token_count_after),
                "estimated_tokens_saved": int(tokens_saved),
                "pruning_timestamp": time.time()
            }
        else:
            logger.info(f"Thread {thread_id} has {len(messages.data)} messages, no pruning needed (max: {max_messages})")
            return {
                "thread_id": thread_id,
                "messages_before": len(messages.data),
                "messages_after": len(messages.data),
                "messages_deleted": 0,
                "pruning_needed": False
            }
    
    except Exception as e:
        logger.error(f"Error pruning thread {thread_id}: {str(e)}")
        return {
            "thread_id": thread_id,
            "error": str(e),
            "pruning_success": False
        }


async def role_based_pruning(thread_id: str, max_messages: int = 6, client: Optional[AsyncOpenAI] = None) -> Dict[str, Any]:
    """
    Prune a thread while maintaining a balance of user and assistant messages.
    
    Args:
        thread_id: The thread ID to prune
        max_messages: Maximum number of messages to keep
        client: Optional OpenAI client instance
    """
    if client is None:
        client = AsyncOpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_API_URL', 'https://api.openai.com/v1'),
            default_headers={"OpenAI-Beta": "assistants=v2"}
        )
    
    # Get all messages in the thread
    messages = await client.beta.threads.messages.list(thread_id=thread_id)
    
    if len(messages.data) <= max_messages:
        return {"thread_id": thread_id, "messages_deleted": 0, "pruning_needed": False}
    
    # Separate messages by role
    user_messages = [msg for msg in messages.data if msg.role == "user"]
    assistant_messages = [msg for msg in messages.data if msg.role == "assistant"]
    
    # Sort each list by created_at (oldest first)
    user_messages.sort(key=lambda x: x.created_at)
    assistant_messages.sort(key=lambda x: x.created_at)
    
    # Calculate how many messages to keep from each role
    # Aim for roughly equal numbers, but adjust based on the current distribution
    total_to_delete = len(messages.data) - max_messages
    user_ratio = len(user_messages) / len(messages.data)
    
    user_to_delete = min(len(user_messages) - 1, int(total_to_delete * user_ratio))
    assistant_to_delete = total_to_delete - user_to_delete
    
    # Ensure we don't delete all messages of either type
    if user_to_delete >= len(user_messages):
        user_to_delete = max(0, len(user_messages) - 1)
    if assistant_to_delete >= len(assistant_messages):
        assistant_to_delete = max(0, len(assistant_messages) - 1)
    
    # Get messages to delete
    user_to_delete_list = user_messages[:user_to_delete]
    assistant_to_delete_list = assistant_messages[:assistant_to_delete]
    messages_to_delete = user_to_delete_list + assistant_to_delete_list
    
    # Delete messages
    deleted_count = 0
    for message in messages_to_delete:
        try:
            await client.beta.threads.messages.delete(
                thread_id=thread_id,
                message_id=message.id
            )
            deleted_count += 1
        except Exception as e:
            logger.error(f"Error deleting message {message.id}: {str(e)}")
    
    return {
        "thread_id": thread_id,
        "messages_deleted": deleted_count,
        "user_messages_deleted": len(user_to_delete_list),
        "assistant_messages_deleted": len(assistant_to_delete_list),
        "pruning_success": True
    }


async def conversation_aware_pruning(thread_id: str, max_exchanges: int = 3, client: Optional[AsyncOpenAI] = None) -> Dict[str, Any]:
    """
    Prune a thread while preserving complete conversation exchanges.
    
    Args:
        thread_id: The thread ID to prune
        max_exchanges: Maximum number of back-and-forth exchanges to keep
        client: Optional OpenAI client instance
    """
    if client is None:
        client = AsyncOpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_API_URL', 'https://api.openai.com/v1'),
            default_headers={"OpenAI-Beta": "assistants=v2"}
        )
    
    # Get all messages in the thread
    messages = await client.beta.threads.messages.list(
        thread_id=thread_id,
        order="desc"  # Newest first
    )
    
    # Group messages into exchanges (user question + assistant response)
    exchanges = []
    current_exchange = []
    
    for msg in messages.data:
        current_exchange.append(msg)
        
        # If we have a user message followed by an assistant message, that's a complete exchange
        if len(current_exchange) >= 2 and current_exchange[0].role == "assistant" and current_exchange[1].role == "user":
            exchanges.append(current_exchange)
            current_exchange = []
    
    # Add any remaining messages as a partial exchange
    if current_exchange:
        exchanges.append(current_exchange)
    
    # If we don't have more exchanges than the max, no pruning needed
    if len(exchanges) <= max_exchanges:
        return {"thread_id": thread_id, "messages_deleted": 0, "pruning_needed": False}
    
    # Identify messages to keep (from newest exchanges)
    messages_to_keep = []
    for i in range(min(max_exchanges, len(exchanges))):
        messages_to_keep.extend(exchanges[i])
    
    messages_to_keep_ids = {msg.id for msg in messages_to_keep}
    messages_to_delete = [msg for msg in messages.data if msg.id not in messages_to_keep_ids]
    
    # Delete messages
    deleted_count = 0
    for message in messages_to_delete:
        try:
            await client.beta.threads.messages.delete(
                thread_id=thread_id,
                message_id=message.id
            )
            deleted_count += 1
        except Exception as e:
            logger.error(f"Error deleting message {message.id}: {str(e)}")
    
    return {
        "thread_id": thread_id,
        "messages_deleted": deleted_count,
        "exchanges_kept": min(max_exchanges, len(exchanges)),
        "exchanges_deleted": max(0, len(exchanges) - max_exchanges),
        "pruning_success": True
    }


async def get_cached_thread_length(thread_id: str) -> int:
    """
    Get the cached thread length, or fetch it if not cached.
    
    Args:
        thread_id: The thread ID
        
    Returns:
        The number of messages in the thread
    """
    redis_client = await get_async_redis_client()
    
    # Try to get cached length
    cached_length = await redis_client.get(f"thread_length:{thread_id}")
    
    if cached_length is not None:
        return int(cached_length)
    
    # If not cached, fetch from API
    client = AsyncOpenAI(
        api_key=os.getenv('OPENAI_API_KEY'),
        base_url=os.getenv('OPENAI_API_URL', 'https://api.openai.com/v1'),
        default_headers={"OpenAI-Beta": "assistants=v2"}
    )
    
    messages = await client.beta.threads.messages.list(thread_id=thread_id)
    length = len(messages.data)
    
    # Cache the length (expire after 1 hour)
    await redis_client.set(f"thread_length:{thread_id}", length, ex=3600)
    
    return length


class ThreadPruningManager:
    """Manager for thread pruning strategies."""
    
    def __init__(self, client=None):
        self.client = client or AsyncOpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_API_URL', 'https://api.openai.com/v1'),
            default_headers={"OpenAI-Beta": "assistants=v2"}
        )
        self.redis_client = None
        self.config = ThreadPruningConfig()
    
    async def get_redis_client(self):
        """Get or create Redis client."""
        if self.redis_client is None:
            self.redis_client = await get_async_redis_client()
        return self.redis_client
    
    async def should_prune(self, thread_id: str) -> bool:
        """Check if a thread should be pruned based on message count and last prune time."""
        if not self.config.enabled:
            return False
        
        try:
            # Check message count
            messages = await self.client.beta.threads.messages.list(
                thread_id=thread_id,
                limit=self.config.max_messages + 1,
                order="asc"
            )
            
            if len(messages.data) <= self.config.max_messages:
                return False
            
            # Check when the thread was last pruned
            redis = await self.get_redis_client()
            last_pruned = await redis.hget(f"thread_pruning:{thread_id}", "last_pruned")
            
            if last_pruned:
                # Don't prune if it was pruned recently
                time_since_last_prune = time.time() - float(last_pruned)
                if time_since_last_prune < self.config.min_time_between_prunes:
                    logger.info(f"Don't prune if it was pruned recently")
                    return False
            
            return True
        except Exception as e:
            logger.error(f"Error checking if thread {thread_id} should be pruned: {str(e)}")
            return False
    
    async def prune_if_needed(self, thread_id: str, force: bool = False) -> Dict[str, Any]:
        """Prune a thread if needed or forced."""
        if not self.config.enabled and not force:
            return {"thread_id": thread_id, "pruning_needed": False, "pruning_enabled": False}
        
        if not force and not await self.should_prune(thread_id):
            return {"thread_id": thread_id, "pruning_needed": False}
        
        try:
            # Choose pruning strategy based on configuration
            if self.config.strategy == "role_based":
                result = await role_based_pruning(
                    thread_id, 
                    max_messages=self.config.target_length,
                    client=self.client
                )
            elif self.config.strategy == "conversation":
                # For conversation strategy, max_exchanges is roughly half of target_length
                max_exchanges = max(1, self.config.target_length // 2)
                result = await conversation_aware_pruning(
                    thread_id, 
                    max_exchanges=max_exchanges,
                    client=self.client
                )
            else:  # Default to simple pruning
                result = await prune_thread_messages(
                    thread_id, 
                    max_messages=self.config.target_length,
                    client=self.client
                )
            
            # Update pruning metrics
            if result.get("messages_deleted", 0) > 0:
                redis = await self.get_redis_client()
                await redis.hset(
                    f"thread_pruning:{thread_id}",
                    mapping={
                        "last_pruned": time.time(),
                        "messages_deleted": result.get("messages_deleted", 0),
                        "messages_before": result.get("messages_before", 0),
                        "messages_after": result.get("messages_after", 0),
                        "estimated_tokens_saved": result.get("estimated_tokens_saved", 0)
                    }
                )
                
                # Set an expiration on the metrics (e.g., 30 days)
                await redis.expire(f"thread_pruning:{thread_id}", 60 * 60 * 24 * 30)
            
            return result
        except Exception as e:
            logger.error(f"Error pruning thread {thread_id}: {str(e)}")
            return {"thread_id": thread_id, "error": str(e), "pruning_success": False}
    
    async def pre_request_prune(self, thread_id: str) -> Dict[str, Any]:
        """Prune before processing a request."""
        if self.config.pre_request_enabled:
            return await self.prune_if_needed(thread_id)
        return {"thread_id": thread_id, "pruning_needed": False, "pre_request_enabled": False}
    
    async def post_response_prune(self, thread_id: str) -> Dict[str, Any]:
        """Prune after processing a response."""
        if self.config.post_response_enabled:
            return await self.prune_if_needed(thread_id)
        return {"thread_id": thread_id, "pruning_needed": False, "post_response_enabled": False}
    
    async def start_background_pruning(self):
        """Start background pruning task."""
        if not self.config.enabled:
            logger.info("Background pruning disabled")
            return
        
        asyncio.create_task(self._background_pruning_task())
        logger.info(f"Background pruning task started with interval {self.config.background_interval} seconds")
    
    async def _background_pruning_task(self):
        """Background task for periodic pruning."""
        while True:
            try:
                # Get all thread IDs from Redis
                redis = await self.get_redis_client()
                thread_keys = await redis.keys("thread:*")
                
                pruned_count = 0
                error_count = 0
                
                for key in thread_keys:
                    thread_id = await redis.get(key)
                    if thread_id:
                        thread_id = thread_id.decode('utf-8') if isinstance(thread_id, bytes) else thread_id
                        logger.info(f"thread id {thread_id}")
                        try:
                            if await self.should_prune(thread_id):
                                result = await self.prune_if_needed(thread_id, force=True)
                                if result.get("messages_deleted", 0) > 0:
                                    pruned_count += 1
                        except Exception as e:
                            logger.error(f"Error in background pruning for thread {thread_id}: {str(e)}")
                            error_count += 1
                
                logger.info(f"Background pruning completed: {pruned_count} threads pruned, {error_count} errors")
                
            except Exception as e:
                logger.error(f"Error in background pruning task: {str(e)}")
            
            # Wait for the next interval
            await asyncio.sleep(self.config.background_interval)


async def get_pruning_metrics(auth0_sub: Optional[str] = None, thread_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Get pruning metrics for a user or thread.
    
    Args:
        auth0_sub: Optional user identifier
        thread_id: Optional thread ID
        
    Returns:
        Dict containing pruning metrics
    """
    redis_client = await get_async_redis_client()
    
    # If auth0_sub is provided but thread_id is not, get thread_id from Redis
    if auth0_sub and not thread_id:
        thread_id = await redis_client.get(f"thread:{auth0_sub}")
        if thread_id:
            thread_id = thread_id.decode('utf-8') if isinstance(thread_id, bytes) else thread_id
    
    if not thread_id:
        return {"error": "No thread ID provided or found"}
    
    # Get pruning metrics from Redis
    metrics = await redis_client.hgetall(f"thread_pruning:{thread_id}")
    
    if not metrics:
        return {"thread_id": thread_id, "pruning_history": False}
    
    # Convert metrics to appropriate types
    for key in ["last_pruned", "messages_deleted", "messages_before", "messages_after", "estimated_tokens_saved"]:
        if key in metrics:
            try:
                if key == "last_pruned":
                    metrics[key] = float(metrics[key])
                else:
                    metrics[key] = int(metrics[key])
            except (ValueError, TypeError):
                pass
    
    return {
        "thread_id": thread_id,
        "pruning_history": True,
        "metrics": metrics
    }
