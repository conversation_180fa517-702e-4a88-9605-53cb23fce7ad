"""
Database-backed functions for S&P 500 data.
These functions use the MySQL database instead of Yahoo Finance API.
"""

import json
import traceback
import asyncio
import time
import random
from typing import Dict, Any, List, Optional
from datetime import datetime
from utils.logging import logger
from utils.redis_client import redis_client
from utils.multi_schema_db_manager import db_manager
from sqlalchemy import text

# Use a constant for the schema name
DEFAULT_SCHEMA = "stock_data"

# Cache version to handle schema changes
SP500_CACHE_VERSION = "v1.2"  # Updated for database version

# Redis cache keys for persistent caching
SP500_REDIS_KEY = "sp500_data_cache"
SP500_REDIS_SECTOR_PREFIX = "sp500_sector_"

# Maximum number of retries for database operations
MAX_DB_RETRIES = 3
# Base delay between retries in seconds
BASE_DB_RETRY_DELAY = 1

async def get_tickers_dividend_from_db(
    limit: int = 500,
    min_dividend_yield: float = 0,
    min_dividend_rate: float = 0,
    sort_by: str = "yield",
    sector: Optional[str] = None,
    indice: Optional[str] = "SPX"
) -> Dict[str, Any]:
    """
    Get dividend information for stocks from the database, filtered by index type.
    
    Args:
        limit: Maximum number of stocks to return (default: all 500)
        min_dividend_yield: Filter for stocks with minimum dividend yield percentage
        min_dividend_rate: Filter for stocks with minimum dividend rate in dollars per share
        sort_by: Sort results by 'yield', 'name', or 'sector'
        sector: Filter by sector (e.g., 'Technology', 'Healthcare', etc.)
        indice: Filter tickers according to type of indices (e.g., 'SPX' for S&P 500, 'IXIC' for Nasdaq Composite)
        
    Returns:
        dict: Dividend data for stocks matching the specified criteria
    """
    logger.info(f"Function call: get_tickers_dividend_from_db(limit={limit}, min_dividend_yield={min_dividend_yield}, min_dividend_rate={min_dividend_rate}, sort_by='{sort_by}', sector={sector}, indice='{indice}')")
    
    try:
        # Validate parameters
        if limit <= 0:
            limit = 500
            logger.debug(f"Adjusted invalid limit to default (500)")
        
        if sort_by not in ["yield", "name", "sector"]:
            sort_by = "yield"
            logger.debug(f"Adjusted invalid sort_by to default ('yield')")
        
        # Helper function to process and filter the data
        def process_and_filter_data(data, min_yield, min_rate, sort_method, sector_filter, limit_val):
            # Apply filters
            filtered_data = data.copy()
            
            # Filter by sector (still done in memory since it's a case-insensitive partial match)
            if sector_filter:
                logger.debug(f"Filtering stocks in sector: {sector_filter}")
                filtered_data = [stock for stock in filtered_data if sector_filter.lower() in stock['sector'].lower()]
            
            # Sort the data
            if sort_method == "yield":
                logger.debug("Sorting stocks by dividend yield (descending)")
                filtered_data.sort(key=lambda x: x['dividend_yield'], reverse=True)
            elif sort_method == "name":
                logger.debug("Sorting stocks by company name")
                filtered_data.sort(key=lambda x: x['name'])
            elif sort_method == "sector":
                logger.debug("Sorting stocks by sector, then by yield")
                filtered_data.sort(key=lambda x: (x['sector'], -x['dividend_yield']))
            
            # Apply limit
            if limit_val < len(filtered_data):
                logger.debug(f"Limiting results to {limit_val} stocks")
                filtered_data = filtered_data[:limit_val]
            
            # Get unique sectors for metadata
            all_sectors = sorted(list(set(stock['sector'] for stock in data)))
            
            # Calculate statistics
            dividend_payers = [s for s in data if s['has_dividends']]
            avg_dividend_yield = sum(s['dividend_yield'] for s in dividend_payers) / len(dividend_payers) if dividend_payers else 0
            
            # Format the response
            logger.info(f"Returning dividend data for {len(filtered_data)} stocks")
            return {
                "stocks": filtered_data,
                "metadata": {
                    "total_stocks": len(data),
                    "filtered_stocks": len(filtered_data),
                    "dividend_payers": len(dividend_payers),
                    "average_dividend_yield": round(avg_dividend_yield, 2),
                    "available_sectors": all_sectors,
                    "timestamp": datetime.now().isoformat(),
                    "cache_version": SP500_CACHE_VERSION,
                    "data_source": "database"  # Indicate data source is database
                }
            }
            
        # No Redis caching for database-backed implementation
        
        # If we get here, we need to fetch the data from the database
        logger.info("S&P 500 data not in cache, fetching from database")
        
        # Create a function to run in a separate thread to avoid blocking
        async def fetch_sp500_data_from_db():
            retry_count = 0
            last_exception = None
            
            while retry_count < MAX_DB_RETRIES:
                try:
                    # Use the multi-schema database manager with the default schema
                    async with await db_manager.get_db_session(DEFAULT_SCHEMA) as session:
                        # Build the SQL query with filtering at the database level
                        query = f"""
                        SELECT 
                            ticker, 
                            name, 
                            sector,
                            dividend_yield, 
                            dividend_rate, 
                            ex_dividend_date, 
                            dividend_date,
                            five_year_avg_dividend_yield,
                            trailing_annual_dividend_rate,
                            trailing_annual_dividend_yield,
                            last_dividend_date,
                            last_dividend_value,
                            indexes
                        FROM 
                            stocks
                        WHERE 
                            indexes LIKE '%{indice}%'
                            AND (dividend_yield >= {min_dividend_yield} OR {min_dividend_yield} = 0)
                            AND (dividend_rate >= {min_dividend_rate} OR {min_dividend_rate} = 0)
                        """
                        
                        logger.debug(f"Executing SQL query with filters: indice='{indice}', min_dividend_yield={min_dividend_yield}, min_dividend_rate={min_dividend_rate}")
                        
                        # Execute the query with a timeout to prevent long-running queries
                        result = await session.execute(text(query))
                        
                        # Process the results
                        all_stocks_data = []
                        for row in result:
                            # Convert string values to appropriate types
                            try:
                                dividend_yield = float(row.dividend_yield) if row.dividend_yield else 0
                            except (ValueError, TypeError):
                                dividend_yield = 0
                                
                            try:
                                dividend_rate = float(row.dividend_rate) if row.dividend_rate else 0
                            except (ValueError, TypeError):
                                dividend_rate = 0
                            
                            # Create stock data entry
                            stock_data = {
                                "ticker": row.ticker,
                                "name": row.name,
                                "sector": row.sector or "Unknown",
                                "dividend_yield": dividend_yield,
                                "dividend_rate": dividend_rate,
                                "ex_dividend_date": row.ex_dividend_date,
                                "dividend_date": row.dividend_date,
                                "five_year_avg_dividend_yield": row.five_year_avg_dividend_yield,
                                "trailing_annual_dividend_rate": row.trailing_annual_dividend_rate,
                                "trailing_annual_dividend_yield": row.trailing_annual_dividend_yield,
                                "last_dividend_date": row.last_dividend_date,
                                "last_dividend_value": row.last_dividend_value,
                                "has_dividends": dividend_rate > 0 or dividend_yield > 0
                            }
                            
                            all_stocks_data.append(stock_data)
                        
                        logger.info(f"Successfully fetched {len(all_stocks_data)} stocks from database")
                    
                    return all_stocks_data
                    
                except Exception as e:
                    retry_count += 1
                    last_exception = e
                    
                    if "Lost connection" in str(e) or "Connection refused" in str(e) or "Connection timed out" in str(e):
                        logger.warning(f"Database connection error (attempt {retry_count}/{MAX_DB_RETRIES}): {str(e)}")
                        
                        if retry_count < MAX_DB_RETRIES:
                            # Calculate exponential backoff with jitter
                            delay = BASE_DB_RETRY_DELAY * (2 ** (retry_count - 1)) + random.uniform(0, 0.5)
                            logger.info(f"Retrying database operation in {delay:.2f} seconds...")
                            time.sleep(delay)
                        else:
                            logger.error(f"Error fetching S&P 500 data from database after {MAX_DB_RETRIES} attempts: {str(e)}")
                            logger.debug(f"Database fetch traceback: {traceback.format_exc()}")
                            return []
                    else:
                        logger.error(f"Error fetching S&P 500 data from database: {str(e)}")
                        logger.debug(f"Database fetch traceback: {traceback.format_exc()}")
                        return []
            
            # If we've exhausted all retries
            if last_exception:
                logger.error(f"Database operation failed after {MAX_DB_RETRIES} attempts: {str(last_exception)}")
                return []
            
            return []
        
        # Run the fetch function
        try:
            sp500_data = await fetch_sp500_data_from_db()
        except Exception as e:
            logger.error(f"Error executing database query in executor: {str(e)}")
            sp500_data = []
        
        # If database connection failed, return empty result with error message
        if not sp500_data:
            logger.warning("Database connection failed, returning empty result")
            return {
                "stocks": [],
                "metadata": {
                    "total_stocks": 0,
                    "filtered_stocks": 0,
                    "dividend_payers": 0,
                    "average_dividend_yield": 0,
                    "available_sectors": [],
                    "timestamp": datetime.now().isoformat(),
                    "cache_version": SP500_CACHE_VERSION,
                    "data_source": "database",
                    "error": "Database connection failed. Please try again later."
                }
            }
        
        # No Redis caching for database-backed implementation
        
        # Process the data with the user's filters (now only for sector filtering and sorting)
        result = process_and_filter_data(sp500_data, min_dividend_yield, min_dividend_rate, sort_by, sector, limit)
        
        # ALWAYS enforce a hard limit of 20 stocks maximum to avoid response size issues
        if "stocks" in result:
            original_count = len(result["stocks"])
            if original_count > 20:
                logger.info(f"Hard limiting stocks in result from {original_count} to 20 to avoid response size issues")
                result["stocks"] = result["stocks"][:20]
                result["metadata"]["note"] = "Results limited to 20 stocks to avoid response size issues"
                # Update the filtered_stocks count in metadata
                result["metadata"]["filtered_stocks"] = 20
        
        return result
    except Exception as e:
        logger.error(f"Error in get_tickers_dividend_from_db function: {str(e)}")
        logger.debug(f"Dividend rates function traceback: {traceback.format_exc()}")
        return {
            "error": str(e),
            "stocks": [],
            "timestamp": datetime.now().isoformat()
        }
