import asyncio
from utils.function_registry import function_registry
import utils.sample_functions  # This will register the functions
from datetime import datetime

async def main():
    # Print all registered functions
    print("Registered functions:")
    for schema in function_registry.get_function_schemas():
        print(f"- {schema['name']}: {schema['description']}")
    
    # Test the dividend function with a very small limit
    if function_registry.has_function("get_tickers_dividend"):
        print("\nTesting get_tickers_dividend function with limit=3...")
        
        # Create a mock result to avoid API rate limits during testing
        mock_result = {
            "stocks": [
                {
                    "ticker": "AAPL",
                    "name": "Apple Inc.",
                    "sector": "Information Technology",
                    "dividend_yield": 0.5,
                    "dividend_rate": 0.94,
                    "annual_dividends": {"2020": 0.82, "2021": 0.85, "2022": 0.91, "2023": 0.94, "2024": 0.24},
                    "has_dividends": True
                },
                {
                    "ticker": "MSFT",
                    "name": "Microsoft Corporation",
                    "sector": "Information Technology",
                    "dividend_yield": 0.8,
                    "dividend_rate": 2.72,
                    "annual_dividends": {"2020": 2.04, "2021": 2.24, "2022": 2.48, "2023": 2.72, "2024": 0.68},
                    "has_dividends": True
                },
                {
                    "ticker": "JNJ",
                    "name": "Johnson & Johnson",
                    "sector": "Healthcare",
                    "dividend_yield": 3.2,
                    "dividend_rate": 4.65,
                    "annual_dividends": {"2020": 3.98, "2021": 4.19, "2022": 4.45, "2023": 4.65, "2024": 1.19},
                    "has_dividends": True
                }
            ],
            "metadata": {
                "total_stocks": 500,
                "filtered_stocks": 3,
                "dividend_payers": 380,
                "average_dividend_yield": 2.15,
                "available_sectors": ["Communication Services", "Consumer Discretionary", "Consumer Staples", "Energy", "Financials", "Healthcare", "Industrials", "Information Technology", "Materials", "Real Estate", "Utilities"],
                "timestamp": datetime.now().isoformat()
            }
        }
        
        # Use mock data instead of calling the actual function
        result = mock_result
        
        # Uncomment the following to test the actual function (may hit rate limits)
        # result = await function_registry.execute_function(
        #     "get_tickers_dividend", 
        #     limit=3,
        #     min_dividend_yield=1.0,
        #     min_dividend_rate=2.0,  # Filter for stocks with dividend rate >= $2.0 per share
        #     sort_by="yield"
        # )
        
        # Print the results
        print(f"\nResults: {len(result.get('stocks', []))} stocks found")
        for stock in result.get('stocks', [])[:3]:  # Show top 3 stocks
            print(f"- {stock['ticker']}: {stock['name']} ({stock['sector']}) - Dividend Yield: {stock['dividend_yield']}%, Dividend Rate: ${stock['dividend_rate']}/share")
        
        # Print metadata
        print("\nMetadata:")
        for key, value in result.get('metadata', {}).items():
            if key != 'available_sectors':  # Skip the long list of sectors
                print(f"- {key}: {value}")
    else:
        print("Function get_tickers_dividend not found in registry")

if __name__ == "__main__":
    asyncio.run(main())
