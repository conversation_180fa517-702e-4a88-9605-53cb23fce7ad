import asyncio
import time
import json
from utils.function_registry import function_registry
from utils.redis_client import redis_client
import utils.sample_functions  # This will register the functions

async def test_cache_fix():
    print("Testing dividend rates cache fix with Redis persistence...")
    
    # Verify the function is registered
    if not function_registry.has_function("get_tickers_dividend"):
        print("Function get_tickers_dividend not found in registry")
        return
    
    # Clear Redis cache to ensure a fresh test
    try:
        print("Clearing Redis cache for a fresh test...")
        redis_client.delete("sp500_data_cache")
        keys = redis_client.keys("sp500_sector_*")
        if keys:
            redis_client.delete(*keys)
        print("Redis cache cleared")
    except Exception as e:
        print(f"Error clearing Redis cache: {str(e)}")
    
    # First run - this will hit the API and cache the results
    print("\nFirst run (min_dividend_yield=1.0):")
    start_time = time.time()
    result = await function_registry.execute_function(
        "get_tickers_dividend", 
        limit=5,
        min_dividend_yield=1.0,
        sort_by="yield"
    )
    end_time = time.time()
    
    # Print execution time and basic stats
    print(f"Execution time: {end_time - start_time:.2f} seconds")
    print(f"Results: {len(result.get('stocks', []))} stocks found")
    print(f"Cache version: {result.get('metadata', {}).get('cache_version', 'N/A')}")
    
    # Verify Redis cache was populated
    try:
        redis_data = redis_client.get("sp500_data_cache")
        if redis_data:
            cache_data = json.loads(redis_data)
            print(f"Redis cache populated with {len(cache_data.get('data', []))} stocks")
            print(f"Redis cache version: {cache_data.get('version', 'N/A')}")
        else:
            print("Redis cache was not populated")
    except Exception as e:
        print(f"Error checking Redis cache: {str(e)}")
    
    # Wait a moment
    print("\nWaiting 2 seconds before second run...")
    await asyncio.sleep(2)
    
    # Second run - with different filtering parameters
    # This should use the Redis cache with our fix
    print("\nSecond run (min_dividend_yield=3.0):")
    start_time = time.time()
    result = await function_registry.execute_function(
        "get_tickers_dividend", 
        limit=5,
        min_dividend_yield=3.0,  # Different filter
        sort_by="yield"
    )
    end_time = time.time()
    
    # Print execution time and basic stats
    print(f"Execution time: {end_time - start_time:.2f} seconds")
    print(f"Results: {len(result.get('stocks', []))} stocks found")
    print(f"Cache version: {result.get('metadata', {}).get('cache_version', 'N/A')}")
    
    # Print top 5 results from the last query
    print("\nTop 5 stocks with dividend yield >= 3.0%:")
    for i, stock in enumerate(result.get('stocks', [])[:5]):
        print(f"{i+1}. {stock['ticker']}: {stock['name']} - Yield: {stock['dividend_yield']}%, Rate: ${stock['dividend_rate']}/share")
    
    # Test with a sector filter
    print("\nThird run (sector='Technology', min_dividend_yield=1.0):")
    start_time = time.time()
    result = await function_registry.execute_function(
        "get_tickers_dividend", 
        limit=5,
        min_dividend_yield=1.0,
        sector="Technology",
        sort_by="yield"
    )
    end_time = time.time()
    
    # Print execution time and basic stats
    print(f"Execution time: {end_time - start_time:.2f} seconds")
    print(f"Results: {len(result.get('stocks', []))} stocks found")
    print(f"Cache version: {result.get('metadata', {}).get('cache_version', 'N/A')}")
    
    # Print top 5 results from the sector query
    print("\nTop 5 Technology stocks with dividend yield >= 1.0%:")
    for i, stock in enumerate(result.get('stocks', [])[:5]):
        print(f"{i+1}. {stock['ticker']}: {stock['name']} - Yield: {stock['dividend_yield']}%, Rate: ${stock['dividend_rate']}/share")
    
    # Simulate a server restart by clearing in-memory cache but keeping Redis cache
    print("\nSimulating server restart by clearing in-memory cache...")
    utils.sample_functions.SP500_DATA_CACHE.clear()
    utils.sample_functions.SP500_SECTOR_CACHE.clear()
    utils.sample_functions.SP500_TICKERS_CACHE.clear()
    print("In-memory cache cleared")
    
    # Fourth run after "server restart" - should use Redis cache
    print("\nFourth run after 'server restart' (min_dividend_yield=2.0):")
    start_time = time.time()
    result = await function_registry.execute_function(
        "get_tickers_dividend", 
        limit=5,
        min_dividend_yield=2.0,
        sort_by="yield"
    )
    end_time = time.time()
    
    # Print execution time and basic stats
    print(f"Execution time: {end_time - start_time:.2f} seconds")
    print(f"Results: {len(result.get('stocks', []))} stocks found")
    print(f"Cache version: {result.get('metadata', {}).get('cache_version', 'N/A')}")
    
    print("\nCache test completed successfully!")

if __name__ == "__main__":
    asyncio.run(test_cache_fix())
