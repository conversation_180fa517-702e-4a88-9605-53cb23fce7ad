-- Add indexes for SEC filings and stocks tables

-- For SECFiling.ticker (may already exist as it's a foreign key)
-- Check if index exists
SET @index_exists_sec_filings_ticker = (
    SELECT COUNT(*)
    FROM information_schema.statistics
    WHERE table_schema = 'stock_data'
    AND table_name = 'sec_filings'
    AND index_name = 'idx_sec_filings_ticker'
);

-- Create the index if it doesn't exist
SET @create_index_sec_filings_ticker = IF(
    @index_exists_sec_filings_ticker = 0,
    'CREATE INDEX idx_sec_filings_ticker ON stock_data.sec_filings(ticker)',
    'SELECT "Index idx_sec_filings_ticker already exists"'
);

PREPARE stmt FROM @create_index_sec_filings_ticker;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- For Stock.ticker (may already exist as it's the primary key)
-- Check if index exists
SET @index_exists_stocks_ticker = (
    SELECT COUNT(*)
    FROM information_schema.statistics
    WHERE table_schema = 'stock_data'
    AND table_name = 'stocks'
    AND index_name = 'idx_stocks_ticker'
);

-- Create the index if it doesn't exist
SET @create_index_stocks_ticker = IF(
    @index_exists_stocks_ticker = 0,
    'CREATE INDEX idx_stocks_ticker ON stock_data.stocks(ticker)',
    'SELECT "Index idx_stocks_ticker already exists"'
);

PREPARE stmt FROM @create_index_stocks_ticker;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- For Stock.name (for the search condition)
-- Check if index exists
SET @index_exists_stocks_name = (
    SELECT COUNT(*)
    FROM information_schema.statistics
    WHERE table_schema = 'stock_data'
    AND table_name = 'stocks'
    AND index_name = 'idx_stocks_name'
);

-- Create the index if it doesn't exist
SET @create_index_stocks_name = IF(
    @index_exists_stocks_name = 0,
    'CREATE INDEX idx_stocks_name ON stock_data.stocks(name)',
    'SELECT "Index idx_stocks_name already exists"'
);

PREPARE stmt FROM @create_index_stocks_name;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- For the LIKE operations with ticker_search
-- Check if index exists
SET @index_exists_sec_filings_filing_type = (
    SELECT COUNT(*)
    FROM information_schema.statistics
    WHERE table_schema = 'stock_data'
    AND table_name = 'sec_filings'
    AND index_name = 'idx_sec_filings_filing_type'
);

-- Create the index if it doesn't exist
SET @create_index_sec_filings_filing_type = IF(
    @index_exists_sec_filings_filing_type = 0,
    'CREATE INDEX idx_sec_filings_filing_type ON stock_data.sec_filings(filing_type)',
    'SELECT "Index idx_sec_filings_filing_type already exists"'
);

PREPARE stmt FROM @create_index_sec_filings_filing_type;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if index exists
SET @index_exists_sec_filings_filing_date = (
    SELECT COUNT(*)
    FROM information_schema.statistics
    WHERE table_schema = 'stock_data'
    AND table_name = 'sec_filings'
    AND index_name = 'idx_sec_filings_filing_date'
);

-- Create the index if it doesn't exist
SET @create_index_sec_filings_filing_date = IF(
    @index_exists_sec_filings_filing_date = 0,
    'CREATE INDEX idx_sec_filings_filing_date ON stock_data.sec_filings(filing_date)',
    'SELECT "Index idx_sec_filings_filing_date already exists"'
);

PREPARE stmt FROM @create_index_sec_filings_filing_date;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
