-- Create schema registry table
-- This table is used to track schemas in the database

-- Create the schema registry table
CREATE TABLE IF NOT EXISTS schema_registry (
    id INT AUTO_INCREMENT PRIMARY KEY,
    schema_name VARCHAR(100) NOT NULL,
    description VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_schema_name (schema_name),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create a table to track schema migrations
CREATE TABLE IF NOT EXISTS schema_migrations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    schema_name VARCHAR(100) NOT NULL,
    migration_name VARCHAR(255) NOT NULL,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_schema_migration (schema_name, migration_name),
    INDEX idx_schema_name (schema_name),
    INDEX idx_migration_name (migration_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default schemas
INSERT INTO schema_registry (schema_name, description, is_active)
VALUES 
    ('default', 'Default schema', TRUE),
    ('stock_data', 'Schema for stock data', TRUE),
    ('user_data', 'Schema for user data', TRUE),
    ('analytics', 'Schema for analytics data', TRUE)
ON DUPLICATE KEY UPDATE 
    description = VALUES(description),
    is_active = VALUES(is_active);
