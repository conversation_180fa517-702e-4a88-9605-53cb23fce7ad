-- Add read field to user_query table
-- This migration adds a new boolean field 'read' to the user_query table

-- Check if the column exists
SET @exists = (
    SELECT COUNT(*)
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = 'user_query'
    AND column_name = 'read'
);

-- Add the column if it doesn't exist
SET @query = IF(
    @exists = 0,
    'ALTER TABLE user_query ADD COLUMN `read` BOOLEAN NOT NULL DEFAULT 0',
    'SELECT "read column already exists in user_query table"'
);

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
