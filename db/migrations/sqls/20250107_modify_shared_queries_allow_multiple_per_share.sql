-- Remove unique constraint on share_id to allow multiple queries per share
-- This enables one share_id to contain multiple queries

-- Drop the unique constraint on share_id
ALTER TABLE shared_queries DROP INDEX share_id;

-- Add a composite unique constraint to prevent duplicate (share_id, query_id) pairs
ALTER TABLE shared_queries ADD UNIQUE KEY unique_share_query (share_id, query_id);

-- Update the index to be non-unique
CREATE INDEX idx_share_id ON shared_queries (share_id);
