-- Add cacheable column to query table if it doesn't exist
-- This migration adds a new BOOLEAN column to indicate if a query can be used for caching

-- Check if cacheable column exists
SET @exists = (
    SELECT COUNT(*)
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = 'query'
    AND column_name = 'cacheable'
);

-- Add the column if it doesn't exist
SET @query = IF(
    @exists = 0,
    'ALTER TABLE query ADD COLUMN cacheable BOOLEAN NOT NULL DEFAULT TRUE AFTER type',
    'SELECT "cacheable column already exists in query table"'
);

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
