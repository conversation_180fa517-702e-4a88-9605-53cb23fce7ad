-- Add type column to query table if it doesn't exist
-- This migration adds a new STRING column to store query types

-- Check if type column exists
SET @exists = (
    SELECT COUNT(*)
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = 'query'
    AND column_name = 'type'
);

-- Add the column if it doesn't exist
SET @query = IF(
    @exists = 0,
    'ALTER TABLE query ADD COLUMN type VARCHAR(50) NULL AFTER system_prompt',
    'SELECT "type column already exists in query table"'
);

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
