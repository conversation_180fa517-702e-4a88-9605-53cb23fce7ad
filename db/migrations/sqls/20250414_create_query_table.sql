-- Create query table for storing user queries and LLM answers
-- This migration creates a new table named "query" in the database

-- Check if query table exists
SET @exists = (
    SELECT COUNT(*)
    FROM information_schema.tables
    WHERE table_schema = DATABASE()
    AND table_name = 'query'
);

-- Create the table if it doesn't exist
SET @query = IF(
    @exists = 0,
    'CREATE TABLE query (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        thread_id VARCHAR(100) NOT NULL,
        run_id VARCHAR(100) NOT NULL,
        user_query TEXT NOT NULL,
        llm_answer TEXT,
        status VARCHAR(20) NOT NULL DEFAULT "pending",
        resources JSON,
        model VARCHAR(100),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        INDEX (thread_id),
        INDEX (run_id),
        UNIQUE INDEX (thread_id, run_id)
    )',
    'SELECT "query table already exists"'
);

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
