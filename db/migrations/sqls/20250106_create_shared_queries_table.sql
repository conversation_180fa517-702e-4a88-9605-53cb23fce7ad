-- Create shared_queries table for public query sharing
CREATE TABLE shared_queries (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    share_id VARCHAR(36) UNIQUE NOT NULL,     -- UUID format
    query_id BIGINT NOT NULL,                 -- Reference to query table
    user_id VARCHAR(100) NOT NULL,            -- Original query owner (auth0_sub)
    shared_by VA<PERSON>HAR(100) NOT NULL,          -- Who shared it (initially same as user_id)
    title VARCHAR(255) NULL,                  -- Optional custom title for the share
    description TEXT NULL,                    -- Optional description
    is_active BOOLEAN DEFAULT TRUE,           -- Can be disabled/revoked
    expires_at DATETIME NULL,                 -- Optional expiration
    view_count INT DEFAULT 0,                 -- Track popularity
    last_viewed_at DATETIME NULL,             -- Last access time
    created_at DATETIME DEFAULT NOW(),
    updated_at DATETIME DEFAULT NOW() ON UPDATE NOW(),
    
    FOREIGN KEY (query_id) REFERENCES query(id) ON DELETE CASCADE,
    INDEX idx_share_id (share_id),
    INDEX idx_query_id (query_id),
    INDEX idx_user_id (user_id),
    INDEX idx_active_expires (is_active, expires_at)
);
