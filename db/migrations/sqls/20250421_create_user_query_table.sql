-- Create user_query table for storing user-specific query information
-- This migration creates a new table named "user_query" in the database

-- Check if user_query table exists
SET @exists = (
    SELECT COUNT(*)
    FROM information_schema.tables
    WHERE table_schema = DATABASE()
    AND table_name = 'user_query'
);

-- Create the table if it doesn't exist
SET @query = IF(
    @exists = 0,
    'CREATE TABLE user_query (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        uid VARCHAR(100) NOT NULL,
        query_id BIGINT NOT NULL,
        query_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        completed_time DATETIME,
        status VARCHAR(20) NOT NULL DEFAULT "pending",
        INDEX (uid),
        INDEX (query_id),
        FOREIGN KEY (query_id) REFERENCES query(id)
    )',
    'SELECT "user_query table already exists"'
);

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
