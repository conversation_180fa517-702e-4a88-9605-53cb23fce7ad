-- Add token_usage and cost columns to query table if they don't exist
SET @tokenUsageExists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'user_data' 
    AND TABLE_NAME = 'query' 
    AND COLUMN_NAME = 'token_usage'
);

SET @costExists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'user_data' 
    AND TABLE_NAME = 'query' 
    AND COLUMN_NAME = 'cost'
);

SET @sql = '';

-- Add token_usage column if it doesn't exist
SET @sql = IF(@tokenUsageExists = 0, 
    'ALTER TABLE query ADD COLUMN token_usage JSON NULL', 
    'SELECT "token_usage column already exists" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add cost column if it doesn't exist
SET @sql = IF(@costExists = 0, 
    'ALTER TABLE query ADD COLUMN cost DECIMAL(10, 6) NULL', 
    'SELECT "cost column already exists" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
