-- Add system_prompt column to query table if it doesn't exist
-- This migration adds a new TEXT column to store system prompts for queries

-- Check if system_prompt column exists
SET @exists = (
    SELECT COUNT(*)
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = 'query'
    AND column_name = 'system_prompt'
);

-- Add the column if it doesn't exist
SET @query = IF(
    @exists = 0,
    'ALTER TABLE query ADD COLUMN system_prompt TEXT NULL AFTER model',
    'SELECT "system_prompt column already exists in query table"'
);

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
