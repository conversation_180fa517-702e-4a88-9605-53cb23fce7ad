-- Make run_id nullable in query table
-- This migration modifies the run_id column to allow NULL values

-- Check if run_id column exists and is NOT NULL
SET @column_exists = (
    SELECT COUNT(*)
    FROM information_schema.columns
    WHERE table_schema = DATABASE()
    AND table_name = 'query'
    AND column_name = 'run_id'
    AND is_nullable = 'NO'
);

-- Alter the column to allow NULL values if it exists and is NOT NULL
SET @query = IF(
    @column_exists > 0,
    'ALTER TABLE query MODIFY COLUMN run_id VARCHAR(100) NULL',
    'SELECT "run_id column is already nullable or does not exist"'
);

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update any existing records with NULL run_id to have a placeholder value
-- This is optional but helps maintain data integrity
SET @update_query = IF(
    @column_exists > 0,
    'UPDATE query SET run_id = "pending" WHERE run_id IS NULL',
    'SELECT "No update needed"'
);

PREPARE update_stmt FROM @update_query;
EXECUTE update_stmt;
DEALLOCATE PREPARE update_stmt;

-- Update indexes if needed
SET @index_exists = (
    SELECT COUNT(*)
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = 'query'
    AND index_name = 'thread_id_run_id'
);

-- Drop and recreate the unique index if it exists
SET @drop_index_query = IF(
    @index_exists > 0,
    'DROP INDEX thread_id_run_id ON query',
    'SELECT "No index to drop"'
);

PREPARE drop_index_stmt FROM @drop_index_query;
EXECUTE drop_index_stmt;
DEALLOCATE PREPARE drop_index_stmt;

SET @create_index_query = IF(
    @index_exists > 0,
    'CREATE UNIQUE INDEX thread_id_run_id ON query(thread_id, run_id(100))',
    'SELECT "No index to recreate"'
);

PREPARE create_index_stmt FROM @create_index_query;
EXECUTE create_index_stmt;
DEALLOCATE PREPARE create_index_stmt;
