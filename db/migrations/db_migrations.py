"""
Database migration utility for executing SQL migration files.

This module provides functions to run database migrations by executing SQL files
in the migrations/sqls directory. Files are executed in alphabetical order,
which should correspond to their timestamp-based naming convention.
"""

import os
import glob
import logging
import asyncio
from typing import List, Optional, Dict, Any

import aiomysql
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlalchemy import text

# Configure logging
logger = logging.getLogger(__name__)

# Path to SQL migration files
MIGRATIONS_DIR = os.path.join(os.path.dirname(__file__), 'sqls')


async def get_db_connection(connection_string: str) -> AsyncEngine:
    """
    Create a database connection using the provided connection string.
    
    Args:
        connection_string: SQLAlchemy connection string for the database
        
    Returns:
        AsyncEngine: SQLAlchemy async engine
    """
    engine = create_async_engine(connection_string)
    return engine


async def execute_sql_file(engine: AsyncEngine, file_path: str) -> None:
    """
    Execute a SQL file against the database.
    
    Args:
        engine: SQLAlchemy async engine
        file_path: Path to the SQL file to execute
    """
    try:
        with open(file_path, 'r') as f:
            sql = f.read()
            
        if not sql.strip():
            logger.warning(f"SQL file is empty: {file_path}")
            return
            
        async with engine.begin() as conn:
            await conn.execute(text(sql))
            
        logger.info(f"Successfully executed SQL file: {os.path.basename(file_path)}")
    except Exception as e:
        logger.error(f"Error executing SQL file {file_path}: {str(e)}")
        # Re-raise the exception to be handled by the caller
        raise


def get_migration_files() -> List[str]:
    """
    Get a sorted list of SQL migration files.
    
    Returns:
        List[str]: Sorted list of file paths
    """
    # Get all .sql files in the migrations directory
    sql_files = glob.glob(os.path.join(MIGRATIONS_DIR, '*.sql'))
    
    # Sort files by name (which should include a timestamp)
    sql_files.sort()
    
    return sql_files


async def run_migrations(connection_string: Optional[str] = None, schema_name: Optional[str] = None) -> None:
    """
    Run all database migrations.
    
    Args:
        connection_string: Optional SQLAlchemy connection string.
            If not provided, it will be read from environment variables.
    """
    if not connection_string:
        # Get connection string from environment variables
        from config.settings import get_database_url
        connection_string = get_database_url(async_mode=True)
    
    # Extract schema name from the connection string
    # The schema is the part after the last '/'
    schema_name = None
    if '/' in connection_string:
        schema_name = connection_string.split('/')[-1]
        # Handle any query parameters
        if '?' in schema_name:
            schema_name = schema_name.split('?')[0]
    
    logger.info(f"Starting database migrations for schema: {schema_name or 'default'}")
    
    # Get sorted list of migration files
    migration_files = get_migration_files()
    
    if not migration_files:
        logger.info("No migration files found")
        return
    
    # Initialize engine to None so we can check if it's defined in the finally block
    engine = None
    base_engine = None
    
    try:
        # First, connect to MySQL without specifying a database
        from config.settings import get_database_url
        base_connection_string = get_database_url(schema_name=None, async_mode=True)
        base_engine = await get_db_connection(base_connection_string)
        
        # Create the schema if it doesn't exist
        if schema_name:
            async with base_engine.begin() as conn:
                await conn.execute(text(f"CREATE DATABASE IF NOT EXISTS {schema_name}"))
            
            # Close the base connection
            await base_engine.dispose()
            base_engine = None
        
        # Now connect to the specific schema
        engine = await get_db_connection(connection_string)
        
        # Execute each migration file in order
        for file_path in migration_files:
            file_name = os.path.basename(file_path)
            logger.info(f"Executing migration: {file_name} on schema: {schema_name or 'default'}")
            try:
                await execute_sql_file(engine, file_path)
            except Exception as e:
                logger.error(f"Migration failed for {file_name} on schema {schema_name or 'default'}: {str(e)}")
                # Continue with next migration even if one fails
                continue
        
        logger.info(f"Database migrations completed successfully for schema: {schema_name or 'default'}")
    finally:
        # Close the database connections if they were created
        if engine is not None:
            await engine.dispose()
        if base_engine is not None:
            await base_engine.dispose()


async def run_migrations_on_multiple_schemas(schema_names: List[str]) -> Dict[str, Any]:
    """
    Run all database migrations on multiple schemas.
    
    Args:
        schema_names: List of schema names to run migrations on
        
    Returns:
        Dict[str, Any]: Dictionary with schema names as keys and migration results as values
    """
    logger.info(f"Starting database migrations on multiple schemas: {schema_names}")
    
    results = {}
    
    # Run migrations on each schema
    for schema_name in schema_names:
        try:
            # Get connection string for this schema
            from config.settings import get_database_url
            connection_string = get_database_url(schema_name, async_mode=True)
            
            # Run migrations with the connection string and schema name
            await run_migrations(connection_string=connection_string, schema_name=schema_name)
            results[schema_name] = {"success": True}
        except Exception as e:
            logger.error(f"Migrations failed for schema {schema_name}: {str(e)}")
            results[schema_name] = {"success": False, "error": str(e)}
    
    logger.info(f"Database migrations completed on multiple schemas: {schema_names}")
    
    return results


async def track_migration(schema_name: str, migration_name: str, connection_string: Optional[str] = None) -> None:
    """
    Track a migration in the schema_migrations table.
    
    Args:
        schema_name: Name of the schema the migration was applied to
        migration_name: Name of the migration
        connection_string: Optional SQLAlchemy connection string.
            If not provided, it will be read from environment variables.
    """
    # Get connection string from environment variables if not provided
    if not connection_string:
        from config.settings import get_database_url
        connection_string = get_database_url(async_mode=True)
    
    # Create database connection
    engine = await get_db_connection(connection_string)
    
    try:
        # Insert migration record
        async with engine.begin() as conn:
            # Ensure the schema_migrations table exists
            await conn.execute(text("""
                CREATE TABLE IF NOT EXISTS schema_migrations (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    schema_name VARCHAR(255) NOT NULL,
                    migration_name VARCHAR(255) NOT NULL,
                    applied_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY uk_schema_migration (schema_name, migration_name),
                    INDEX idx_schema_name (schema_name),
                    INDEX idx_migration_name (migration_name)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """))
            
            # Insert migration record
            await conn.execute(text(f"""
                INSERT INTO schema_migrations (schema_name, migration_name)
                VALUES ('{schema_name}', '{migration_name}')
                ON DUPLICATE KEY UPDATE applied_at = CURRENT_TIMESTAMP
            """))
        
        logger.info(f"Tracked migration {migration_name} for schema {schema_name}")
    finally:
        # Close the database connection
        await engine.dispose()


async def get_applied_migrations(schema_name: Optional[str] = None, connection_string: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get a list of applied migrations.
    
    Args:
        schema_name: Optional schema name to filter by
        connection_string: Optional SQLAlchemy connection string.
            If not provided, it will be read from environment variables.
        
    Returns:
        List[Dict[str, Any]]: List of applied migrations
    """
    # Get connection string from environment variables if not provided
    if not connection_string:
        from config.settings import get_database_url
        connection_string = get_database_url(async_mode=True)
    
    # Create database connection
    engine = await get_db_connection(connection_string)
    
    try:
        # Get applied migrations
        async with engine.begin() as conn:
            # Ensure the schema_migrations table exists
            await conn.execute(text("""
                CREATE TABLE IF NOT EXISTS schema_migrations (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    schema_name VARCHAR(255) NOT NULL,
                    migration_name VARCHAR(255) NOT NULL,
                    applied_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY uk_schema_migration (schema_name, migration_name),
                    INDEX idx_schema_name (schema_name),
                    INDEX idx_migration_name (migration_name)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """))
            
            # Get applied migrations
            if schema_name:
                result = await conn.execute(text(f"""
                    SELECT id, schema_name, migration_name, applied_at
                    FROM schema_migrations
                    WHERE schema_name = '{schema_name}'
                    ORDER BY applied_at
                """))
            else:
                result = await conn.execute(text("""
                    SELECT id, schema_name, migration_name, applied_at
                    FROM schema_migrations
                    ORDER BY schema_name, applied_at
                """))
            
            # Convert to list of dictionaries
            migrations = []
            for row in result.fetchall():
                migrations.append({
                    "id": row[0],
                    "schema_name": row[1],
                    "migration_name": row[2],
                    "applied_at": row[3].isoformat() if row[3] else None
                })
            
            return migrations
    finally:
        # Close the database connection
        await engine.dispose()
