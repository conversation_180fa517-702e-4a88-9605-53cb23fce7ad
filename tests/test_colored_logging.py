import sys
import os

# Add the parent directory to the Python path to import the logger
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.logging import setup_logging, logger

def test_colored_logging():
    """
    Test the colored logging functionality by logging messages at different levels.
    """
    print("Testing colored logging...")
    
    # Log messages at different levels
    logger.debug("This is a DEBUG message (blue)")
    logger.info("This is an INFO message (green)")
    logger.warning("This is a WARNING message (yellow)")
    logger.error("This is an ERROR message (red)")
    logger.critical("This is a CRITICAL message (white on red background, bold)")
    
    # Test with colors disabled
    print("\nTesting without colors...")
    no_color_logger = setup_logging(enable_colors=False)
    no_color_logger.debug("This is a DEBUG message (no color)")
    no_color_logger.info("This is an INFO message (no color)")
    no_color_logger.warning("This is a WARNING message (no color)")
    no_color_logger.error("This is an ERROR message (no color)")
    no_color_logger.critical("This is a CRITICAL message (no color)")

if __name__ == "__main__":
    test_colored_logging()
