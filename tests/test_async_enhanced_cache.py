import unittest
import sys
import os
import json
import time
import random
import string
import asyncio
from unittest.mock import patch, MagicMock

# Add parent directory to path to import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.async_cache import (
    generate_cache_key,
    get_cached_response,
    cache_response,
    async_invalidate_cache,
    async_get_cache_stats,
    warm_cache,
    enforce_cache_size_limits,
    compress_data,
    decompress_data,
    find_similar_key
)

class TestAsyncEnhancedCache(unittest.TestCase):
    """Test the enhanced async caching functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Clear the cache before each test
        self.loop = asyncio.get_event_loop()
        self.loop.run_until_complete(async_invalidate_cache())
        
        # Sample data for testing
        self.provider = "test_provider"
        self.model = "test_model"
        self.messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello, how are you?"}
        ]
        self.response = {
            "id": "test_id",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": "test_model",
            "provider": "test_provider",
            "choices": [
                {
                    "message": {
                        "role": "assistant",
                        "content": "I'm doing well, thank you for asking! How can I help you today?"
                    },
                    "finish_reason": "stop",
                    "index": 0
                }
            ],
            "usage": {
                "prompt_tokens": 20,
                "completion_tokens": 15,
                "total_tokens": 35
            }
        }
    
    def tearDown(self):
        """Tear down test fixtures."""
        # Clear the cache after each test
        self.loop.run_until_complete(async_invalidate_cache())
    
    def test_cache_key_generation(self):
        """Test that cache keys are generated consistently."""
        key1 = generate_cache_key(self.provider, self.model, self.messages)
        key2 = generate_cache_key(self.provider, self.model, self.messages)
        self.assertEqual(key1, key2, "Cache keys should be identical for identical inputs")
        
        # Test with different messages
        different_messages = self.messages + [{"role": "user", "content": "What's the weather like?"}]
        key3 = generate_cache_key(self.provider, self.model, different_messages)
        self.assertNotEqual(key1, key3, "Cache keys should be different for different inputs")
    
    def test_cache_response_and_retrieval(self):
        """Test caching a response and retrieving it."""
        async def test_async():
            # Cache a response
            await cache_response(self.provider, self.model, self.messages, self.response)
            
            # Retrieve the cached response
            cached = await get_cached_response(self.provider, self.model, self.messages)
            
            # Check that the cached response matches the original
            self.assertIsNotNone(cached, "Cached response should not be None")
            self.assertEqual(cached["id"], self.response["id"], "Cached response ID should match original")
            self.assertEqual(
                cached["choices"][0]["message"]["content"],
                self.response["choices"][0]["message"]["content"],
                "Cached response content should match original"
            )
        
        self.loop.run_until_complete(test_async())
    
    def test_cache_invalidation(self):
        """Test invalidating the cache."""
        async def test_async():
            # Cache a response
            await cache_response(self.provider, self.model, self.messages, self.response)
            
            # Invalidate the cache
            count = await async_invalidate_cache()
            
            # Check that the cache is empty
            cached = await get_cached_response(self.provider, self.model, self.messages)
            self.assertIsNone(cached, "Cached response should be None after invalidation")
            self.assertGreater(count, 0, "Invalidation should have deleted at least one key")
        
        self.loop.run_until_complete(test_async())
    
    def test_provider_specific_invalidation(self):
        """Test invalidating the cache for a specific provider."""
        async def test_async():
            # Cache a response
            await cache_response(self.provider, self.model, self.messages, self.response)
            
            # Cache another response with a different provider
            other_response = self.response.copy()
            other_response["provider"] = "other_provider"
            await cache_response("other_provider", self.model, self.messages, other_response)
            
            # Invalidate the cache for the specific provider
            count = await async_invalidate_cache(provider=self.provider)
            
            # Check that only the specified provider's cache was invalidated
            cached1 = await get_cached_response(self.provider, self.model, self.messages)
            cached2 = await get_cached_response("other_provider", self.model, self.messages)
            
            self.assertIsNone(cached1, "First provider's cache should be invalidated")
            self.assertIsNotNone(cached2, "Other provider's cache should still exist")
            self.assertEqual(count, 1, "Invalidation should have deleted exactly one key")
        
        self.loop.run_until_complete(test_async())
    
    def test_compression(self):
        """Test data compression and decompression."""
        # Generate a large string
        large_string = ''.join(random.choice(string.ascii_letters) for _ in range(10000))
        
        # Compress the string
        compressed = compress_data(large_string)
        
        # Decompress the string
        decompressed = decompress_data(compressed)
        
        # Check that the decompressed string matches the original
        self.assertEqual(decompressed, large_string, "Decompressed data should match original")
        
        # Check that compression actually reduced the size
        self.assertLess(len(compressed), len(large_string.encode()), "Compressed data should be smaller than original")
    
    def test_similar_key_detection(self):
        """Test detection of similar cache keys."""
        async def test_async():
            # Cache a response
            key = generate_cache_key(self.provider, self.model, self.messages)
            await cache_response(self.provider, self.model, self.messages, self.response)
            
            # Create a slightly different message that should produce a similar key
            similar_messages = [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Hello, how are you doing?"}  # Added "doing"
            ]
            similar_key = generate_cache_key(self.provider, self.model, similar_messages)
            
            # Find a similar key
            found_key = await find_similar_key(similar_key)
            
            # Check that the similar key was found
            self.assertIsNotNone(found_key, "Similar key should be found")
            self.assertEqual(found_key, key, "Found key should match the original key")
            
            # Try to get a cached response using the similar messages
            cached = await get_cached_response(self.provider, self.model, similar_messages)
            
            # Check that we got a response even though the exact key wasn't cached
            self.assertIsNotNone(cached, "Should get a cached response for similar messages")
            self.assertEqual(
                cached["choices"][0]["message"]["content"],
                self.response["choices"][0]["message"]["content"],
                "Cached response content should match original"
            )
        
        self.loop.run_until_complete(test_async())
    
    def test_cache_stats(self):
        """Test getting cache statistics."""
        async def test_async():
            # Cache a response
            await cache_response(self.provider, self.model, self.messages, self.response)
            
            # Get cache stats
            stats = await async_get_cache_stats()
            
            # Check that the stats include the expected information
            self.assertIn('total_keys', stats, "Stats should include total_keys")
            self.assertIn('providers', stats, "Stats should include providers")
            self.assertIn('models', stats, "Stats should include models")
            self.assertIn('provider_models', stats, "Stats should include provider_models")
            self.assertIn('memory_cache_size', stats, "Stats should include memory_cache_size")
            
            # Check that the stats reflect the cached response
            self.assertEqual(stats['total_keys'], 1, "Should have one cached key")
            self.assertIn(self.provider, stats['providers'], "Stats should include the provider")
            self.assertIn(self.model, stats['models'], "Stats should include the model")
            self.assertIn(f"{self.provider}/{self.model}", stats['provider_models'], "Stats should include the provider/model")
        
        self.loop.run_until_complete(test_async())
    
    @patch('utils.async_cache.memory_cache')
    def test_cache_warming(self, mock_memory_cache):
        """Test warming the cache."""
        async def test_async():
            # Set up the mock
            mock_memory_cache.__len__.return_value = 0
            
            # Cache a response
            await cache_response(self.provider, self.model, self.messages, self.response)
            
            # Warm the cache
            await warm_cache()
            
            # Check that the memory cache was accessed
            mock_memory_cache.__contains__.assert_called()
        
        self.loop.run_until_complete(test_async())
    
    @patch('utils.async_cache.async_redis')
    def test_enforce_cache_size_limits(self, mock_redis):
        """Test enforcing cache size limits."""
        async def test_async():
            # Set up the mock
            mock_client = MagicMock()
            mock_client.keys.return_value = [b'response_cache:key1', b'response_cache:key2']
            mock_redis.get_async_redis_client.return_value = mock_client
            
            # Enforce cache size limits
            await enforce_cache_size_limits()
            
            # Check that Redis was accessed
            mock_client.keys.assert_called_once()
        
        self.loop.run_until_complete(test_async())

if __name__ == '__main__':
    unittest.main()
