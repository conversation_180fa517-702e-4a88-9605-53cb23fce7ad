import pytest
import json
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi.testclient import TestClient
import async<PERSON>

from core.async_conversation import AsyncConversationManager
from providers.async_base_provider import AsyncModelProvider
from providers.async_openai_provider import Async<PERSON>penAIProvider
from providers.async_anthropic_provider import AsyncA<PERSON>hropicProvider
from providers.async_deepseek_provider import AsyncDeepSeekProvider
from providers.async_provider_factory import get_async_provider, register_async_provider
from utils.async_redis_client import async_redis

# Import the FastAPI app
from async_app import app

# Create a test client
client = TestClient(app)

# Mock async Redis client
@pytest.fixture
def mock_async_redis():
    """Fixture to mock the async Redis client."""
    with patch('core.async_conversation.async_redis') as mock:
        # Set up mock methods
        mock.lrange = AsyncMock(return_value=[])
        mock.rpush = AsyncMock(return_value=1)
        mock.delete = AsyncMock(return_value=1)
        yield mock

# Mock async provider
class MockAsyncProvider(AsyncModelProvider):
    """Mock async provider for testing."""
    
    async def _chat_completion_impl(self, messages, model, **kwargs):
        """Mock implementation of chat completion."""
        return {
            "choices": [
                {
                    "message": {
                        "role": "assistant",
                        "content": "This is a mock response"
                    },
                    "finish_reason": "stop",
                    "index": 0
                }
            ],
            "created": 0,
            "model": model,
            "object": "chat.completion"
        }

@pytest.fixture
def mock_async_provider():
    """Fixture to mock the async provider factory."""
    with patch('async_app.get_async_provider') as mock:
        provider = MockAsyncProvider()
        mock.return_value = provider
        yield mock

@pytest.mark.asyncio
async def test_async_conversation_manager(mock_async_redis):
    """Test the async conversation manager."""
    # Create a conversation manager
    conversation = AsyncConversationManager("test-session")
    
    # Test adding a user message
    user_message = await conversation.add_user_message("Hello")
    assert user_message["role"] == "user"
    assert user_message["content"] == "Hello"
    
    # Test adding an assistant message
    assistant_message = await conversation.add_assistant_message("Hi there")
    assert assistant_message["role"] == "assistant"
    assert assistant_message["content"] == "Hi there"
    
    # Test adding a system message
    system_message = await conversation.add_system_message("System message")
    assert system_message["role"] == "system"
    assert system_message["content"] == "System message"
    
    # Test getting history
    mock_async_redis.lrange.return_value = [
        json.dumps({"role": "user", "content": "Hello"}),
        json.dumps({"role": "assistant", "content": "Hi there"}),
        json.dumps({"role": "system", "content": "System message"})
    ]
    
    history = await conversation.get_history()
    assert len(history) == 3
    assert history[0]["role"] == "user"
    assert history[0]["content"] == "Hello"
    
    # Test clearing history
    await conversation.clear_history()
    mock_async_redis.delete.assert_called_once_with(conversation.history_key)

@pytest.mark.asyncio
async def test_async_openai_provider():
    """Test the async OpenAI provider."""
    with patch('providers.async_openai_provider.AsyncOpenAI') as mock_client:
        # Set up mock response
        mock_response = MagicMock()
        mock_response.model_dump.return_value = {
            "choices": [
                {
                    "message": {
                        "role": "assistant",
                        "content": "This is a test response"
                    },
                    "finish_reason": "stop",
                    "index": 0
                }
            ],
            "created": 0,
            "model": "gpt-3.5-turbo",
            "object": "chat.completion"
        }
        
        # Set up mock client
        mock_instance = MagicMock()
        mock_instance.chat.completions.create = AsyncMock(return_value=mock_response)
        mock_client.return_value = mock_instance
        
        # Create provider
        provider = AsyncOpenAIProvider()
        
        # Test chat completion
        messages = [{"role": "user", "content": "Hello"}]
        response = await provider.chat_completion(messages=messages, model="gpt-3.5-turbo")
        
        # Check response
        assert response["choices"][0]["message"]["content"] == "This is a test response"
        
        # Check that the client was called correctly
        mock_instance.chat.completions.create.assert_called_once_with(
            model="gpt-3.5-turbo",
            messages=messages,
            stream=False
        )

@pytest.mark.asyncio
async def test_async_provider_factory():
    """Test the async provider factory."""
    # Test getting a provider
    with patch('providers.async_provider_factory.AsyncOpenAIProvider') as mock_provider:
        mock_instance = MagicMock()
        mock_provider.return_value = mock_instance
        
        provider = await get_async_provider("openai")
        assert provider == mock_instance
    
    # Test registering a new provider
    class TestProvider(AsyncModelProvider):
        async def _chat_completion_impl(self, messages, model, **kwargs):
            pass
    
    register_async_provider("test", TestProvider)
    
    with patch('providers.async_provider_factory.TestProvider') as mock_provider:
        mock_instance = MagicMock()
        mock_provider.return_value = mock_instance
        
        provider = await get_async_provider("test")
        assert provider == mock_instance

def test_async_app_root():
    """Test the root endpoint of the async app."""
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "Go GPT Backend API"}

def test_async_app_query(mock_async_provider):
    """Test the query endpoint of the async app."""
    # Set up request data
    request_data = {
        "query": "Hello",
        "session_id": "test-session",
        "provider_model": "openai:gpt-3.5-turbo",
        "web_search": False,
        "stream": False
    }
    
    # Make request
    response = client.post("/query", json=request_data)
    
    # Check response
    assert response.status_code == 200
    assert response.json()["response"] == "This is a mock response"
    assert response.json()["session_id"] == "test-session"
    assert response.json()["provider"] == "openai"
    assert response.json()["model"] == "gpt-3.5-turbo"
    
    # Check that the provider was called
    mock_async_provider.assert_called_once_with("openai")

def test_async_app_clear_history():
    """Test the clear history endpoint of the async app."""
    # Set up request data
    request_data = {
        "session_id": "test-session"
    }
    
    # Mock the conversation manager
    with patch('async_app.AsyncConversationManager') as mock_conversation:
        mock_instance = MagicMock()
        mock_instance.clear_history = AsyncMock()
        mock_conversation.return_value = mock_instance
        
        # Make request
        response = client.post("/clear", json=request_data)
        
        # Check response
        assert response.status_code == 200
        assert response.json() == {"message": "Conversation history cleared"}
        
        # Check that the conversation manager was called
        mock_conversation.assert_called_once_with("test-session")
        mock_instance.clear_history.assert_called_once()
