#!/usr/bin/env python3
"""
Test script for the Assistant API streaming functionality.
"""
import sys
import json
import asyncio
import aiohttp
import argparse

async def test_streaming(url, query, thread_id=None, model=None):
    """
    Test the streaming functionality of the Assistant API.
    
    Args:
        url: The URL to send the request to
        query: The query to send
        thread_id: Optional thread ID for continuing a conversation
        model: Optional model name
    """
    # Prepare request payload
    payload = {
        "query": query,
        "stream": True
    }
    
    if thread_id:
        payload["thread_id"] = thread_id
    
    if model:
        payload["model"] = model
    
    print(f"Sending request to {url} with payload: {json.dumps(payload, indent=2)}")
    
    # Make the request
    async with aiohttp.ClientSession() as session:
        async with session.post(
            url,
            json=payload,
            headers={
                "Content-Type": "application/json",
                "Accept": "text/event-stream"
            }
        ) as response:
            if response.status != 200:
                print(f"Error: {response.status} {response.reason}")
                text = await response.text()
                print(f"Response: {text}")
                return
            
            # Process the streaming response
            buffer = ""
            current_thread_id = thread_id
            current_run_id = None
            
            async for line in response.content:
                line = line.decode('utf-8')
                buffer += line
                
                if buffer.endswith('\n\n'):
                    # Process complete message
                    message = buffer.strip()
                    buffer = ""
                    
                    if message.startswith('data: '):
                        try:
                            data = json.loads(message[6:])
                            
                            # Update metadata
                            if "thread_id" in data and data["thread_id"]:
                                current_thread_id = data["thread_id"]
                            
                            if "session_id" in data and data["session_id"]:
                                current_thread_id = data["session_id"]
                            
                            if "run_id" in data and data["run_id"]:
                                current_run_id = data["run_id"]
                            
                            if "round_id" in data and data["round_id"]:
                                current_run_id = data["round_id"]
                            
                            # Print metadata
                            print(f"\rThread ID: {current_thread_id or 'None'}, Run ID: {current_run_id or 'None'}", end="")
                            
                            # Handle different message types
                            if data.get("type") == "connection_established":
                                print("\nConnection established...")
                            elif data.get("type") == "metadata_update":
                                print("\nMetadata updated")
                            elif data.get("type") == "test":
                                print(f"\nTest message {data.get('index')}: {data.get('content')}")
                            elif data.get("content") == "[DONE]":
                                # End of stream
                                print("\nStream complete")
                            elif "content" in data and data["content"]:
                                # Regular content
                                print(data["content"], end="", flush=True)
                            elif "choices" in data and data["choices"] and "delta" in data["choices"][0]:
                                # OpenAI format content delta
                                content = data["choices"][0]["delta"].get("content", "")
                                if content:
                                    print(content, end="", flush=True)
                        except json.JSONDecodeError as e:
                            print(f"\nError parsing message: {e}")
                            print(f"Message: {message}")

async def main():
    parser = argparse.ArgumentParser(description="Test the Assistant API streaming functionality")
    parser.add_argument("--url", default="http://localhost:8080/assistant/stream", help="URL to send the request to")
    parser.add_argument("--query", default="Tell me about the weather today", help="Query to send")
    parser.add_argument("--thread-id", help="Thread ID for continuing a conversation")
    parser.add_argument("--model", default="gpt-4-turbo-preview", help="Model name")
    parser.add_argument("--port", default=8000, type=int, help="Port number")
    
    args = parser.parse_args()
    
    # Update URL with port
    args.url = f"http://localhost:{args.port}/assistant/stream"
    
    await test_streaming(args.url, args.query, args.thread_id, args.model)

if __name__ == "__main__":
    asyncio.run(main())
