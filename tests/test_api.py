import pytest
from app import create_app
import json

@pytest.fixture
def client():
    app = create_app()
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client

def test_health_check(client):
    """Test the health check endpoint."""
    response = client.get('/')
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data['status'] == 'ok'

def test_list_models(client):
    """Test the list models endpoint."""
    response = client.get('/models')
    assert response.status_code == 200
    data = json.loads(response.data)
    # Check that we have model data
    assert len(data) > 0
    # Check that each model has the expected fields
    for model_key, model_info in data.items():
        assert 'capabilities' in model_info
        assert 'avg_latency' in model_info
        assert 'error_rate' in model_info
        assert 'cost_per_token' in model_info

def test_query_missing_parameter(client):
    """Test the query endpoint with missing parameter."""
    response = client.post('/query', json={})
    assert response.status_code == 400
    data = json.loads(response.data)
    assert 'error' in data
    assert 'Missing query parameter' in data['error']

# Add more tests for the query endpoint with valid parameters
# These would typically use mocking to avoid actual API calls
