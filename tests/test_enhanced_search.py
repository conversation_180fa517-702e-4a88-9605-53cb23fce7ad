import pytest
from unittest.mock import patch, MagicMock
import json
from utils.enhanced_search import (
    SearchResult,
    perform_web_search,
    perform_multi_provider_search,
    calculate_credibility_score,
    extract_content_from_results,
    cache_search_results,
    get_cached_search_results
)

@pytest.fixture
def mock_search_results():
    """Fixture to create mock search results."""
    return [
        SearchResult(
            title="Test Result 1",
            url="https://example.com/1",
            snippet="This is a test snippet 1",
            source="google",
            credibility_score=0.8,
            content="This is the full content of test result 1."
        ),
        SearchResult(
            title="Test Result 2",
            url="https://example.com/2",
            snippet="This is a test snippet 2",
            source="google_serp",
            credibility_score=0.6,
            content="This is the full content of test result 2."
        )
    ]

@pytest.fixture
def redis_mock():
    """Fixture to mock Redis client."""
    with patch('utils.enhanced_search.redis_client') as mock:
        yield mock

def test_search_result_to_dict(mock_search_results):
    """Test converting a SearchResult to a dictionary."""
    result = mock_search_results[0]
    result_dict = result.to_dict()
    
    assert result_dict['title'] == "Test Result 1"
    assert result_dict['url'] == "https://example.com/1"
    assert result_dict['snippet'] == "This is a test snippet 1"
    assert result_dict['source'] == "google"
    assert result_dict['credibility_score'] == 0.8
    assert result_dict['content'] == "This is the full content of test result 1."
    assert 'timestamp' in result_dict

def test_search_result_from_dict():
    """Test creating a SearchResult from a dictionary."""
    data = {
        'title': "Test Result",
        'url': "https://example.com",
        'snippet': "This is a test snippet",
        'source': "google",
        'credibility_score': 0.7,
        'content': "This is the full content."
    }
    
    result = SearchResult.from_dict(data)
    
    assert result.title == "Test Result"
    assert result.url == "https://example.com"
    assert result.snippet == "This is a test snippet"
    assert result.source == "google"
    assert result.credibility_score == 0.7
    assert result.content == "This is the full content."
    assert hasattr(result, 'timestamp')

def test_calculate_credibility_score():
    """Test calculating credibility scores for different URLs."""
    # Educational domain
    score1 = calculate_credibility_score(
        "https://stanford.edu/research/paper",
        "Research Paper 2023",
        "This study analyzes data from multiple sources."
    )
    assert score1 > 0.5  # Should be higher due to .edu domain and research content
    
    # News site with year in title
    score2 = calculate_credibility_score(
        "https://nytimes.com/article",
        "News Article 2023",
        "This is a news article about current events."
    )
    assert score2 > 0.5  # Should be higher due to reputable domain and year
    
    # Opinion piece
    score3 = calculate_credibility_score(
        "https://example.com/blog",
        "My Opinion",
        "This is my editorial opinion on the topic."
    )
    assert score3 <= 0.5  # Should be lower due to opinion content

def test_extract_content_from_results(mock_search_results):
    """Test extracting and combining content from search results."""
    content = extract_content_from_results(mock_search_results, max_chars=1000)
    
    # Check that content includes information from both results
    assert "Test Result 1" in content
    assert "https://example.com/1" in content
    assert "Test Result 2" in content
    assert "https://example.com/2" in content
    
    # Check that results are ordered by credibility score (highest first)
    first_result_pos = content.find("Test Result 1")
    second_result_pos = content.find("Test Result 2")
    assert first_result_pos < second_result_pos

@patch('utils.enhanced_search.search_providers')
def test_perform_multi_provider_search(mock_search_providers, mock_search_results):
    """Test performing a search using multiple providers."""
    # Set up mock search providers
    google_mock = MagicMock(return_value=[mock_search_results[0]])
    google_serp_mock = MagicMock(return_value=[mock_search_results[1]])
    
    mock_search_providers.items.return_value = [
        ('google', google_mock),
        ('google_serp', google_serp_mock)
    ]
    
    # Perform the search
    results = perform_multi_provider_search("test query", providers=['google', 'google_serp'])
    
    # Check that both providers were called
    google_mock.assert_called_once_with("test query", 5)
    google_serp_mock.assert_called_once_with("test query", 5)
    
    # Check that results from both providers are included
    assert len(results) == 2
    assert any(r.source == 'google' for r in results)
    assert any(r.source == 'google_serp' for r in results)

def test_cache_search_results(redis_mock, mock_search_results):
    """Test caching search results in Redis."""
    cache_search_results("test query", mock_search_results)
    
    # Check that Redis setex was called with the correct arguments
    redis_mock.setex.assert_called_once()
    args = redis_mock.setex.call_args[0]
    assert args[0] == "search_cache:test query"
    assert isinstance(args[1], int)  # TTL
    
    # Check that the cached data is correct
    cached_data = json.loads(args[2])
    assert len(cached_data) == 2
    assert cached_data[0]['title'] == "Test Result 1"
    assert cached_data[1]['title'] == "Test Result 2"

def test_get_cached_search_results(redis_mock):
    """Test retrieving cached search results from Redis."""
    # Set up mock Redis response
    cached_data = [
        {
            'title': "Cached Result",
            'url': "https://example.com/cached",
            'snippet': "This is a cached snippet",
            'source': "google",
            'credibility_score': 0.9,
            'content': "This is cached content.",
            'timestamp': 1234567890.0
        }
    ]
    redis_mock.get.return_value = json.dumps(cached_data)
    
    # Get cached results
    results = get_cached_search_results("test query")
    
    # Check that Redis get was called with the correct key
    redis_mock.get.assert_called_once_with("search_cache:test query")
    
    # Check that the results were correctly parsed
    assert len(results) == 1
    assert results[0].title == "Cached Result"
    assert results[0].url == "https://example.com/cached"
    assert results[0].credibility_score == 0.9

@patch('utils.enhanced_search.perform_multi_provider_search')
@patch('utils.enhanced_search.get_cached_search_results')
def test_perform_web_search_with_cache(mock_get_cached, mock_perform_search, mock_search_results):
    """Test performing a web search with caching."""
    # Set up mock cached results
    mock_get_cached.return_value = mock_search_results
    
    # Perform search with caching enabled
    results, content = perform_web_search("test query", use_cache=True)
    
    # Check that cached results were used
    mock_get_cached.assert_called_once_with("test query")
    mock_perform_search.assert_not_called()
    
    # Check that results and content are correct
    assert len(results) == 2
    assert results[0].title == "Test Result 1"
    assert "Test Result 1" in content
    assert "Test Result 2" in content

@patch('utils.enhanced_search.perform_multi_provider_search')
@patch('utils.enhanced_search.get_cached_search_results')
@patch('utils.enhanced_search.cache_search_results')
def test_perform_web_search_without_cache(mock_cache_results, mock_get_cached, 
                                         mock_perform_search, mock_search_results):
    """Test performing a web search without using cache."""
    # Set up mocks
    mock_get_cached.return_value = None
    mock_perform_search.return_value = mock_search_results
    
    # Perform search with caching disabled
    results, content = perform_web_search("test query", use_cache=False)
    
    # Check that search was performed and results were cached
    mock_get_cached.assert_not_called()
    mock_perform_search.assert_called_once_with("test query")
    mock_cache_results.assert_called_once()
    
    # Check that results and content are correct
    assert len(results) == 2
    assert results[0].title == "Test Result 1"
    assert "Test Result 1" in content
    assert "Test Result 2" in content
