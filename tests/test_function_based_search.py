import pytest
import json
import uuid
from unittest.mock import patch, MagicMock, AsyncMock

from utils.sample_functions import search, refine_search, get_search_history
from utils.enhanced_search import SearchResult
from utils.round_aware_search import get_relevant_context_from_rounds


@pytest.fixture
def mock_search_results():
    """Fixture to create mock search results."""
    return [
        SearchResult(
            title="Test Result 1",
            url="https://example.com/1",
            snippet="This is a test result 1",
            source="google",
            credibility_score=0.8,
            content="Detailed content for test result 1"
        ),
        SearchResult(
            title="Test Result 2",
            url="https://example.com/2",
            snippet="This is a test result 2",
            source="google_serp",
            credibility_score=0.7,
            content="Detailed content for test result 2"
        )
    ]


@pytest.mark.asyncio
@patch('utils.sample_functions.perform_multi_provider_search')
@patch('utils.sample_functions.extract_content_from_results')
@patch('utils.sample_functions.async_redis')
async def test_search_function_basic(mock_redis, mock_extract_content, mock_perform_search, mock_search_results):
    """Test the basic functionality of the search function."""
    # Setup mocks
    mock_perform_search.return_value = mock_search_results
    mock_extract_content.return_value = "Combined content from search results"
    mock_redis.get.return_value = None
    mock_redis.setex.return_value = True
    mock_redis.rpush.return_value = True
    mock_redis.expire.return_value = True
    
    # Call the search function
    result = await search(
        query="test query",
        num_results=2,
        engines=["google"],
        include_content=True
    )
    
    # Verify the result
    assert result["query"] == "test query"
    assert result["engines"] == ["google"]
    assert result["num_results"] == 2
    assert "search_id" in result
    assert result["content"] == "Combined content from search results"
    assert len(result["results"]) == 2
    
    # Verify the search was performed with the correct parameters
    mock_perform_search.assert_called_once_with("test query", ["google"], 2)
    
    # Verify content extraction was called
    mock_extract_content.assert_called_once_with(mock_search_results)


@pytest.mark.asyncio
@patch('utils.sample_functions.perform_multi_provider_search')
@patch('utils.sample_functions.get_relevant_context_from_rounds')
@patch('utils.sample_functions.async_redis')
async def test_search_function_with_conversation_context(mock_redis, mock_get_context, mock_perform_search, mock_search_results):
    """Test the search function with conversation context."""
    # Setup mocks
    mock_perform_search.return_value = mock_search_results
    mock_get_context.return_value = "relevant context from conversation"
    mock_redis.get.return_value = None
    mock_redis.setex.return_value = True
    mock_redis.rpush.return_value = True
    mock_redis.expire.return_value = True
    
    # Mock the AsyncConversationManager
    with patch('utils.sample_functions.AsyncConversationManager') as mock_conversation_manager:
        # Setup the conversation manager mock
        mock_instance = MagicMock()
        mock_instance._get_current_round_id = AsyncMock(return_value=1)
        mock_instance.get_history = AsyncMock(return_value=[{"role": "user", "content": "previous message"}])
        mock_instance.get_previous_rounds_summary = AsyncMock(return_value="Summary of previous rounds")
        mock_conversation_manager.return_value = mock_instance
        
        # Call the search function with session_id
        result = await search(
            query="test query",
            num_results=2,
            include_content=False,
            session_id="test_session_123"
        )
        
        # Verify the conversation context was used
        mock_conversation_manager.assert_called_once_with("test_session_123")
        mock_instance._get_current_round_id.assert_called_once()
        mock_instance.get_history.assert_called_once()
        mock_get_context.assert_called_once()
        
        # Verify search was performed with enhanced query
        assert mock_perform_search.call_count == 1
        # The actual query might be enhanced, so we don't check the exact value


@pytest.mark.asyncio
@patch('utils.sample_functions.perform_multi_provider_search')
@patch('utils.sample_functions.async_redis')
async def test_search_function_with_url_deduplication(mock_redis, mock_perform_search, mock_search_results):
    """Test the search function with URL deduplication."""
    # Setup mocks
    mock_perform_search.return_value = mock_search_results
    
    # Setup Redis mock to return previously seen URLs
    seen_urls = ["https://example.com/1"]
    mock_redis.get.return_value = json.dumps(seen_urls)
    mock_redis.setex.return_value = True
    mock_redis.rpush.return_value = True
    mock_redis.expire.return_value = True
    
    # Call the search function with exclude_seen=True
    result = await search(
        query="test query",
        num_results=2,
        include_content=False,
        session_id="test_session_123",
        exclude_seen=True
    )
    
    # Verify that only one result is returned (the other was filtered out)
    assert result["num_results"] == 1
    assert result["results"][0]["url"] == "https://example.com/2"
    
    # Verify that the seen URLs were updated in Redis
    mock_redis.setex.assert_called_once()
    # The second argument should be the TTL (86400 seconds)
    assert mock_redis.setex.call_args[0][1] == 86400
    # The third argument should be the JSON string of the updated seen URLs
    updated_urls = json.loads(mock_redis.setex.call_args[0][2])
    assert "https://example.com/1" in updated_urls
    assert "https://example.com/2" in updated_urls


@pytest.mark.asyncio
@patch('utils.sample_functions.search')
@patch('utils.sample_functions.async_redis')
async def test_refine_search_function(mock_redis, mock_search):
    """Test the refine_search function."""
    # Setup mocks
    search_id = str(uuid.uuid4())
    original_query = "original query"
    
    # Mock Redis to return search history
    search_history_entry = {
        "id": search_id,
        "query": original_query,
        "enhanced_query": "enhanced original query",
        "timestamp": "2023-01-01T00:00:00",
        "num_results": 2,
        "result_ids": ["id1", "id2"],
        "result_urls": ["url1", "url2"]
    }
    mock_redis.lrange.return_value = [json.dumps(search_history_entry)]
    
    # Mock the search function
    mock_search.return_value = {
        "search_id": str(uuid.uuid4()),
        "query": "refined query",
        "num_results": 2,
        "results": [{"id": "id3", "url": "url3"}, {"id": "id4", "url": "url4"}]
    }
    
    # Call the refine_search function
    result = await refine_search(
        search_id=search_id,
        additional_terms="additional terms",
        exclude_terms="exclude terms",
        num_results=3,
        include_content=True,
        session_id="test_session_123"
    )
    
    # Verify that the search history was retrieved
    mock_redis.lrange.assert_called_once()
    
    # Verify that the search function was called with the refined query
    mock_search.assert_called_once()
    call_args = mock_search.call_args[1]
    assert call_args["query"] == "original query additional terms -exclude -terms"
    assert call_args["num_results"] == 3
    assert call_args["include_content"] == True
    assert call_args["session_id"] == "test_session_123"
    assert call_args["exclude_seen"] == False


@pytest.mark.asyncio
@patch('utils.sample_functions.async_redis')
async def test_get_search_history_function(mock_redis):
    """Test the get_search_history function."""
    # Setup mocks
    search_history = [
        json.dumps({
            "id": str(uuid.uuid4()),
            "query": "query 1",
            "enhanced_query": "enhanced query 1",
            "timestamp": "2023-01-01T00:00:00",
            "num_results": 2
        }),
        json.dumps({
            "id": str(uuid.uuid4()),
            "query": "query 2",
            "enhanced_query": "enhanced query 2",
            "timestamp": "2023-01-02T00:00:00",
            "num_results": 3
        })
    ]
    mock_redis.lrange.return_value = search_history
    
    # Call the get_search_history function
    result = await get_search_history(
        session_id="test_session_123",
        limit=2
    )
    
    # Verify that the search history was retrieved
    mock_redis.lrange.assert_called_once()
    
    # Verify the result
    assert result["session_id"] == "test_session_123"
    assert len(result["history"]) == 2
    assert result["history"][0]["query"] == "query 1"
    assert result["history"][1]["query"] == "query 2"


@pytest.mark.asyncio
async def test_get_relevant_context_from_rounds():
    """Test the get_relevant_context_from_rounds function."""
    # Setup test data
    query = "What is the capital of France?"
    current_round_messages = [
        {"role": "user", "content": "Tell me about France"},
        {"role": "assistant", "content": "France is a country in Western Europe."},
        {"role": "user", "content": "What is the capital of France?"}
    ]
    previous_rounds_summary = "The user asked about European countries."
    
    # Call the function
    context = get_relevant_context_from_rounds(query, current_round_messages, previous_rounds_summary)
    
    # Verify that relevant context was extracted
    assert "France" in context
    assert "country in Western Europe" in context
