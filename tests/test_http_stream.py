"""
Test script to directly test the HTTP streaming endpoint.
"""
import asyncio
import json
import sys
import os
import aiohttp

async def test_http_streaming():
    """Test the HTTP streaming endpoint directly."""
    print("Starting HTTP streaming test")
    
    # Server URL
    url = "http://localhost:8080/assistant/stream"
    
    # Query parameters
    params = {
        "query": "Tell me about the weather today",
        "model": "gpt-4-turbo-preview"
    }
    
    # Make the request
    print(f"Connecting to {url} with params: {params}")
    async with aiohttp.ClientSession() as session:
        async with session.get(url, params=params) as response:
            print(f"Response status: {response.status}")
            
            # Check if the response is successful
            if response.status != 200:
                print(f"Error: {await response.text()}")
                return
            
            # Process the streaming response
            print("Processing streaming response:")
            async for line in response.content:
                line = line.decode('utf-8').strip()
                
                # Skip empty lines
                if not line:
                    continue
                
                # Check if it's an SSE data line
                if line.startswith('data: '):
                    # Extract the JSON data
                    data_str = line[6:]  # Remove 'data: ' prefix
                    try:
                        data = json.loads(data_str)
                        
                        # Print the content if available
                        if "content" in data:
                            print(f"Content: {data['content']}")
                        
                        # Print thread_id and round_id if available
                        if "session_id" in data:
                            print(f"Session ID: {data['session_id']}")
                        if "round_id" in data:
                            print(f"Round ID: {data['round_id']}")
                        
                        # Check if it's the end of the stream
                        if data.get("content") == "[DONE]":
                            print("Stream completed")
                            break
                    except json.JSONDecodeError as e:
                        print(f"Error parsing JSON: {e}")
                        print(f"Raw data: {data_str}")
                else:
                    print(f"Unexpected line: {line}")
    
    print("HTTP streaming test completed")

if __name__ == "__main__":
    # Run the test
    asyncio.run(test_http_streaming())
