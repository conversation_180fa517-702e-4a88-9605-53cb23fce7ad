import pytest
from unittest.mock import patch, MagicMock
from providers import get_provider

def test_get_provider_valid():
    """Test getting a valid provider."""
    with patch('providers.DeepSeekProvider') as mock_provider:
        mock_instance = MagicMock()
        mock_provider.return_value = mock_instance
        
        provider = get_provider('deepseek')
        assert provider == mock_instance
        mock_provider.assert_called_once()

def test_get_provider_invalid():
    """Test getting an invalid provider."""
    with pytest.raises(ValueError) as excinfo:
        get_provider('invalid_provider')
    assert 'Unknown provider: invalid_provider' in str(excinfo.value)

def test_provider_chat_completion():
    """Test provider chat completion method."""
    with patch('providers.OpenAIProvider') as mock_provider:
        mock_instance = MagicMock()
        mock_response = MagicMock()
        mock_response.model_dump.return_value = {'choices': [{'message': {'content': 'Test response'}}]}
        mock_instance.chat_completion.return_value = mock_response
        mock_provider.return_value = mock_instance
        
        provider = get_provider('openai')
        messages = [{'role': 'user', 'content': 'Hello'}]
        response = provider.chat_completion(messages=messages, model='gpt-3.5-turbo')
        
        mock_instance.chat_completion.assert_called_once_with(
            messages=messages, 
            model='gpt-3.5-turbo'
        )
        assert response == mock_response
