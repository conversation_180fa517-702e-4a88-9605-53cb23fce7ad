import pytest
from unittest.mock import patch, MagicMock, call
import pybreaker
from utils.circuit_breaker import (
    with_circuit_breaker,
    with_retry,
    with_fallback,
    circuit_breakers,
    CircuitBreakerListener
)

def test_circuit_breakers_initialization():
    """Test that circuit breakers are initialized for each provider."""
    assert 'openai' in circuit_breakers
    assert 'anthropic' in circuit_breakers
    assert 'deepseek' in circuit_breakers
    
    for provider, cb in circuit_breakers.items():
        assert isinstance(cb, pybreaker.CircuitBreaker)
        assert cb.fail_max == 5
        assert cb.reset_timeout == 30

def test_circuit_breaker_listener():
    """Test the CircuitBreakerListener class."""
    listener = CircuitBreakerListener('test_provider')
    
    # Test state_change method
    with patch('utils.circuit_breaker.logger') as mock_logger:
        listener.state_change(None, 'closed', 'open')
        mock_logger.warning.assert_called_once_with(
            "Circuit breaker for test_provider changed from closed to open"
        )
    
    # Test failure method
    with patch('utils.circuit_breaker.logger') as mock_logger:
        listener.failure(None, Exception("Test error"))
        mock_logger.error.assert_called_once_with(
            "Circuit breaker for test_provider recorded a failure: Test error"
        )
    
    # Test success method
    with patch('utils.circuit_breaker.logger') as mock_logger:
        listener.success(None)
        mock_logger.info.assert_called_once_with(
            "Circuit breaker for test_provider recorded a success"
        )

def test_with_circuit_breaker_decorator():
    """Test the with_circuit_breaker decorator."""
    # Create a mock function to decorate
    mock_func = MagicMock(return_value="success")
    
    # Create a mock circuit breaker
    mock_cb = MagicMock()
    mock_cb.return_value = lambda f: lambda *args, **kwargs: f(*args, **kwargs)
    
    # Patch the circuit_breakers dictionary
    with patch('utils.circuit_breaker.circuit_breakers', {'test_provider': mock_cb}):
        # Apply the decorator
        decorated_func = with_circuit_breaker('test_provider')(mock_func)
        
        # Call the decorated function
        result = decorated_func("arg1", key="value")
        
        # Check that the circuit breaker was used
        mock_cb.assert_called_once()
        
        # Check that the original function was called with the correct arguments
        mock_func.assert_called_once_with("arg1", key="value")
        
        # Check that the result is correct
        assert result == "success"

def test_with_circuit_breaker_new_provider():
    """Test the with_circuit_breaker decorator with a new provider."""
    # Create a mock function to decorate
    mock_func = MagicMock(return_value="success")
    
    # Patch the circuit_breakers dictionary and CircuitBreaker class
    with patch('utils.circuit_breaker.circuit_breakers', {}) as mock_cbs, \
         patch('utils.circuit_breaker.pybreaker.CircuitBreaker') as mock_cb_class, \
         patch('utils.circuit_breaker.CircuitBreakerListener') as mock_listener_class:
        
        # Set up the mock circuit breaker
        mock_cb = MagicMock()
        mock_cb.return_value = lambda f: lambda *args, **kwargs: f(*args, **kwargs)
        mock_cb_class.return_value = mock_cb
        
        # Apply the decorator
        decorated_func = with_circuit_breaker('new_provider')(mock_func)
        
        # Call the decorated function
        result = decorated_func("arg1", key="value")
        
        # Check that a new circuit breaker was created
        mock_cb_class.assert_called_once_with(
            fail_max=5,
            reset_timeout=30,
            exclude=[KeyboardInterrupt, SystemExit],
            name='new_provider'
        )
        
        # Check that a listener was added
        mock_listener_class.assert_called_once_with('new_provider')
        mock_cb.add_listener.assert_called_once()
        
        # Check that the circuit breaker was added to the dictionary
        assert 'new_provider' in mock_cbs
        
        # Check that the original function was called with the correct arguments
        mock_func.assert_called_once_with("arg1", key="value")
        
        # Check that the result is correct
        assert result == "success"

@patch('utils.circuit_breaker.retry')
def test_with_retry_decorator(mock_retry):
    """Test the with_retry decorator."""
    # Set up the mock retry decorator
    mock_retry_instance = MagicMock()
    mock_retry.return_value = mock_retry_instance
    
    # Create a mock function to decorate
    mock_func = MagicMock(return_value="success")
    
    # Apply the decorator
    decorated_func = with_retry(max_attempts=5, min_wait=2, max_wait=20)(mock_func)
    
    # Check that retry was called with the correct arguments
    mock_retry.assert_called_once()
    args = mock_retry.call_args[1]
    assert 'stop' in args
    assert 'wait' in args
    assert 'retry' in args
    assert 'before_sleep' in args

def test_with_fallback_decorator():
    """Test the with_fallback decorator."""
    # Create mock functions
    primary_func = MagicMock(side_effect=Exception("Primary function failed"))
    fallback_func = MagicMock(return_value="fallback result")
    
    # Apply the decorator
    decorated_func = with_fallback(fallback_func)(primary_func)
    
    # Call the decorated function
    with patch('utils.circuit_breaker.logger') as mock_logger:
        result = decorated_func("arg1", key="value")
    
    # Check that the primary function was called
    primary_func.assert_called_once_with("arg1", key="value")
    
    # Check that the fallback function was called with the same arguments
    fallback_func.assert_called_once_with("arg1", key="value")
    
    # Check that the result is from the fallback function
    assert result == "fallback result"
    
    # Check that a warning was logged
    mock_logger.warning.assert_called_once()

def test_integration_of_decorators():
    """Test the integration of all three decorators."""
    # Create mock functions
    primary_func = MagicMock(side_effect=[Exception("First attempt failed"), "success"])
    fallback_func = MagicMock(return_value="fallback result")
    
    # Apply all decorators
    decorated_func = with_circuit_breaker('test_provider')(
        with_retry(max_attempts=2)(
            with_fallback(fallback_func)(primary_func)
        )
    )
    
    # Set up the mock circuit breaker
    mock_cb = MagicMock()
    mock_cb.return_value = lambda f: lambda *args, **kwargs: f(*args, **kwargs)
    
    # Patch the retry decorator to call the function directly
    with patch('utils.circuit_breaker.retry', lambda **kwargs: lambda f: f), \
         patch('utils.circuit_breaker.circuit_breakers', {'test_provider': mock_cb}), \
         patch('utils.circuit_breaker.logger'):
        
        # Call the decorated function
        result = decorated_func("arg1", key="value")
    
    # Check that the primary function was called twice (retry)
    assert primary_func.call_count == 2
    primary_func.assert_has_calls([
        call("arg1", key="value"),
        call("arg1", key="value")
    ])
    
    # Check that the fallback function was not called (second attempt succeeded)
    fallback_func.assert_not_called()
    
    # Check that the result is from the primary function
    assert result == "success"
