import pytest
from utils.ticker_utils import (
    get_cleaned_symbol,
    get_market_from_symbol,
    get_yf_symbol_and_market,
    check_symbol_exists
)
from utils.ticker_db_functions import check_yf_symbol_exists


class TestGetCleanedSymbol:
    """Test cases for get_cleaned_symbol function."""

    def test_empty_symbol(self):
        """Test with empty symbol."""
        assert get_cleaned_symbol("") == ""
        assert get_cleaned_symbol("", "United States") == ""

    def test_none_symbol(self):
        """Test with None symbol."""
        # Function gracefully handles None by returning None
        assert get_cleaned_symbol(None) is None
        assert get_cleaned_symbol(None, "United States") is None

    def test_basic_symbol_no_suffix(self):
        """Test basic symbol without suffix."""
        assert get_cleaned_symbol("AAPL") == "AAPL"
        assert get_cleaned_symbol("aapl") == "AAPL"

    def test_us_market_specific_behavior(self):
        """Test US market specific behavior."""
        # US market should only remove .US suffix
        assert get_cleaned_symbol("AAPL.US", "United States") == "AAPL"
        assert get_cleaned_symbol("AAPL.US", "United States") == "AAPL"
        assert get_cleaned_symbol("MRP.V", "United States") == "MRP.V"  # Should not remove .V
        assert get_cleaned_symbol("ECG.V", "United States") == "ECG.V"  # Should not remove .V
        assert get_cleaned_symbol("BALY.T", "United States") == "BALY.T"  # Should not remove .T

    def test_suffix_removal_non_us_markets(self):
        """Test suffix removal for non-US markets."""
        # Test all supported suffixes
        assert get_cleaned_symbol("0700.HK") == "700"
        assert get_cleaned_symbol("005930.KS") == "5930"
        assert get_cleaned_symbol("005930.KQ") == "5930"
        assert get_cleaned_symbol("7203.T") == "7203"
        assert get_cleaned_symbol("2330.TW") == "2330"
        assert get_cleaned_symbol("AAPL.US") == "AAPL"

    def test_suffix_removal_with_market_parameter(self):
        """Test suffix removal with specific market parameter."""
        # Non-US markets should remove suffixes
        assert get_cleaned_symbol("0700.HK", "Hong Kong") == "700"
        assert get_cleaned_symbol("005930.KS", "South Korea") == "5930"
        assert get_cleaned_symbol("7203.T", "Japan") == "7203"

    def test_leading_zero_removal(self):
        """Test removal of leading zeros from numeric symbols."""
        assert get_cleaned_symbol("0700.HK") == "700"
        assert get_cleaned_symbol("0101.HK") == "101"
        assert get_cleaned_symbol("0001.HK") == "1"
        assert get_cleaned_symbol("1234.HK") == "1234"  # No leading zeros

    def test_non_numeric_symbols(self):
        """Test symbols that are not purely numeric."""
        assert get_cleaned_symbol("AAPL.US") == "AAPL"
        assert get_cleaned_symbol("TSM.TW") == "TSM"
        assert get_cleaned_symbol("ABC123.HK") == "ABC123"

    def test_case_insensitive_market(self):
        """Test case insensitive market parameter."""
        assert get_cleaned_symbol("AAPL.US", "UNITED STATES") == "AAPL"
        assert get_cleaned_symbol("AAPL.US", "United States") == "AAPL"
        assert get_cleaned_symbol("AAPL.US", "United States") == "AAPL"

    def test_no_matching_suffix(self):
        """Test symbols with no matching suffix."""
        assert get_cleaned_symbol("AAPL") == "AAPL"
        assert get_cleaned_symbol("AAPL.XX") == "AAPL.XX"
        assert get_cleaned_symbol("AAPL.NASDAQ") == "AAPL.NASDAQ"


class TestGetMarketFromSymbol:
    """Test cases for get_market_from_symbol function."""

    def test_hong_kong_market(self):
        """Test Hong Kong market detection."""
        assert get_market_from_symbol("0700.HK") == "Hong Kong"
        assert get_market_from_symbol("0700.hk") == "Hong Kong"

    def test_south_korea_market(self):
        """Test South Korea market detection."""
        assert get_market_from_symbol("005930.KS") == "South Korea"
        assert get_market_from_symbol("005930.KQ") == "South Korea"
        assert get_market_from_symbol("005930.ks") == "South Korea"
        assert get_market_from_symbol("005930.kq") == "South Korea"

    def test_Japan_market(self):
        """Test Japan market detection."""
        assert get_market_from_symbol("7203.T") == "Japan"
        assert get_market_from_symbol("7203.t") == "Japan"

    def test_Taiwan_market(self):
        """Test Taiwan market detection."""
        assert get_market_from_symbol("2330.TW") == "Taiwan"
        assert get_market_from_symbol("2330.tw") == "Taiwan"

    def test_united_states_market(self):
        """Test United States market detection."""
        assert get_market_from_symbol("AAPL.US") == "United States"
        assert get_market_from_symbol("AAPL.us") == "United States"

    def test_default_market(self):
        """Test default market for symbols without recognized suffix."""
        assert get_market_from_symbol("AAPL") == "United States"
        assert get_market_from_symbol("MSFT.NASDAQ") == "United States"
        assert get_market_from_symbol("GOOGL.XX") == "United States"

    def test_case_insensitive(self):
        """Test case insensitive symbol processing."""
        assert get_market_from_symbol("aapl.us") == "United States"
        assert get_market_from_symbol("AAPL.US") == "United States"
        assert get_market_from_symbol("AaPl.Us") == "United States"


class TestCheckSymbolExists:
    """Test cases for check_symbol_exists function."""

    def test_symbol_exists_success(self):
        """Test successful symbol existence check."""

        result = check_symbol_exists("AAPL")

        assert result is True

    def test_symbol_exists_failure(self):
        """Test symbol existence check when symbol doesn't exist."""

        result = check_symbol_exists("INVALID")
        assert result is False


class TestGetYfSymbolAndMarket:
    """Test cases for get_yf_symbol_and_market function."""

    def test_none_symbol(self):
        """Test with None symbol."""
        result = get_yf_symbol_and_market(None)
        assert result == (None, None)

        result = get_yf_symbol_and_market(None, "United States")
        assert result == (None, None)

    def test_hong_kong_market(self):
        """Test Hong Kong market symbol formatting."""

        result = get_yf_symbol_and_market("700", "Hong Kong")

        assert result == ("0700.HK", "Hong Kong")

    def test_hong_kong_market_from_symbol(self):
        """Test Hong Kong market detection from symbol."""

        result1 = get_yf_symbol_and_market("0700.HK")
        assert result1 == ("0700.HK", "Hong Kong")

        result2 = get_yf_symbol_and_market("9618.HK")
        assert result2 == ("9618.HK", "Hong Kong")


    def test_south_korea_market_ks(self):
        """Test South Korea market with .KS suffix."""

        result = get_yf_symbol_and_market("5930", "South Korea")
        assert result == ("005930.KS", "South Korea")

    def test_south_korea_market_kq_fallback(self):
        """Test South Korea market fallback behavior."""
        result = get_yf_symbol_and_market("5930", "South Korea")

        # Note: This test will make real API calls to check symbol existence
        # Result depends on whether the symbol actually exists in the data source
        if result[0] is not None:
            assert result[1] == "South Korea"
            assert result[0].startswith("005930.")
            assert result[0].endswith((".KS", ".KQ"))

    def test_japan_market(self):
        """Test Japan market symbol formatting."""
        result = get_yf_symbol_and_market("7203", "Japan")

        # Note: This test will make real API calls to check symbol existence
        # Result depends on whether 7203.T actually exists in the data source
        if result[0] is not None:
            assert result[1] == "Japan"
            assert result[0] == "7203.T"

    def test_taiwan_market_tw(self):
        """Test Taiwan market with .TW suffix."""
        result = get_yf_symbol_and_market("2330", "Taiwan")

        # Note: This test will make real API calls to check symbol existence
        # Result depends on whether the symbol actually exists in the data source
        if result[0] is not None:
            assert result[1] == "Taiwan"
            assert result[0].startswith("2330.")

        result2 = get_yf_symbol_and_market("5483", "Taiwan")
        assert result2 == ("5483.TWO", "Taiwan")


    def test_taiwan_market_two_fallback(self):
        """Test Taiwan market fallback behavior."""
        result = get_yf_symbol_and_market("2330", "Taiwan")

        # Note: This test will make real API calls to check symbol existence
        # Result depends on whether the symbol actually exists in the data source
        if result[0] is not None:
            assert result[1] == "Taiwan"
            assert result[0].startswith("2330.")
            assert result[0].endswith((".TW", ".TWO"))

    def test_us_market(self):
        """Test US market symbol formatting."""
        result = get_yf_symbol_and_market("AAPL", "United States")

        # Note: This test will make real API calls to check symbol existence
        # Result depends on whether AAPL actually exists in the data source
        if result[0] is not None:
            assert result[1] == "United States"
            assert result[0] == "AAPL"

    def test_default_market_us(self):
        """Test default market (US) symbol formatting."""
        result = get_yf_symbol_and_market("AAPL", "other market")

        # Note: This test will make real API calls to check symbol existence
        # Result depends on whether AAPL actually exists in the data source
        if result[0] is not None:
            assert result[1] == "other market"
            assert result[0] == "AAPL"

    def test_no_market_parameter_infers_from_symbol(self):
        """Test market inference when no market parameter provided."""
        result = get_yf_symbol_and_market("0700.HK")

        # Note: This test will make real API calls to check symbol existence
        # Result depends on whether 0700.HK actually exists in the data source
        if result[0] is not None:
            assert result[1] == "Hong Kong"
            assert result[0] == "0700.HK"

    def test_symbol_not_found_returns_none(self):
        """Test when symbol is not found in any format."""
        result = get_yf_symbol_and_market("INVALID", "United States")

        # Note: This test will make real API calls to check symbol existence
        # For an invalid symbol, it should return (None, None)
        assert result == (None, None)

    def test_south_korea_both_suffixes_fail(self):
        """Test South Korea market when both .KS and .KQ fail."""
        result = get_yf_symbol_and_market("INVALID", "South Korea")

        # Note: This test will make real API calls to check symbol existence
        # For an invalid symbol, it should return (None, None)
        assert result == (None, None)

    def test_taiwan_both_suffixes_fail(self):
        """Test Taiwan market when both .TW and .TWO fail."""
        result = get_yf_symbol_and_market("INVALID", "Taiwan")

        # Note: This test will make real API calls to check symbol existence
        # For an invalid symbol, it should return (None, None)
        assert result == (None, None)

    def test_hong_kong_zero_padding(self):
        """Test Hong Kong market zero padding for short symbols."""
        # Note: These tests will make real API calls to check symbol existence
        # Results depend on whether these symbols actually exist in the data source

        # Test various lengths - only assert if symbol exists
        result = get_yf_symbol_and_market("1", "Hong Kong")
        if result[0] is not None:
            assert result == ("0001.HK", "Hong Kong")

        result = get_yf_symbol_and_market("12", "Hong Kong")
        if result[0] is not None:
            assert result[0].startswith("00") and result[0].endswith(".HK")
            assert result[1] == "Hong Kong"

        result = get_yf_symbol_and_market("123", "Hong Kong")
        if result[0] is not None:
            assert result[0].startswith("0") and result[0].endswith(".HK")
            assert result[1] == "Hong Kong"

    def test_south_korea_zero_padding(self):
        """Test South Korea market zero padding for short symbols."""
        # Note: These tests will make real API calls to check symbol existence
        # Results depend on whether these symbols actually exist in the data source

        # Test various lengths - only assert if symbol exists
        result = get_yf_symbol_and_market("1", "South Korea")
        if result[0] is not None:
            assert result[1] == "South Korea"
            assert result[0].startswith("00000") and result[0].endswith((".KS", ".KQ"))

        result = get_yf_symbol_and_market("123", "South Korea")
        if result[0] is not None:
            assert result[1] == "South Korea"
            assert result[0].startswith("000") and result[0].endswith((".KS", ".KQ"))

    def test_edge_cases_integration(self):
        """Test integration of edge cases across functions."""
        # Test that market detection and symbol cleaning work together
        # Note: These tests will make real API calls to check symbol existence

        # Symbol with suffix should be cleaned and market detected
        result = get_yf_symbol_and_market("0700.HK")
        if result[0] is not None:
            assert result == ("0700.HK", "Hong Kong")

        # Symbol already in correct format
        result = get_yf_symbol_and_market("AAPL")
        if result[0] is not None:
            assert result == ("AAPL", "United States")


class TestCheckYfSymbolExists:
    """Test cases for check_yf_symbol_exists function."""

    @pytest.mark.asyncio
    async def test_valid_us_symbol_exists(self):
        """Test that a valid US symbol exists in the database."""
        result = await check_yf_symbol_exists("AAPL", "United States")
        assert isinstance(result, bool)
        # AAPL should exist in most databases, but we'll accept either result
        # as long as it returns a boolean

    @pytest.mark.asyncio
    async def test_valid_us_symbol_without_market(self):
        """Test that a valid US symbol exists without specifying market."""
        result = await check_yf_symbol_exists("AAPL")
        assert isinstance(result, bool)

    @pytest.mark.asyncio
    async def test_invalid_symbol_returns_false(self):
        """Test that an invalid symbol returns False."""
        result = await check_yf_symbol_exists("INVALID_SYMBOL_12345", "United States")
        assert result is False

    @pytest.mark.asyncio
    async def test_hong_kong_symbol_exists(self):
        """Test Hong Kong symbol existence check."""
        result = await check_yf_symbol_exists("0700.HK", "Hong Kong")
        assert isinstance(result, bool)

    @pytest.mark.asyncio
    async def test_south_korea_symbol_exists(self):
        """Test South Korea symbol existence check."""
        result = await check_yf_symbol_exists("005930.KS", "South Korea")
        assert isinstance(result, bool)

    @pytest.mark.asyncio
    async def test_japan_symbol_exists(self):
        """Test Japan symbol existence check."""
        result = await check_yf_symbol_exists("7203.T", "Japan")
        assert isinstance(result, bool)

    @pytest.mark.asyncio
    async def test_taiwan_symbol_exists(self):
        """Test Taiwan symbol existence check."""
        result = await check_yf_symbol_exists("2330.TW", "Taiwan")
        assert isinstance(result, bool)

    @pytest.mark.asyncio
    async def test_empty_symbol(self):
        """Test with empty symbol."""
        result = await check_yf_symbol_exists("", "United States")
        assert result is False

    @pytest.mark.asyncio
    async def test_none_symbol(self):
        """Test with None symbol."""
        # This might raise an exception or return False depending on implementation
        try:
            result = await check_yf_symbol_exists(None, "United States")
            assert result is False
        except (TypeError, AttributeError):
            # Function might not handle None gracefully, which is acceptable
            pass

    @pytest.mark.asyncio
    async def test_case_sensitivity(self):
        """Test case sensitivity of symbol lookup."""
        # Test both uppercase and lowercase versions
        result_upper = await check_yf_symbol_exists("AAPL", "United States")
        result_lower = await check_yf_symbol_exists("aapl", "United States")

        assert isinstance(result_upper, bool)
        assert isinstance(result_lower, bool)
        # Results might differ based on database implementation

    @pytest.mark.asyncio
    async def test_different_markets_same_symbol(self):
        """Test same symbol in different markets."""
        # Some symbols might exist in multiple markets
        us_result = await check_yf_symbol_exists("AAPL", "United States")
        hk_result = await check_yf_symbol_exists("AAPL", "Hong Kong")

        assert isinstance(us_result, bool)
        assert isinstance(hk_result, bool)

    @pytest.mark.asyncio
    async def test_symbol_with_special_characters(self):
        """Test symbols with special characters."""
        result = await check_yf_symbol_exists("BRK.A", "United States")
        assert isinstance(result, bool)

    @pytest.mark.asyncio
    async def test_numeric_symbol(self):
        """Test purely numeric symbols (common in Asian markets)."""
        result = await check_yf_symbol_exists("0700", "Hong Kong")
        assert isinstance(result, bool)

    @pytest.mark.asyncio
    async def test_symbol_with_leading_zeros(self):
        """Test symbols with leading zeros."""
        result = await check_yf_symbol_exists("0001.HK", "Hong Kong")
        assert isinstance(result, bool)

    @pytest.mark.asyncio
    async def test_very_long_symbol(self):
        """Test with unusually long symbol."""
        long_symbol = "A" * 50  # Very long symbol
        result = await check_yf_symbol_exists(long_symbol, "United States")
        assert result is False

    @pytest.mark.asyncio
    async def test_symbol_with_numbers_and_letters(self):
        """Test mixed alphanumeric symbols."""
        result = await check_yf_symbol_exists("ABC123", "United States")
        assert isinstance(result, bool)

    @pytest.mark.asyncio
    async def test_unsupported_market(self):
        """Test with unsupported market parameter."""
        # This should still work as it falls back to default table
        result = await check_yf_symbol_exists("AAPL", "Unsupported Market")
        assert isinstance(result, bool)


class TestGetYfSymbolAndMarketWithDbCheck:
    """Test cases for get_yf_symbol_and_market function using check_yf_symbol_exists."""

    @pytest.mark.asyncio
    async def test_us_symbol_with_db_check(self):
        """Test US symbol with database check function."""
        result = await get_yf_symbol_and_market("AAPL", "United States", check_yf_symbol_exists)

        # Should return a tuple
        assert isinstance(result, tuple)
        assert len(result) == 2

        symbol, market = result
        if symbol is not None:
            assert market == "United States"
            assert symbol == "AAPL"
        else:
            # If symbol doesn't exist in DB, both should be None
            assert market is None

    @pytest.mark.asyncio
    async def test_hong_kong_symbol_with_db_check(self):
        """Test Hong Kong symbol with database check function."""
        result = await get_yf_symbol_and_market("700", "Hong Kong", check_yf_symbol_exists)

        assert isinstance(result, tuple)
        assert len(result) == 2

        symbol, market = result
        if symbol is not None:
            assert market == "Hong Kong"
            assert symbol == "0700.HK"
        else:
            assert market is None

    @pytest.mark.asyncio
    async def test_south_korea_symbol_with_db_check(self):
        """Test South Korea symbol with database check function."""
        result = await get_yf_symbol_and_market("5930", "South Korea", check_yf_symbol_exists)

        assert isinstance(result, tuple)
        assert len(result) == 2

        symbol, market = result
        if symbol is not None:
            assert market == "South Korea"
            assert symbol.startswith("005930.")
            assert symbol.endswith((".KS", ".KQ"))
        else:
            assert market is None

    @pytest.mark.asyncio
    async def test_japan_symbol_with_db_check(self):
        """Test Japan symbol with database check function."""
        result = await get_yf_symbol_and_market("7203", "Japan", check_yf_symbol_exists)

        assert isinstance(result, tuple)
        assert len(result) == 2

        symbol, market = result
        if symbol is not None:
            assert market == "Japan"
            assert symbol == "7203.T"
        else:
            assert market is None

    @pytest.mark.asyncio
    async def test_taiwan_symbol_with_db_check(self):
        """Test Taiwan symbol with database check function."""
        result = await get_yf_symbol_and_market("2330", "Taiwan", check_yf_symbol_exists)

        assert isinstance(result, tuple)
        assert len(result) == 2

        symbol, market = result
        if symbol is not None:
            assert market == "Taiwan"
            assert symbol.startswith("2330.")
            assert symbol.endswith((".TW", ".TWO"))
        else:
            assert market is None

    @pytest.mark.asyncio
    async def test_invalid_symbol_with_db_check(self):
        """Test invalid symbol with database check function."""
        result = await get_yf_symbol_and_market("INVALID_SYMBOL_12345", "United States", check_yf_symbol_exists)

        assert isinstance(result, tuple)
        assert len(result) == 2

        # Should return (None, None) for invalid symbol
        assert result == (None, None)

    @pytest.mark.asyncio
    async def test_symbol_with_suffix_and_db_check(self):
        """Test symbol that already has suffix with database check function."""
        result = await get_yf_symbol_and_market("0700.HK", None, check_yf_symbol_exists)

        assert isinstance(result, tuple)
        assert len(result) == 2

        symbol, market = result
        if symbol is not None:
            assert market == "Hong Kong"
            assert symbol == "0700.HK"
        else:
            assert market is None

    @pytest.mark.asyncio
    async def test_none_symbol_with_db_check(self):
        """Test None symbol with database check function."""
        result = await get_yf_symbol_and_market(None, "United States", check_yf_symbol_exists)

        assert isinstance(result, tuple)
        assert len(result) == 2
        assert result == (None, None)

    @pytest.mark.asyncio
    async def test_empty_symbol_with_db_check(self):
        """Test empty symbol with database check function."""
        result = await get_yf_symbol_and_market("", "United States", check_yf_symbol_exists)

        assert isinstance(result, tuple)
        assert len(result) == 2
        # Empty symbol should return (None, None)
        assert result == (None, None)

    @pytest.mark.asyncio
    async def test_market_inference_with_db_check(self):
        """Test market inference from symbol with database check function."""
        result = await get_yf_symbol_and_market("0700.HK", None, check_yf_symbol_exists)

        assert isinstance(result, tuple)
        assert len(result) == 2

        symbol, market = result
        if symbol is not None:
            # Market should be inferred as Hong Kong from .HK suffix
            assert market == "Hong Kong"
            assert symbol == "0700.HK"
        else:
            assert market is None

    @pytest.mark.asyncio
    async def test_fallback_behavior_with_db_check(self):
        """Test fallback behavior when first format doesn't exist in DB."""
        # Use a symbol that might not exist in .KS format but could exist in .KQ
        result = await get_yf_symbol_and_market("1234", "South Korea", check_yf_symbol_exists)

        assert isinstance(result, tuple)
        assert len(result) == 2

        symbol, market = result
        # Either finds a valid format or returns None
        if symbol is not None:
            assert market == "South Korea"
            assert symbol.endswith((".KS", ".KQ"))
        else:
            assert market is None

    @pytest.mark.asyncio
    async def test_comparison_with_and_without_db_check(self):
        """Test comparison between using DB check vs not using it."""
        # Test with a known symbol
        result_with_db = await get_yf_symbol_and_market("AAPL", "United States", check_yf_symbol_exists)
        result_without_db = await get_yf_symbol_and_market("AAPL", "United States", None)

        # Both should return tuples
        assert isinstance(result_with_db, tuple)
        assert isinstance(result_without_db, tuple)

        # If DB check finds the symbol, results should be similar
        if result_with_db[0] is not None:
            assert result_with_db[1] == result_without_db[1]  # Same market
            # Symbol format might be the same or different depending on validation
