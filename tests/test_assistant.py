"""
Tests for the OpenAI Assistant API integration.
"""
import unittest
from unittest.mock import patch, MagicMock
import json
import os
from flask import Flask

from services.assistant_service import AssistantService
from api.assistant_routes import assistant_bp
from providers.openai_assistant_provider import OpenAIAssistantProvider

class TestAssistantAPI(unittest.TestCase):
    """
    Test cases for the Assistant API.
    """
    def setUp(self):
        """Set up the test environment."""
        self.app = Flask(__name__)
        self.app.register_blueprint(assistant_bp)
        self.client = self.app.test_client()
        
        # Mock environment variables
        self.env_patcher = patch.dict('os.environ', {
            'OPENAI_API_KEY': 'test_key',
            'OPENAI_API_URL': 'https://api.openai.com/v1',
            'OPENAI_ASSISTANT_ID': 'test_assistant_id'
        })
        self.env_patcher.start()
        
    def tearDown(self):
        """Clean up after tests."""
        self.env_patcher.stop()
    
    @patch.object(OpenAIAssistantProvider, 'chat_completion')
    def test_assistant_query(self, mock_chat_completion):
        """Test the assistant query endpoint."""
        # Mock the chat completion response
        mock_response = {
            "choices": [
                {
                    "message": {
                        "role": "assistant",
                        "content": "This is a test response."
                    },
                    "finish_reason": "stop",
                    "index": 0
                }
            ],
            "created": **********,
            "model": "openai-assistant",
            "object": "chat.completion",
            "thread_id": "thread_123456",
            "run_id": "run_123456"
        }
        mock_chat_completion.return_value = mock_response
        
        # Make the request
        response = self.client.post(
            '/assistant',
            json={
                "query": "Test query",
                "model": "gpt-4-turbo-preview"
            }
        )
        
        # Check the response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data["response"], "This is a test response.")
        self.assertEqual(data["thread_id"], "thread_123456")
        self.assertEqual(data["run_id"], "run_123456")
        self.assertEqual(data["model"], "gpt-4-turbo-preview")
        
        # Check that the provider was called with the correct arguments
        mock_chat_completion.assert_called_once()
        args, kwargs = mock_chat_completion.call_args
        self.assertEqual(kwargs["model"], "gpt-4-turbo-preview")
        self.assertEqual(len(args[0]), 1)  # One message
        self.assertEqual(args[0][0]["role"], "user")
        self.assertEqual(args[0][0]["content"], "Test query")
    
    @patch.object(OpenAIAssistantProvider, 'client')
    def test_create_thread(self, mock_client):
        """Test the create thread endpoint."""
        # Mock the thread creation response
        mock_thread = MagicMock()
        mock_thread.id = "thread_123456"
        mock_client.beta.threads.create.return_value = mock_thread
        
        # Make the request
        response = self.client.post('/assistant/thread')
        
        # Check the response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data["thread_id"], "thread_123456")
        self.assertTrue(data["created"])
        
        # Check that the client was called
        mock_client.beta.threads.create.assert_called_once()
    
    @patch.object(OpenAIAssistantProvider, 'client')
    def test_delete_thread(self, mock_client):
        """Test the delete thread endpoint."""
        # Make the request
        response = self.client.delete('/assistant/thread/thread_123456')
        
        # Check the response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data["deleted"])
        self.assertEqual(data["thread_id"], "thread_123456")
        
        # Check that the client was called with the correct thread ID
        mock_client.beta.threads.delete.assert_called_once_with("thread_123456")
    
    @patch.object(OpenAIAssistantProvider, 'client')
    def test_get_thread_messages(self, mock_client):
        """Test the get thread messages endpoint."""
        # Mock the messages response
        mock_message1 = MagicMock()
        mock_message1.id = "msg_123456"
        mock_message1.role = "user"
        mock_message1.content = [MagicMock()]
        mock_message1.content[0].text.value = "Test query"
        mock_message1.created_at = **********
        
        mock_message2 = MagicMock()
        mock_message2.id = "msg_123457"
        mock_message2.role = "assistant"
        mock_message2.content = [MagicMock()]
        mock_message2.content[0].text.value = "Test response"
        mock_message2.created_at = **********
        
        mock_messages = MagicMock()
        mock_messages.data = [mock_message1, mock_message2]
        mock_client.beta.threads.messages.list.return_value = mock_messages
        
        # Make the request
        response = self.client.get('/assistant/thread/thread_123456/messages')
        
        # Check the response
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data["thread_id"], "thread_123456")
        self.assertEqual(len(data["messages"]), 2)
        
        # Check the first message
        self.assertEqual(data["messages"][0]["id"], "msg_123456")
        self.assertEqual(data["messages"][0]["role"], "user")
        self.assertEqual(data["messages"][0]["content"], "Test query")
        self.assertEqual(data["messages"][0]["created_at"], **********)
        
        # Check the second message
        self.assertEqual(data["messages"][1]["id"], "msg_123457")
        self.assertEqual(data["messages"][1]["role"], "assistant")
        self.assertEqual(data["messages"][1]["content"], "Test response")
        self.assertEqual(data["messages"][1]["created_at"], **********)
        
        # Check that the client was called with the correct thread ID
        mock_client.beta.threads.messages.list.assert_called_once_with(thread_id="thread_123456")

if __name__ == '__main__':
    unittest.main()
