<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assistant API Streaming Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        #response {
            white-space: pre-wrap;
            border: 1px solid #ccc;
            padding: 10px;
            min-height: 200px;
            margin-top: 20px;
        }
        #metadata {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
        #references, #follow-up-questions {
            margin-top: 20px;
            border: 1px solid #eee;
            padding: 10px;
            border-radius: 5px;
        }
        #references h3, #follow-up-questions h3 {
            margin-top: 0;
            color: #333;
        }
        .reference-item {
            margin: 10px 0;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border-left: 3px solid #4CAF50;
        }
        
        .reference-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .reference-snippet {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .reference-url {
            font-size: 11px;
            color: #999;
            word-break: break-all;
        }
        
        .follow-up-question {
            margin: 5px 0;
            padding: 8px;
            background-color: #f0f7ff;
            border-radius: 3px;
            cursor: pointer;
        }
        .follow-up-question {
            cursor: pointer;
        }
        .follow-up-question:hover {
            background-color: #e9e9e9;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        textarea {
            width: 100%;
            height: 100px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>Assistant API Streaming Test</h1>
    
    <div>
        <label for="endpoint">Endpoint:</label>
        <select id="endpoint">
            <option value="/assistant/stream">Assistant Stream</option>
            <option value="/assistant">Regular Assistant</option>
        </select>
    </div>
    
    <div>
        <label for="query">Query:</label>
        <textarea id="query">Tell me about the weather today</textarea>
    </div>
    
    <div>
        <label for="thread_id">Thread ID (optional):</label>
        <input type="text" id="thread_id">
    </div>
    
    <div>
        <label for="model">Model (optional):</label>
        <input type="text" id="model" value="gpt-4-turbo-preview">
    </div>
    
    <button id="send">Send Request</button>
    <button id="clear">Clear Response</button>
    
    <div id="response"></div>
    <div id="metadata"></div>
    
    <div id="references" style="display: none;">
        <h3>Reference URLs</h3>
        <div id="reference-list"></div>
    </div>
    
    <div id="follow-up-questions" style="display: none;">
        <h3>Follow-up Questions</h3>
        <div id="question-list"></div>
    </div>
    
    <script>
        document.getElementById('send').addEventListener('click', async () => {
            const endpoint = document.getElementById('endpoint').value;
            const query = document.getElementById('query').value;
            const threadId = document.getElementById('thread_id').value;
            const model = document.getElementById('model').value;
            const responseDiv = document.getElementById('response');
            const metadataDiv = document.getElementById('metadata');
            
            // Clear previous response
            responseDiv.textContent = '';
            metadataDiv.textContent = '';
            
            try {
                // Prepare request payload
                const payload = {
                    query: query,
                    stream: true
                };
                
                if (threadId) {
                    payload.thread_id = threadId;
                }
                
                if (model) {
                    payload.model = model;
                }
                
                // Make the request
                const response = await fetch(`http://localhost:8000${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify(payload)
                });
                
                // Check if response is ok
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                // Get the reader for the stream
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let fullContent = '';
                let currentThreadId = threadId;
                let currentRunId = null;
                
                // Process the stream
                while (true) {
                    const { value, done } = await reader.read();
                    
                    if (done) {
                        console.log('Stream complete');
                        break;
                    }
                    
                    // Decode the chunk and add to buffer
                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;
                    
                // Process complete messages
                const messages = buffer.split('\n\n');
                buffer = messages.pop() || ''; // Keep the last incomplete message in the buffer
                
                for (const message of messages) {
                    if (message.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(message.slice(6));
                            console.log('Received data:', data);
                            
                            // Update metadata
                            if (data.thread_id) {
                                currentThreadId = data.thread_id;
                            }
                            
                            if (data.session_id) {
                                currentThreadId = data.session_id;
                            }
                            
                            if (data.run_id) {
                                currentRunId = data.run_id;
                            }
                            
                            if (data.round_id) {
                                currentRunId = data.round_id;
                            }
                            
                            // Update metadata display
                            metadataDiv.textContent = `Thread ID: ${currentThreadId || 'None'}, Run ID: ${currentRunId || 'None'}`;
                            
                                // Handle different message types
                                if (data.type === "connection_established") {
                                    responseDiv.textContent += 'Connection established...\n';
                                } else if (data.type === "metadata_update") {
                                    // Just update the metadata display
                                } else if (data.type === "metadata") {
                                    // Handle metadata with references and follow-up questions
                                    console.log('Received metadata:', data);
                                    
                                    // Handle reference URLs
                                    const referencesDiv = document.getElementById('references');
                                    const referenceList = document.getElementById('reference-list');
                                    
                                    if (data.reference_urls && data.reference_urls.length > 0) {
                                        referenceList.innerHTML = '';
                                        
                                        data.reference_urls.forEach(ref => {
                                            const refItem = document.createElement('div');
                                            refItem.className = 'reference-item';
                                            
                                            // Handle both old format (string) and new format (object)
                                            if (typeof ref === 'string') {
                                                refItem.innerHTML = `
                                                    <div class="reference-url"><a href="${ref}" target="_blank">${ref}</a></div>
                                                `;
                                            } else {
                                                const title = ref.title || 'Reference';
                                                const snippet = ref.snippet || '';
                                                const url = ref.url || '#';
                                                
                                                refItem.innerHTML = `
                                                    <div class="reference-title">${title}</div>
                                                    ${snippet ? `<div class="reference-snippet">${snippet}</div>` : ''}
                                                    <div class="reference-url"><a href="${url}" target="_blank">${url}</a></div>
                                                `;
                                            }
                                            
                                            referenceList.appendChild(refItem);
                                        });
                                        
                                        referencesDiv.style.display = 'block';
                                    }
                                    
                                    // Handle follow-up questions
                                    const questionsDiv = document.getElementById('follow-up-questions');
                                    const questionList = document.getElementById('question-list');
                                    
                                    if (data.follow_up_questions && data.follow_up_questions.length > 0) {
                                        questionList.innerHTML = '';
                                        data.follow_up_questions.forEach(question => {
                                            const questionDiv = document.createElement('div');
                                            questionDiv.className = 'follow-up-question';
                                            questionDiv.textContent = question;
                                            questionDiv.addEventListener('click', () => {
                                                document.getElementById('query').value = question;
                                            });
                                            questionList.appendChild(questionDiv);
                                        });
                                        questionsDiv.style.display = 'block';
                                    }
                                } else if (data.type === "test") {
                                    responseDiv.textContent += `Test message ${data.index}: ${data.content}\n`;
                                } else if (data.content === '[DONE]') {
                                    // End of stream
                                    console.log('Stream complete');
                                    eventSource.close();
                                } else if (data.content) {
                                    // Regular content
                                    console.log('Received content:', data.content);
                                    fullContent += data.content;
                                    responseDiv.textContent = fullContent;
                                    responseDiv.scrollTop = responseDiv.scrollHeight;
                                } else if (data.choices && data.choices[0] && data.choices[0].delta) {
                                    // OpenAI format content delta
                                    const content = data.choices[0].delta.content || '';
                                    console.log('Received delta content:', content);
                                    fullContent += content;
                                    responseDiv.textContent = fullContent;
                                    responseDiv.scrollTop = responseDiv.scrollHeight;
                                }
                            } catch (e) {
                                console.error('Error parsing message:', e);
                                responseDiv.textContent += `\nError parsing message: ${e.message}\n`;
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('Error:', error);
                responseDiv.textContent = `Error: ${error.message}`;
            }
        });
        
        document.getElementById('clear').addEventListener('click', () => {
            document.getElementById('response').textContent = '';
            document.getElementById('metadata').textContent = '';
            document.getElementById('references').style.display = 'none';
            document.getElementById('reference-list').innerHTML = '';
            document.getElementById('follow-up-questions').style.display = 'none';
            document.getElementById('question-list').innerHTML = '';
        });
    </script>
</body>
</html>
