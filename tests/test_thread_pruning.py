"""
Test script for thread pruning functionality.

This script tests the thread pruning functionality to ensure it correctly
manages thread message length and reduces token consumption.
"""
import os
import sys
import asyncio
import json
from datetime import datetime

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.thread_pruning import (
    ThreadPruningManager, 
    prune_thread_messages,
    role_based_pruning,
    conversation_aware_pruning,
    get_pruning_metrics
)
from utils.logging import logger
from openai import AsyncOpenAI

# Set up OpenAI client
client = AsyncOpenAI(
    api_key=os.getenv('OPENAI_API_KEY'),
    base_url=os.getenv('OPENAI_API_URL', 'https://api.openai.com/v1'),
    default_headers={"OpenAI-Beta": "assistants=v2"}
)

async def create_test_thread_with_messages(num_messages=10):
    """Create a test thread with a specified number of messages."""
    logger.info(f"Creating test thread with {num_messages} messages")
    
    # Create a new thread
    thread = await client.beta.threads.create()
    thread_id = thread.id
    logger.info(f"Created thread: {thread_id}")
    
    # Add messages to the thread
    for i in range(num_messages):
        role = "user" if i % 2 == 0 else "assistant"
        content = f"Test message {i+1} for thread pruning test. This is a {role} message."
        
        if role == "user":
            await client.beta.threads.messages.create(
                thread_id=thread_id,
                role=role,
                content=content
            )
        else:
            # For assistant messages, we need to create a run
            await client.beta.threads.messages.create(
                thread_id=thread_id,
                role=role,
                content=content
            )
        
        logger.info(f"Added {role} message {i+1} to thread")
    
    # Verify the messages were added
    messages = await client.beta.threads.messages.list(thread_id=thread_id)
    logger.info(f"Thread has {len(messages.data)} messages")
    
    return thread_id

async def test_simple_pruning():
    """Test the simple pruning strategy."""
    logger.info("=== Testing Simple Pruning ===")
    
    # Create a test thread with 10 messages
    thread_id = await create_test_thread_with_messages(10)
    
    # Get initial message count
    messages = await client.beta.threads.messages.list(thread_id=thread_id)
    initial_count = len(messages.data)
    logger.info(f"Initial message count: {initial_count}")
    
    # Prune the thread to 6 messages
    result = await prune_thread_messages(thread_id, max_messages=6, client=client)
    logger.info(f"Pruning result: {json.dumps(result, indent=2)}")
    
    # Verify the message count after pruning
    messages = await client.beta.threads.messages.list(thread_id=thread_id)
    final_count = len(messages.data)
    logger.info(f"Final message count: {final_count}")
    
    # Clean up
    await client.beta.threads.delete(thread_id=thread_id)
    logger.info(f"Deleted thread: {thread_id}")
    
    return result

async def test_role_based_pruning():
    """Test the role-based pruning strategy."""
    logger.info("=== Testing Role-Based Pruning ===")
    
    # Create a test thread with 10 messages
    thread_id = await create_test_thread_with_messages(10)
    
    # Get initial message count
    messages = await client.beta.threads.messages.list(thread_id=thread_id)
    initial_count = len(messages.data)
    logger.info(f"Initial message count: {initial_count}")
    
    # Count messages by role
    user_messages = sum(1 for msg in messages.data if msg.role == "user")
    assistant_messages = sum(1 for msg in messages.data if msg.role == "assistant")
    logger.info(f"Initial user messages: {user_messages}, assistant messages: {assistant_messages}")
    
    # Prune the thread using role-based strategy
    result = await role_based_pruning(thread_id, max_messages=6, client=client)
    logger.info(f"Pruning result: {json.dumps(result, indent=2)}")
    
    # Verify the message count after pruning
    messages = await client.beta.threads.messages.list(thread_id=thread_id)
    final_count = len(messages.data)
    
    # Count messages by role after pruning
    user_messages_after = sum(1 for msg in messages.data if msg.role == "user")
    assistant_messages_after = sum(1 for msg in messages.data if msg.role == "assistant")
    
    logger.info(f"Final message count: {final_count}")
    logger.info(f"Final user messages: {user_messages_after}, assistant messages: {assistant_messages_after}")
    
    # Clean up
    await client.beta.threads.delete(thread_id=thread_id)
    logger.info(f"Deleted thread: {thread_id}")
    
    return result

async def test_conversation_aware_pruning():
    """Test the conversation-aware pruning strategy."""
    logger.info("=== Testing Conversation-Aware Pruning ===")
    
    # Create a test thread with 10 messages
    thread_id = await create_test_thread_with_messages(10)
    
    # Get initial message count
    messages = await client.beta.threads.messages.list(thread_id=thread_id)
    initial_count = len(messages.data)
    logger.info(f"Initial message count: {initial_count}")
    
    # Prune the thread using conversation-aware strategy
    result = await conversation_aware_pruning(thread_id, max_exchanges=3, client=client)
    logger.info(f"Pruning result: {json.dumps(result, indent=2)}")
    
    # Verify the message count after pruning
    messages = await client.beta.threads.messages.list(thread_id=thread_id)
    final_count = len(messages.data)
    logger.info(f"Final message count: {final_count}")
    
    # Clean up
    await client.beta.threads.delete(thread_id=thread_id)
    logger.info(f"Deleted thread: {thread_id}")
    
    return result

async def test_pruning_manager():
    """Test the ThreadPruningManager class."""
    logger.info("=== Testing ThreadPruningManager ===")
    
    # Create a test thread with 10 messages
    thread_id = await create_test_thread_with_messages(10)
    
    # Initialize the pruning manager
    manager = ThreadPruningManager(client)
    
    # Test pre-request pruning
    logger.info("Testing pre-request pruning")
    pre_result = await manager.pre_request_prune(thread_id)
    logger.info(f"Pre-request pruning result: {json.dumps(pre_result, indent=2)}")
    
    # Get message count after pre-request pruning
    messages = await client.beta.threads.messages.list(thread_id=thread_id)
    mid_count = len(messages.data)
    logger.info(f"Message count after pre-request pruning: {mid_count}")
    
    # Test post-response pruning
    logger.info("Testing post-response pruning")
    post_result = await manager.post_response_prune(thread_id)
    logger.info(f"Post-response pruning result: {json.dumps(post_result, indent=2)}")
    
    # Get message count after post-response pruning
    messages = await client.beta.threads.messages.list(thread_id=thread_id)
    final_count = len(messages.data)
    logger.info(f"Message count after post-response pruning: {final_count}")
    
    # Test pruning metrics
    metrics = await get_pruning_metrics(thread_id=thread_id)
    logger.info(f"Pruning metrics: {json.dumps(metrics, indent=2)}")
    
    # Clean up
    await client.beta.threads.delete(thread_id=thread_id)
    logger.info(f"Deleted thread: {thread_id}")
    
    return {
        "pre_request_result": pre_result,
        "post_response_result": post_result,
        "metrics": metrics
    }

async def main():
    """Run all tests."""
    logger.info("Starting thread pruning tests")
    logger.info(f"Current time: {datetime.now().isoformat()}")
    
    try:
        # Test simple pruning
        simple_result = await test_simple_pruning()
        
        # Test role-based pruning
        role_result = await test_role_based_pruning()
        
        # Test conversation-aware pruning
        conversation_result = await test_conversation_aware_pruning()
        
        # Test pruning manager
        manager_result = await test_pruning_manager()
        
        # Print summary
        logger.info("=== Test Summary ===")
        logger.info(f"Simple pruning: {'Success' if simple_result.get('messages_deleted', 0) > 0 else 'No pruning needed'}")
        logger.info(f"Role-based pruning: {'Success' if role_result.get('messages_deleted', 0) > 0 else 'No pruning needed'}")
        logger.info(f"Conversation-aware pruning: {'Success' if conversation_result.get('messages_deleted', 0) > 0 else 'No pruning needed'}")
        logger.info(f"Pruning manager: {'Success' if manager_result.get('pre_request_result', {}).get('messages_deleted', 0) > 0 or manager_result.get('post_response_result', {}).get('messages_deleted', 0) > 0 else 'No pruning needed'}")
        
    except Exception as e:
        logger.error(f"Error during tests: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
    
    logger.info("Thread pruning tests completed")

if __name__ == "__main__":
    asyncio.run(main())
