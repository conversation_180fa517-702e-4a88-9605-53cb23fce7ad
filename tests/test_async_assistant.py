"""
Tests for the Async OpenAI Assistant API integration.
"""
import unittest
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock
import json
import os

from services.async_assistant_service import AsyncAssistantService
from providers.async_openai_assistant_provider import AsyncOpenAIAssistantProvider

class TestAsyncAssistantService(unittest.TestCase):
    """
    Test cases for the Async Assistant Service.
    """
    def setUp(self):
        """Set up the test environment."""
        # Mock environment variables
        self.env_patcher = patch.dict('os.environ', {
            'OPENAI_API_KEY': 'test_key',
            'OPENAI_API_URL': 'https://api.openai.com/v1',
            'OPENAI_ASSISTANT_ID': 'test_assistant_id'
        })
        self.env_patcher.start()
        
        # Create the service
        self.service = AsyncAssistantService()
        
    def tearDown(self):
        """Clean up after tests."""
        self.env_patcher.stop()
    
    @patch.object(AsyncOpenAIAssistantProvider, 'chat_completion')
    def test_query_with_generator(self, mock_chat_completion):
        """Test the query method with an async generator response."""
        # Create a mock async generator that yields response chunks
        async def mock_generator():
            # First chunk with thread_id and run_id
            yield {
                "thread_id": "thread_123456",
                "run_id": "run_123456",
                "choices": [
                    {
                        "delta": {
                            "content": "This is "
                        }
                    }
                ]
            }
            # Second chunk with more content
            yield {
                "choices": [
                    {
                        "delta": {
                            "content": "a test "
                        }
                    }
                ]
            }
            # Final chunk with the rest of the content
            yield {
                "choices": [
                    {
                        "delta": {
                            "content": "response."
                        }
                    }
                ]
            }
        
        # Set up the mock to return our generator
        mock_chat_completion.return_value = mock_generator()
        
        # Run the test using asyncio
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(self.service.query(
            user_query="Test query",
            model="gpt-4-turbo-preview"
        ))
        
        # Check the result
        self.assertEqual(result["response"], "This is a test response.")
        self.assertEqual(result["thread_id"], "thread_123456")
        self.assertEqual(result["run_id"], "run_123456")
        self.assertEqual(result["model"], "gpt-4-turbo-preview")
        
        # Check that the provider was called with the correct arguments
        mock_chat_completion.assert_called_once()
        args, kwargs = mock_chat_completion.call_args
        self.assertEqual(kwargs["model"], "gpt-4-turbo-preview")
        self.assertEqual(len(args[0]), 1)  # One message
        self.assertEqual(args[0][0]["role"], "user")
        self.assertEqual(args[0][0]["content"], "Test query")
    
    @patch.object(AsyncOpenAIAssistantProvider, 'chat_completion')
    def test_query_with_error(self, mock_chat_completion):
        """Test the query method with an error in the generator."""
        # Create a mock async generator that yields an error
        async def mock_generator():
            yield {
                "error": "Test error",
                "choices": []
            }
        
        # Set up the mock to return our generator
        mock_chat_completion.return_value = mock_generator()
        
        # Run the test using asyncio
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(self.service.query(
            user_query="Test query",
            model="gpt-4-turbo-preview"
        ))
        
        # Check the result
        self.assertTrue("error" in result)
        self.assertTrue(result["response"].startswith("Error:"))
        
        # Check that the provider was called
        mock_chat_completion.assert_called_once()
    
    @patch.object(AsyncOpenAIAssistantProvider, '_ensure_assistant_initialized')
    @patch.object(AsyncOpenAIAssistantProvider, 'client')
    def test_create_thread(self, mock_client, mock_ensure_initialized):
        """Test the create thread method."""
        # Mock the thread creation response
        mock_thread = MagicMock()
        mock_thread.id = "thread_123456"
        mock_client.beta.threads.create = AsyncMock(return_value=mock_thread)
        
        # Run the test using asyncio
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(self.service.create_thread())
        
        # Check the result
        self.assertEqual(result["thread_id"], "thread_123456")
        self.assertTrue(result["created"])
        
        # Check that the client was called
        mock_client.beta.threads.create.assert_called_once()
        mock_ensure_initialized.assert_called_once()

if __name__ == '__main__':
    unittest.main()
