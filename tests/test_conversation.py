import pytest
from unittest.mock import patch, MagicMock
import json
from core.conversation import Conversation<PERSON>anager

@pytest.fixture
def redis_mock():
    with patch('core.conversation.redis_client') as mock:
        yield mock

def test_conversation_init(redis_mock):
    """Test conversation manager initialization."""
    session_id = 'test-session'
    manager = ConversationManager(session_id)
    assert manager.session_id == session_id
    assert manager.history_key == f"conversation:{session_id}"

def test_add_user_message(redis_mock):
    """Test adding a user message."""
    session_id = 'test-session'
    manager = ConversationManager(session_id)
    
    message = manager.add_user_message('Hello')
    
    assert message['role'] == 'user'
    assert message['content'] == 'Hello'
    assert 'timestamp' in message
    
    # Check that the message was added to Redis
    redis_mock.rpush.assert_called_once()
    args = redis_mock.rpush.call_args[0]
    assert args[0] == f"conversation:{session_id}"
    # The second argument is the JSON string of the message
    stored_message = json.loads(args[1])
    assert stored_message['role'] == 'user'
    assert stored_message['content'] == 'Hello'

def test_add_assistant_message(redis_mock):
    """Test adding an assistant message."""
    session_id = 'test-session'
    manager = ConversationManager(session_id)
    
    message = manager.add_assistant_message('Response', provider='openai', model='gpt-4')
    
    assert message['role'] == 'assistant'
    assert message['content'] == 'Response'
    assert message['provider'] == 'openai'
    assert message['model'] == 'gpt-4'
    assert 'timestamp' in message
    
    # Check that the message was added to Redis
    redis_mock.rpush.assert_called_once()

def test_get_history(redis_mock):
    """Test getting conversation history."""
    session_id = 'test-session'
    manager = ConversationManager(session_id)
    
    # Mock Redis response
    redis_mock.lrange.return_value = [
        json.dumps({'role': 'user', 'content': 'Hello'}),
        json.dumps({'role': 'assistant', 'content': 'Hi there'})
    ]
    
    history = manager.get_history()
    
    assert len(history) == 2
    assert history[0]['role'] == 'user'
    assert history[0]['content'] == 'Hello'
    assert history[1]['role'] == 'assistant'
    assert history[1]['content'] == 'Hi there'
    
    redis_mock.lrange.assert_called_once_with(f"conversation:{session_id}", 0, -1)

def test_clear_history(redis_mock):
    """Test clearing conversation history."""
    session_id = 'test-session'
    manager = ConversationManager(session_id)
    
    manager.clear_history()
    
    redis_mock.delete.assert_called_once_with(f"conversation:{session_id}")
