<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assistant API Streaming Test (Fetch API)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        #response {
            white-space: pre-wrap;
            border: 1px solid #ccc;
            padding: 10px;
            min-height: 200px;
            margin-top: 20px;
        }
        #metadata {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        textarea {
            width: 100%;
            height: 100px;
            margin-top: 10px;
        }
        #log {
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            color: #666;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
        }
    </style>
</head>
<body>
    <h1>Assistant API Streaming Test (Fetch API)</h1>
    
    <div>
        <label for="query">Query:</label>
        <textarea id="query">Tell me about the weather today</textarea>
    </div>
    
    <div>
        <label for="thread_id">Thread ID (optional):</label>
        <input type="text" id="thread_id">
    </div>
    
    <div>
        <label for="model">Model (optional):</label>
        <input type="text" id="model" value="gpt-4-turbo-preview">
    </div>
    
    <button id="send">Send Request</button>
    <button id="clear">Clear Response</button>
    
    <div id="response"></div>
    <div id="metadata"></div>
    <div id="log"></div>
    
    <script>
        // Helper function to log messages
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        document.getElementById('send').addEventListener('click', async () => {
            const query = document.getElementById('query').value;
            const threadId = document.getElementById('thread_id').value;
            const model = document.getElementById('model').value;
            const responseDiv = document.getElementById('response');
            const metadataDiv = document.getElementById('metadata');
            
            // Clear previous response
            responseDiv.textContent = 'Waiting for response...';
            metadataDiv.textContent = '';
            
            // Build the URL with query parameters
            let url = `http://localhost:8080/assistant/stream?query=${encodeURIComponent(query)}`;
            if (threadId) {
                url += `&thread_id=${encodeURIComponent(threadId)}`;
            }
            if (model) {
                url += `&model=${encodeURIComponent(model)}`;
            }
            
            log(`Connecting to ${url}`);
            
            try {
                // Make the fetch request
                const response = await fetch(url);
                
                log(`Response status: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                if (!response.body) {
                    throw new Error('ReadableStream not supported in this browser.');
                }
                
                // Get a reader from the response body stream
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullContent = '';
                
                log('Starting to read stream');
                
                // Read the stream
                while (true) {
                    const { value, done } = await reader.read();
                    
                    if (done) {
                        log('Stream complete');
                        break;
                    }
                    
                    // Decode the chunk
                    const chunk = decoder.decode(value, { stream: true });
                    log(`Received chunk: ${chunk.length} bytes`);
                    
                    // Process the chunk (which may contain multiple SSE messages)
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.substring(6));
                                log(`Parsed data: ${JSON.stringify(data)}`);
                                
                                // Update metadata
                                if (data.session_id || data.round_id) {
                                    metadataDiv.textContent = `Session ID: ${data.session_id || 'None'}, Round ID: ${data.round_id || 'None'}`;
                                }
                                
                                // Handle content
                                if (data.content === '[DONE]') {
                                    log('Stream complete marker received');
                                } else if (data.content) {
                                    fullContent += data.content;
                                    responseDiv.textContent = fullContent;
                                }
                            } catch (e) {
                                log(`Error parsing JSON: ${e.message}`);
                            }
                        }
                    }
                }
            } catch (error) {
                log(`Error: ${error.message}`);
                responseDiv.textContent = `Error: ${error.message}`;
            }
        });
        
        document.getElementById('clear').addEventListener('click', () => {
            document.getElementById('response').textContent = '';
            document.getElementById('metadata').textContent = '';
            document.getElementById('log').innerHTML = '';
        });
    </script>
</body>
</html>
