import unittest
from unittest.mock import patch, AsyncMock, MagicMock
import pytest
from datetime import datetime

from utils.sec_db_functions import query_sec_filing_file_id_from_db


@pytest.mark.asyncio
class TestSecDbFunctions:
    """Test cases for SEC database functions."""

    @patch('utils.sec_db_functions.db_manager')
    async def test_query_sec_filing_file_id_with_valid_data(self, mock_db_manager):
        """Test retrieving a file ID with valid ticker and fiscal year."""
        # Set up the mock
        mock_session = AsyncMock()
        mock_db_manager.get_db_session = AsyncMock()
        mock_db_manager.get_db_session.return_value.__aenter__.return_value = mock_session
        
        # Mock the execute method and its result
        mock_result = MagicMock()
        mock_fetchone = MagicMock()
        mock_fetchone.return_value = ('id1', 'file_id_12345', '', '2023', '4')
        mock_result.fetchone = mock_fetchone
        mock_session.execute.return_value = mock_result
        
        # Call the function with test parameters
        result = await query_sec_filing_file_id_from_db('AAPL', fiscal_year='2023')
        
        # Validate the results
        assert result == 'file_id_12345'  # file_id
        mock_session.execute.assert_called_once()
        
        # Check that the SQL query was constructed correctly
        args, _ = mock_session.execute.call_args
        assert 'ticker = :ticker' in args[0].text
        assert 'fiscal_year = :fiscal_year' in args[0].text
        assert args[1]['ticker'] == 'AAPL'
        assert args[1]['fiscal_year'] == '2023'

    @patch('utils.sec_db_functions.db_manager')
    async def test_query_sec_filing_file_id_with_fiscal_quarter(self, mock_db_manager):
        """Test retrieving a file ID with valid ticker, fiscal year, and fiscal quarter."""
        # Set up the mock
        mock_session = AsyncMock()
        mock_db_manager.get_db_session = AsyncMock()
        mock_db_manager.get_db_session.return_value.__aenter__.return_value = mock_session
        
        # Mock the execute method and its result
        mock_result = MagicMock()
        mock_fetchone = MagicMock()
        mock_fetchone.return_value = ('id1', 'file_id_q2', '', '2023', '2')
        mock_result.fetchone = mock_fetchone
        mock_session.execute.return_value = mock_result
        
        # Call the function with test parameters including fiscal quarter
        result = await query_sec_filing_file_id_from_db('AAPL', fiscal_year='2023', fiscal_quarter='Q2')
        
        # Validate the results
        assert result == 'file_id_q2'     # file_id
        mock_session.execute.assert_called_once()
        
        # Check that the SQL query includes fiscal quarter
        args, _ = mock_session.execute.call_args
        assert 'ticker = :ticker' in args[0].text
        assert 'fiscal_year = :fiscal_year' in args[0].text
        assert 'fiscal_quarter = :fiscal_quarter' in args[0].text
        assert args[1]['ticker'] == 'AAPL'
        assert args[1]['fiscal_year'] == '2023'
        assert args[1]['fiscal_quarter'] == '2'

    @patch('utils.sec_db_functions.db_manager')
    async def test_query_sec_filing_file_id_with_latest_fiscal_year(self, mock_db_manager):
        """Test retrieving a file ID with 'latest' as fiscal year."""
        # Set up the mock
        mock_session = AsyncMock()
        mock_db_manager.get_db_session = AsyncMock()
        mock_db_manager.get_db_session.return_value.__aenter__.return_value = mock_session
        
        # Mock the execute method and its result
        mock_result = MagicMock()
        mock_fetchone = MagicMock()
        current_year = str(datetime.now().year)
        mock_fetchone.return_value = ('id1', f'file_id_{current_year}', '', current_year, None)
        mock_result.fetchone = mock_fetchone
        mock_session.execute.return_value = mock_result
        
        # Call the function with 'latest' as fiscal_year
        result = await query_sec_filing_file_id_from_db('AAPL', fiscal_year='latest')
        
        # Validate the results
        assert result == f'file_id_{current_year}'  # file_id
        mock_session.execute.assert_called_once()
        
        # Check that the current year was used in the query
        args, _ = mock_session.execute.call_args
        assert 'ticker = :ticker' in args[0].text
        assert 'fiscal_year = :fiscal_year' not in args[0].text
        assert 'fiscal_quarter = :fiscal_quarter' not in args[0].text
        assert args[1]['ticker'] == 'AAPL'

    @patch('utils.sec_db_functions.db_manager')
    async def test_query_sec_filing_file_id_with_none_fiscal_year(self, mock_db_manager):
        """Test retrieving a file ID with None as fiscal year."""
        # Set up the mock
        mock_session = AsyncMock()
        mock_db_manager.get_db_session = AsyncMock()
        mock_db_manager.get_db_session.return_value.__aenter__.return_value = mock_session
        
        # Mock the execute method and its result
        mock_result = MagicMock()
        mock_fetchone = MagicMock()
        current_year = str(datetime.now().year)
        mock_fetchone.return_value = ('id1', f'file_id_{current_year}', '', current_year, None)
        mock_result.fetchone = mock_fetchone
        mock_session.execute.return_value = mock_result
        
        # Call the function with None as fiscal_year
        result = await query_sec_filing_file_id_from_db('AAPL', fiscal_year=None)
        
        # Validate the results
        assert result == f'file_id_{current_year}'  # file_id
        mock_session.execute.assert_called_once()
        
        # Check that the current year was used in the query
        args, _ = mock_session.execute.call_args
        assert 'ticker = :ticker' in args[0].text
        assert 'fiscal_year = :fiscal_year' not in args[0].text
        assert args[1]['ticker'] == 'AAPL'

    @patch('utils.sec_db_functions.db_manager')
    async def test_query_sec_filing_file_id_with_none_fiscal_quarter(self, mock_db_manager):
        """Test retrieving a file ID with None as fiscal quarter."""
        # Set up the mock
        mock_session = AsyncMock()
        mock_db_manager.get_db_session = AsyncMock()
        mock_db_manager.get_db_session.return_value.__aenter__.return_value = mock_session
        
        # Mock the execute method and its result
        mock_result = MagicMock()
        mock_fetchone = MagicMock()
        current_quarter = (datetime.now().month - 1) // 3 + 1
        mock_fetchone.return_value = ('id1', f'file_id_q{current_quarter}', '', '2023', f'{current_quarter}')
        mock_result.fetchone = mock_fetchone
        mock_session.execute.return_value = mock_result
        
        # Call the function with None as fiscal_quarter
        result = await query_sec_filing_file_id_from_db('AAPL', fiscal_year='2023', fiscal_quarter=None)
        
        # Validate the results
        assert result == f'file_id_q{current_quarter}'  # file_id
        mock_session.execute.assert_called_once()
        
        # Check that the current quarter was used in the query
        args, _ = mock_session.execute.call_args
        assert 'ticker = :ticker' in args[0].text
        assert 'fiscal_year = :fiscal_year' in args[0].text
        assert 'fiscal_quarter = :fiscal_quarter' not in args[0].text
        assert args[1]['ticker'] == 'AAPL'
        assert args[1]['fiscal_year'] == '2023'

    @patch('utils.sec_db_functions.db_manager')
    async def test_query_sec_filing_file_id_no_results(self, mock_db_manager):
        """Test retrieving a file ID when no results are found."""
        # Set up the mock
        mock_session = AsyncMock()
        mock_db_manager.get_db_session = AsyncMock()
        mock_db_manager.get_db_session.return_value.__aenter__.return_value = mock_session
        
        # Mock the execute method to return None
        mock_result = MagicMock()
        mock_result.fetchone.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Call the function
        result = await query_sec_filing_file_id_from_db('NONEXISTENT', fiscal_year='2023')
        
        # Validate the results
        assert result is None  # file_id
        mock_session.execute.assert_called_once()

        args, _ = mock_session.execute.call_args
        assert 'ticker = :ticker' in args[0].text
        assert 'fiscal_year = :fiscal_year' in args[0].text
        assert 'fiscal_quarter = :fiscal_quarter' not in args[0].text
        assert args[1]['ticker'] == 'NONEXISTENT'
        assert args[1]['fiscal_year'] == '2023'

    @patch('utils.sec_db_functions.db_manager')
    async def test_query_sec_filing_file_id_exception(self, mock_db_manager):
        """Test handling of exceptions in the function."""
        # Set up the mock to raise an exception
        mock_db_manager.get_db_session = AsyncMock()
        mock_db_manager.get_db_session.return_value.__aenter__.side_effect = Exception("Database error")
        
        # Call the function and check that it handles the exception
        result = await query_sec_filing_file_id_from_db('AAPL', fiscal_year='2023')
        
        # Validate the results
        assert result == None
