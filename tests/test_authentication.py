"""
Tests for authentication middleware and public endpoints.
"""
import pytest
import json
from app import app as flask_app
from async_app import app as fastapi_app
from fastapi.testclient import TestClient

# Flask test client
@pytest.fixture
def flask_client():
    with flask_app.test_client() as client:
        yield client

# FastAPI test client
@pytest.fixture
def fastapi_client():
    client = TestClient(fastapi_app)
    yield client

class TestFlaskAuthentication:
    """
    Tests for Flask authentication middleware.
    """
    
    def test_authenticated_endpoint_with_auth(self, flask_client):
        """
        Test that authenticated endpoint works with auth0_sub header.
        """
        response = flask_client.post(
            '/assistant',
            json={"query": "Hello, world!"},
            headers={"auth0_sub": "test-user"}
        )
        assert response.status_code == 200
        
    def test_authenticated_endpoint_without_auth(self, flask_client):
        """
        Test that authenticated endpoint returns 403 without auth0_sub header.
        """
        response = flask_client.post(
            '/assistant',
            json={"query": "Hello, world!"}
        )
        assert response.status_code == 403
        assert "Authentication required" in response.get_data(as_text=True)
        
    def test_public_endpoint(self, flask_client):
        """
        Test that public endpoint works without auth0_sub header.
        """
        response = flask_client.post(
            '/pub/assistant',
            json={"query": "Hello, world!"}
        )
        assert response.status_code == 200
        
    def test_query_endpoint_with_auth(self, flask_client):
        """
        Test that query endpoint works with auth0_sub header.
        """
        response = flask_client.post(
            '/query',
            json={"query": "Hello, world!"},
            headers={"auth0_sub": "test-user"}
        )
        assert response.status_code == 200
        
    def test_query_endpoint_without_auth(self, flask_client):
        """
        Test that query endpoint returns 403 without auth0_sub header.
        """
        response = flask_client.post(
            '/query',
            json={"query": "Hello, world!"}
        )
        assert response.status_code == 403
        assert "Authentication required" in response.get_data(as_text=True)
        
    def test_public_query_endpoint(self, flask_client):
        """
        Test that public query endpoint works without auth0_sub header.
        """
        response = flask_client.post(
            '/pub/query',
            json={"query": "Hello, world!"}
        )
        assert response.status_code == 200

class TestFastAPIAuthentication:
    """
    Tests for FastAPI authentication middleware.
    """
    
    def test_authenticated_endpoint_with_auth(self, fastapi_client):
        """
        Test that authenticated endpoint works with auth0_sub header.
        """
        response = fastapi_client.post(
            '/assistant',
            json={"query": "Hello, world!"},
            headers={"auth0_sub": "test-user"}
        )
        assert response.status_code == 200
        
    def test_authenticated_endpoint_without_auth(self, fastapi_client):
        """
        Test that authenticated endpoint returns 403 without auth0_sub header.
        """
        response = fastapi_client.post(
            '/assistant',
            json={"query": "Hello, world!"}
        )
        assert response.status_code == 403
        assert "Authentication required" in response.text
        
    def test_public_endpoint(self, fastapi_client):
        """
        Test that public endpoint works without auth0_sub header.
        """
        response = fastapi_client.post(
            '/pub/assistant',
            json={"query": "Hello, world!"}
        )
        assert response.status_code == 200
        
    def test_query_endpoint_with_auth(self, fastapi_client):
        """
        Test that query endpoint works with auth0_sub header.
        """
        response = fastapi_client.post(
            '/query',
            json={"query": "Hello, world!"},
            headers={"auth0_sub": "test-user"}
        )
        assert response.status_code == 200
        
    def test_query_endpoint_without_auth(self, fastapi_client):
        """
        Test that query endpoint returns 403 without auth0_sub header.
        """
        response = fastapi_client.post(
            '/query',
            json={"query": "Hello, world!"}
        )
        assert response.status_code == 403
        assert "Authentication required" in response.text
        
    def test_public_query_endpoint(self, fastapi_client):
        """
        Test that public query endpoint works without auth0_sub header.
        """
        response = fastapi_client.post(
            '/pub/query',
            json={"query": "Hello, world!"}
        )
        assert response.status_code == 200

class TestRedisThreadPersistence:
    """
    Tests for Redis thread persistence.
    """
    
    def test_thread_persistence_flask(self, flask_client, monkeypatch):
        """
        Test that thread ID is stored in Redis for authenticated requests.
        """
        # Mock Redis client
        class MockRedis:
            def __init__(self):
                self.data = {}
                
            def get(self, key):
                return self.data.get(key)
                
            def set(self, key, value):
                self.data[key] = value
                
            def delete(self, key):
                if key in self.data:
                    del self.data[key]
        
        mock_redis = MockRedis()
        
        # Mock get_redis_client to return our mock
        def mock_get_redis_client():
            return mock_redis
            
        # Apply the monkeypatch
        monkeypatch.setattr('utils.redis_client.get_redis_client', mock_get_redis_client)
        
        # Create a thread
        response = flask_client.post(
            '/assistant/thread',
            headers={"auth0_sub": "test-user"}
        )
        assert response.status_code == 200
        data = json.loads(response.get_data(as_text=True))
        thread_id = data.get('thread_id')
        
        # Check that the thread ID was stored in Redis
        assert mock_redis.data.get('thread:test-user') == thread_id
        
        # Delete the thread
        response = flask_client.delete(
            f'/assistant/thread/{thread_id}',
            headers={"auth0_sub": "test-user"}
        )
        assert response.status_code == 200
        
        # Check that the thread ID was removed from Redis
        assert 'thread:test-user' not in mock_redis.data
    
    def test_thread_persistence_fastapi(self, fastapi_client, monkeypatch):
        """
        Test that thread ID is stored in Redis for authenticated requests.
        """
        # Mock Redis client
        class MockAsyncRedis:
            def __init__(self):
                self.data = {}
                
            async def get(self, key):
                return self.data.get(key)
                
            async def set(self, key, value):
                self.data[key] = value
                
            async def delete(self, key):
                if key in self.data:
                    del self.data[key]
        
        mock_redis = MockAsyncRedis()
        
        # Mock get_async_redis_client to return our mock
        async def mock_get_async_redis_client():
            return mock_redis
            
        # Apply the monkeypatch
        monkeypatch.setattr('utils.async_redis_client.get_async_redis_client', mock_get_async_redis_client)
        
        # Create a thread
        response = fastapi_client.post(
            '/assistant/thread',
            headers={"auth0_sub": "test-user"}
        )
        assert response.status_code == 200
        data = response.json()
        thread_id = data.get('thread_id')
        
        # Check that the thread ID was stored in Redis
        assert mock_redis.data.get('thread:test-user') == thread_id
        
        # Delete the thread
        response = fastapi_client.delete(
            f'/assistant/thread/{thread_id}',
            headers={"auth0_sub": "test-user"}
        )
        assert response.status_code == 200
        
        # Check that the thread ID was removed from Redis
        assert 'thread:test-user' not in mock_redis.data
        
    def test_auto_thread_creation_flask(self, flask_client, monkeypatch):
        """
        Test that a thread is automatically created when querying without a thread ID.
        """
        # Mock Redis client
        class MockRedis:
            def __init__(self):
                self.data = {}
                
            def get(self, key):
                return self.data.get(key)
                
            def set(self, key, value):
                self.data[key] = value
        
        mock_redis = MockRedis()
        
        # Mock get_redis_client to return our mock
        def mock_get_redis_client():
            return mock_redis
            
        # Apply the monkeypatch
        monkeypatch.setattr('utils.redis_client.get_redis_client', mock_get_redis_client)
        
        # Mock the provider to return a predictable response
        class MockProvider:
            def chat_completion(self, messages, model, thread_id=None):
                # Simulate creating a new thread if none provided
                if not thread_id:
                    thread_id = "new-thread-123"
                return {
                    "thread_id": thread_id,
                    "run_id": "run-123",
                    "choices": [{"message": {"content": "Hello!"}}]
                }
                
            def create_thread(self):
                return {"id": "new-thread-123", "created": True}
        
        # Mock the service to use our mock provider
        monkeypatch.setattr('services.assistant_service.AssistantService.provider', MockProvider())
        monkeypatch.setattr('services.assistant_service.AssistantService.client', None)
        
        # Query without a thread ID
        response = flask_client.post(
            '/assistant',
            json={"query": "Hello, world!"},
            headers={"auth0_sub": "test-user-2"}
        )
        assert response.status_code == 200
        data = json.loads(response.get_data(as_text=True))
        
        # Check that a thread ID was returned
        assert data.get('thread_id') is not None
        
        # Check that the thread ID was stored in Redis
        assert mock_redis.data.get('thread:test-user-2') is not None
        
    def test_thread_retrieval_flask(self, flask_client, monkeypatch):
        """
        Test that an existing thread ID is retrieved from Redis.
        """
        # Mock Redis client with pre-populated data
        class MockRedis:
            def __init__(self):
                self.data = {'thread:test-user-3': 'existing-thread-456'}
                
            def get(self, key):
                return self.data.get(key)
                
            def set(self, key, value):
                self.data[key] = value
        
        mock_redis = MockRedis()
        
        # Mock get_redis_client to return our mock
        def mock_get_redis_client():
            return mock_redis
            
        # Apply the monkeypatch
        monkeypatch.setattr('utils.redis_client.get_redis_client', mock_get_redis_client)
        
        # Mock the provider to return a predictable response
        class MockProvider:
            def chat_completion(self, messages, model, thread_id=None):
                # Return the thread ID that was passed in
                return {
                    "thread_id": thread_id,
                    "run_id": "run-456",
                    "choices": [{"message": {"content": "Hello!"}}]
                }
        
        # Mock the service to use our mock provider
        monkeypatch.setattr('services.assistant_service.AssistantService.provider', MockProvider())
        
        # Query without a thread ID but with auth0_sub that has a stored thread
        response = flask_client.post(
            '/assistant',
            json={"query": "Hello, world!"},
            headers={"auth0_sub": "test-user-3"}
        )
        assert response.status_code == 200
        data = json.loads(response.get_data(as_text=True))
        
        # Check that the existing thread ID was used
        assert data.get('thread_id') == 'existing-thread-456'
