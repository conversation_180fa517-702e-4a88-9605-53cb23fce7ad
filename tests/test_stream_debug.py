"""
Simple test script to verify streaming functionality.
"""
import asyncio
import json
import sys
import os

# Add the parent directory to the path so we can import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.logging import logger, setup_logging
from services.async_assistant_service import AsyncAssistantService

# Set up logging
setup_logging()

async def test_streaming():
    """Test the streaming functionality."""
    logger.info("Starting streaming test")
    
    # Create the assistant service
    service = AsyncAssistantService()
    
    # Test query
    query = "Tell me about the weather today"
    
    # Get the streaming response generator
    logger.info(f"Sending query: {query}")
    response_generator = service.stream_query(
        user_query=query,
        thread_id=None,  # Create a new thread
        model="gpt-4-turbo-preview"
    )
    
    # Process the streaming response
    logger.info("Processing streaming response")
    async for chunk in response_generator:
        # Log the chunk
        logger.info(f"Received chunk: {json.dumps(chunk)}")
        
        # Print the content if available
        if "content" in chunk:
            print(f"Content: {chunk['content']}")
        
        # Print thread_id and run_id if available
        if "thread_id" in chunk:
            print(f"Thread ID: {chunk['thread_id']}")
        if "run_id" in chunk:
            print(f"Run ID: {chunk['run_id']}")
    
    logger.info("Streaming test completed")

if __name__ == "__main__":
    # Run the test
    asyncio.run(test_streaming())
