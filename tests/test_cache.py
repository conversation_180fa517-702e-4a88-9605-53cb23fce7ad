import pytest
import json
from unittest.mock import patch, MagicMock
import time
from utils.cache import (
    generate_cache_key,
    get_cached_response,
    cache_response,
    invalidate_cache,
    get_cache_stats
)
from utils.async_cache import (
    get_cached_response as async_get_cached_response,
    cache_response as async_cache_response,
    invalidate_cache as async_invalidate_cache,
    get_cache_stats as async_get_cache_stats
)

@pytest.fixture
def mock_redis():
    """Fixture to mock the Redis client."""
    with patch('utils.cache.redis_client') as mock:
        # Set up mock methods
        mock.get.return_value = None
        mock.setex.return_value = True
        mock.delete.return_value = 1
        mock.keys.return_value = []
        yield mock

@pytest.fixture
def mock_async_redis():
    """Fixture to mock the async Redis client."""
    with patch('utils.async_cache.async_redis') as mock:
        # Set up mock methods
        mock.get = MagicMock(return_value=None)
        mock.setex = MagicMock(return_value=True)
        mock.delete = MagicMock(return_value=1)
        mock.get_async_redis_client = MagicMock()
        mock.get_async_redis_client.return_value = MagicMock()
        mock.get_async_redis_client.return_value.keys = MagicMock(return_value=[])
        yield mock

@pytest.fixture
def sample_messages():
    """Fixture to provide sample messages."""
    return [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, how are you?"}
    ]

@pytest.fixture
def sample_response():
    """Fixture to provide a sample response."""
    return {
        "choices": [
            {
                "message": {
                    "role": "assistant",
                    "content": "I'm doing well, thank you for asking!"
                },
                "finish_reason": "stop",
                "index": 0
            }
        ],
        "created": int(time.time()),
        "model": "gpt-3.5-turbo",
        "object": "chat.completion",
        "provider": "openai"
    }

def test_generate_cache_key(sample_messages):
    """Test generating a cache key."""
    key1 = generate_cache_key("openai", "gpt-3.5-turbo", sample_messages)
    key2 = generate_cache_key("openai", "gpt-3.5-turbo", sample_messages)
    key3 = generate_cache_key("anthropic", "claude-3-haiku", sample_messages)
    
    # Same inputs should generate the same key
    assert key1 == key2
    
    # Different inputs should generate different keys
    assert key1 != key3
    
    # Key should start with the prefix
    assert key1.startswith("response_cache:")

def test_get_cached_response_miss(mock_redis, sample_messages):
    """Test getting a cached response when there is a cache miss."""
    response = get_cached_response("openai", "gpt-3.5-turbo", sample_messages)
    
    # Should return None for a cache miss
    assert response is None
    
    # Should have called redis.get with the correct key
    mock_redis.get.assert_called_once()
    assert mock_redis.get.call_args[0][0].startswith("response_cache:")

def test_get_cached_response_hit(mock_redis, sample_messages, sample_response):
    """Test getting a cached response when there is a cache hit."""
    # Set up mock to return a cached response
    mock_redis.get.return_value = json.dumps(sample_response)
    
    response = get_cached_response("openai", "gpt-3.5-turbo", sample_messages)
    
    # Should return the cached response
    assert response == sample_response
    
    # Should have called redis.get with the correct key
    mock_redis.get.assert_called_once()
    assert mock_redis.get.call_args[0][0].startswith("response_cache:")

def test_cache_response(mock_redis, sample_messages, sample_response):
    """Test caching a response."""
    cache_response("openai", "gpt-3.5-turbo", sample_messages, sample_response)
    
    # Should have called redis.setex with the correct key and value
    mock_redis.setex.assert_called_once()
    args = mock_redis.setex.call_args[0]
    assert args[0].startswith("response_cache:")
    assert args[1] > 0  # TTL should be positive
    assert json.loads(args[2]) == sample_response

def test_invalidate_cache_all(mock_redis):
    """Test invalidating all cached responses."""
    # Set up mock to return some keys
    mock_redis.keys.return_value = ["response_cache:key1", "response_cache:key2"]
    
    count = invalidate_cache()
    
    # Should have called redis.keys and redis.delete
    mock_redis.keys.assert_called_once_with("response_cache:*")
    mock_redis.delete.assert_called_once_with("response_cache:key1", "response_cache:key2")
    
    # Should return the number of keys deleted
    assert count == 1

def test_get_cache_stats(mock_redis, sample_response):
    """Test getting cache statistics."""
    # Set up mock to return some keys and values
    mock_redis.keys.return_value = ["response_cache:key1", "response_cache:key2"]
    mock_redis.get.side_effect = [
        json.dumps(sample_response),
        json.dumps({**sample_response, "provider": "anthropic", "model": "claude-3-haiku"})
    ]
    
    stats = get_cache_stats()
    
    # Should have called redis.keys and redis.get for each key
    mock_redis.keys.assert_called_once_with("response_cache:*")
    assert mock_redis.get.call_count == 2
    
    # Should return the correct statistics
    assert stats["total_keys"] == 2
    assert "openai" in stats["providers"]
    assert "anthropic" in stats["providers"]
    assert "gpt-3.5-turbo" in stats["models"]
    assert "claude-3-haiku" in stats["models"]
    assert "openai/gpt-3.5-turbo" in stats["provider_models"]
    assert "anthropic/claude-3-haiku" in stats["provider_models"]

@pytest.mark.asyncio
async def test_async_get_cached_response_miss(mock_async_redis, sample_messages):
    """Test getting a cached response asynchronously when there is a cache miss."""
    response = await async_get_cached_response("openai", "gpt-3.5-turbo", sample_messages)
    
    # Should return None for a cache miss
    assert response is None
    
    # Should have called redis.get with the correct key
    mock_async_redis.get.assert_called_once()
    assert mock_async_redis.get.call_args[0][0].startswith("response_cache:")

@pytest.mark.asyncio
async def test_async_get_cached_response_hit(mock_async_redis, sample_messages, sample_response):
    """Test getting a cached response asynchronously when there is a cache hit."""
    # Set up mock to return a cached response
    mock_async_redis.get.return_value = json.dumps(sample_response)
    
    response = await async_get_cached_response("openai", "gpt-3.5-turbo", sample_messages)
    
    # Should return the cached response
    assert response == sample_response
    
    # Should have called redis.get with the correct key
    mock_async_redis.get.assert_called_once()
    assert mock_async_redis.get.call_args[0][0].startswith("response_cache:")

@pytest.mark.asyncio
async def test_async_cache_response(mock_async_redis, sample_messages, sample_response):
    """Test caching a response asynchronously."""
    await async_cache_response("openai", "gpt-3.5-turbo", sample_messages, sample_response)
    
    # Should have called redis.setex with the correct key and value
    mock_async_redis.setex.assert_called_once()
    args = mock_async_redis.setex.call_args[0]
    assert args[0].startswith("response_cache:")
    assert args[1] > 0  # TTL should be positive
    assert json.loads(args[2]) == sample_response

@pytest.mark.asyncio
async def test_async_invalidate_cache_all(mock_async_redis):
    """Test invalidating all cached responses asynchronously."""
    # Set up mock to return some keys
    mock_async_redis.get_async_redis_client.return_value.keys.return_value = ["response_cache:key1", "response_cache:key2"]
    mock_async_redis.get_async_redis_client.return_value.delete.return_value = 2
    
    count = await async_invalidate_cache()
    
    # Should have called redis.keys and redis.delete
    mock_async_redis.get_async_redis_client.return_value.keys.assert_called_once_with("response_cache:*")
    mock_async_redis.get_async_redis_client.return_value.delete.assert_called_once_with("response_cache:key1", "response_cache:key2")
    
    # Should return the number of keys deleted
    assert count == 2

@pytest.mark.asyncio
async def test_async_get_cache_stats(mock_async_redis, sample_response):
    """Test getting cache statistics asynchronously."""
    # Set up mock to return some keys and values
    mock_async_redis.get_async_redis_client.return_value.keys.return_value = ["response_cache:key1", "response_cache:key2"]
    mock_async_redis.get.side_effect = [
        json.dumps(sample_response),
        json.dumps({**sample_response, "provider": "anthropic", "model": "claude-3-haiku"})
    ]
    
    stats = await async_get_cache_stats()
    
    # Should have called redis.keys and redis.get for each key
    mock_async_redis.get_async_redis_client.return_value.keys.assert_called_once_with("response_cache:*")
    assert mock_async_redis.get.call_count == 2
    
    # Should return the correct statistics
    assert stats["total_keys"] == 2
    assert "openai" in stats["providers"]
    assert "anthropic" in stats["providers"]
    assert "gpt-3.5-turbo" in stats["models"]
    assert "claude-3-haiku" in stats["models"]
    assert "openai/gpt-3.5-turbo" in stats["provider_models"]
    assert "anthropic/claude-3-haiku" in stats["provider_models"]
