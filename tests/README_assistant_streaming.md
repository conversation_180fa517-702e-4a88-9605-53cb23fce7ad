# Assistant API Streaming Tests

This directory contains various tests and tools for testing the streaming functionality of the OpenAI Assistant API implementation.

## Changes Made to Fix Streaming

1. **Response Format**: Updated the response format in `async_openai_assistant_provider.py` to use a simpler, more consistent format:
   ```json
   {
     "content": "text chunk",
     "thread_id": "thread-123",
     "run_id": "run-456",
     "timestamp": **********.123
   }
   ```

2. **CORS Headers**: Enhanced CORS headers in both `async_app.py` and `async_assistant_routes.py` to ensure proper cross-origin requests.

3. **SSE Headers**: Added proper Server-Sent Events (SSE) headers to the streaming responses.

4. **Debug Logging**: Added extensive debug logging to help diagnose streaming issues.

## Test Files

### 1. `test_stream_debug.py`

A Python script that directly tests the streaming functionality by calling the `AsyncAssistantService` directly, bypassing the HTTP layer.

**Usage:**
```bash
cd go-gpt-backend
python tests/test_stream_debug.py
```

### 2. `test_http_stream.py`

A Python script that tests the HTTP streaming endpoint using `aiohttp`.

**Usage:**
```bash
cd go-gpt-backend
# Make sure aiohttp is installed
pip install aiohttp
# Run the test
python tests/test_http_stream.py
```

### 3. `test_assistant_stream_simple.html`

A simple HTML page that uses the EventSource API to test streaming.

**Usage:**
1. Start the server: `./run_async.sh`
2. Open the HTML file in a browser
3. Enter a query and click "Send Request"

### 4. `test_fetch_stream.html`

An alternative HTML page that uses the Fetch API instead of EventSource to test streaming.

**Usage:**
1. Start the server: `./run_async.sh`
2. Open the HTML file in a browser
3. Enter a query and click "Send Request"

## Troubleshooting

If streaming is not working, check the following:

1. **Server Logs**: Look for errors or warnings in the server logs.

2. **Network Tab**: Use the browser's developer tools to check the network tab for the streaming request. Make sure it's returning a 200 status code and the response headers include:
   - `Content-Type: text/event-stream`
   - `Cache-Control: no-cache, no-transform`
   - `Connection: keep-alive`

3. **CORS Issues**: If you see CORS errors in the console, make sure the server is properly configured to allow cross-origin requests.

4. **Event Handling**: Make sure the client is properly handling the SSE events. The events should be in the format:
   ```
   data: {"content": "text chunk", "thread_id": "thread-123", "run_id": "run-456"}
   ```

5. **Debug Mode**: Try using the `test_fetch_stream.html` file which includes a debug log to see exactly what's being received from the server.

## Testing with curl

You can also test the streaming endpoint with curl:

```bash
curl -N "http://localhost:8080/assistant/stream?query=Tell%20me%20about%20the%20weather%20today"
```

The `-N` flag disables buffering, which is important for streaming responses.
