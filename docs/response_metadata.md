# Response Metadata for Assistant API

This document describes the implementation of structured response metadata for the Assistant API, which provides a cleaner way to include follow-up questions in responses.

## Overview

Previously, the Assistant API was configured to include reference URLs and follow-up questions directly in the response text using a specific format:

```
<url>1.https://example.com,2.https://example2.com<end>
<recommendation>1.What is X?,2.How does Y work?,3.Tell me about Z<end>
```

This approach had several drawbacks:
- It was difficult for client applications to parse this format reliably
- The format was not easily extensible
- It cluttered the main response content
- It required special instructions to the model

The new implementation uses OpenAI's function calling capability to provide this metadata in a structured format that is:
- Easier to parse on the client side
- Cleanly separated from the main content
- More extensible for future metadata types
- More reliable as it uses a defined schema

## Implementation Details

### 1. Function Definition

We've defined a function schema in `utils/response_metadata.py`:

```python
provide_response_metadata = {
    "name": "provide_response_metadata",
    "description": "Provide metadata about the response including reference URLs and follow-up questions",
    "parameters": {
        "type": "object",
        "properties": {
            "reference_urls": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "url": {
                            "type": "string",
                            "description": "The URL of the reference"
                        },
                        "title": {
                            "type": "string",
                            "description": "The title of the referenced page"
                        },
                        "snippet": {
                            "type": "string",
                            "description": "A brief snippet or description of the content"
                        }
                    },
                    "required": ["url"]
                },
                "description": "URLs referenced to answer the question (up to 3)"
            },
            "follow_up_questions": {
                "type": "array",
                "items": {"type": "string"},
                "description": "Related questions for further exploration (up to 3)"
            }
        },
        "required": ["follow_up_questions"]
    }
}
```

### 2. Assistant Configuration

The function is registered with the Assistant during initialization in `utils/assistant_utils.py`. This allows the Assistant to call this function when it wants to provide metadata.

### 3. Function Call Handling

When the Assistant calls the `provide_response_metadata` function, the `AsyncOpenAIAssistantProvider` class handles it in the `on_tool_call_created` method:

```python
if tool_call.function.name == "provide_response_metadata":
    # Parse the function arguments
    metadata = json.loads(tool_call.function.arguments)
    
    # Store the metadata
    self.reference_urls = metadata.get("reference_urls", [])
    self.follow_up_questions = metadata.get("follow_up_questions", [])
    
    # Add metadata to the queue in a special format for the client
    metadata_message = {
        "type": "metadata",
        "reference_urls": self.reference_urls,
        "follow_up_questions": self.follow_up_questions,
        "thread_id": thread_id,
        "run_id": self.current_run_id,
        "timestamp": time.time()
    }
    await self.delta_queue.put(metadata_message)
```

This creates a special message with type "metadata" that is sent to the client.

### 4. Client-Side Handling

The client-side code (e.g., in `test_assistant_stream.html`) handles these metadata messages by:

1. Detecting messages with `type: "metadata"`
2. Extracting the follow-up questions
3. Displaying them in a user-friendly way
4. Optionally making the follow-up questions clickable to easily ask them

## Usage

The Assistant will automatically call the `provide_response_metadata` function when appropriate, typically at the end of its response. No special prompting is required.

The client will receive a message with the following structure:

```json
{
  "type": "metadata",
  "reference_urls": [
    {
      "url": "https://example.com/article1",
      "title": "Example Article 1",
      "snippet": "This article discusses the key points about the topic."
    },
    {
      "url": "https://example.com/article2",
      "title": "Example Article 2",
      "snippet": "Additional information about related aspects."
    }
  ],
  "follow_up_questions": [
    "What are the benefits of X?",
    "How does Y compare to Z?",
    "When was X first introduced?"
  ],
  "thread_id": "thread_abc123",
  "run_id": "run_def456",
  "timestamp": 1628097600
}
```

## Benefits

This approach offers several advantages:

1. **Clean Separation**: Metadata is separate from the main content
2. **Structured Format**: JSON format is easier to parse than text patterns
3. **Reliability**: Using function calling is more reliable than text pattern extraction
4. **Extensibility**: New metadata types can be added easily
5. **Improved UX**: Clients can display metadata in a more user-friendly way
6. **No Special Instructions**: The model doesn't need special instructions in the prompt
7. **Simplified Implementation**: Focusing only on follow-up questions simplifies the implementation
8. **Better User Experience**: Follow-up questions are displayed in a user-friendly way

## Recent Updates

We've made the following updates to the response metadata system:

1. **Restored Reference URLs**: Added back the reference_urls field to the function schema to provide both reference URLs and follow-up questions.

2. **Enhanced Schema**: Improved the reference_urls schema to include structured information (url, title, snippet) for each reference.

3. **Updated Instructions**: Enhanced the assistant instructions to provide clear guidance on how to format both reference URLs and follow-up questions correctly.

4. **Improved Client Experience**: The structured reference URL format provides a better user experience by including titles and snippets along with the URLs.

These updates ensure that the model provides comprehensive metadata that includes both reference sources and follow-up questions, making the responses more informative and useful for users.

## Example Function Call

Here's an example of how the model should call the function:

```javascript
provide_response_metadata({
  "reference_urls": [
    {
      "url": "https://example.com/article1",
      "title": "Example Article 1",
      "snippet": "This article discusses the key points about the topic."
    },
    {
      "url": "https://example.com/article2",
      "title": "Example Article 2",
      "snippet": "Additional information about related aspects."
    }
  ],
  "follow_up_questions": [
    "What are the best practices for implementing this solution?",
    "How does this compare to alternative approaches?",
    "What are the performance implications of this method?"
  ]
})
```

## Future Enhancements

Possible future enhancements include:

1. Adding more metadata types (e.g., sources, confidence scores)
2. Allowing metadata to be provided at any point during the response, not just at the end
3. Supporting more complex metadata structures
4. Adding client-side controls to toggle metadata visibility
5. Implementing a feedback mechanism for the quality of follow-up questions
