# OpenAI Assistant API Integration

This document describes the integration of the OpenAI Assistant API into the Go GPT Backend.

## Overview

The OpenAI Assistant API provides a more powerful and flexible way to interact with OpenAI's models. It supports:

- Persistent threads for maintaining conversation context
- Tool use for function calling
- File uploads and retrieval
- Code interpretation

Our implementation provides both synchronous (Flask) and asynchronous (FastAPI) endpoints for interacting with the Assistant API.

## Configuration

The following environment variables can be used to configure the Assistant API integration:

```
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_URL=https://api.openai.com/v1
OPENAI_ASSISTANT_ID=optional_existing_assistant_id
OPENAI_ASSISTANT_NAME=Go GPT Assistant
OPENAI_ASSISTANT_MODEL=gpt-4-turbo-preview
OPENAI_ASSISTANT_INSTRUCTIONS=You are a helpful assistant that provides accurate and concise information.
OPENAI_ASSISTANT_TOOLS=[]
OPENAI_ASSISTANT_TOOL_RESOURCES=[]
```

If `OPENAI_ASSISTANT_ID` is provided, the system will use the existing assistant. Otherwise, it will create a new assistant with the specified configuration.

## API Endpoints

### Synchronous API (Flask)

#### POST /assistant

Send a query to the assistant.

**Request:**
```json
{
  "query": "What is the capital of France?",
  "thread_id": "optional_thread_id",
  "model": "optional_model_override"
}
```

**Response:**
```json
{
  "response": "The capital of France is Paris.",
  "thread_id": "thread_123456",
  "run_id": "run_123456",
  "model": "gpt-4-turbo-preview"
}
```

#### POST /assistant/thread

Create a new conversation thread.

**Response:**
```json
{
  "thread_id": "thread_123456",
  "created": true
}
```

#### DELETE /assistant/thread/{thread_id}

Delete a conversation thread.

**Response:**
```json
{
  "deleted": true,
  "thread_id": "thread_123456"
}
```

#### GET /assistant/thread/{thread_id}/messages

Get all messages in a thread.

**Response:**
```json
{
  "messages": [
    {
      "id": "msg_123456",
      "role": "user",
      "content": "What is the capital of France?",
      "created_at": 1679012345
    },
    {
      "id": "msg_123457",
      "role": "assistant",
      "content": "The capital of France is Paris.",
      "created_at": 1679012346
    }
  ],
  "thread_id": "thread_123456"
}
```

#### POST /assistant/thread/{thread_id}/run/{run_id}/cancel

Cancel an in-progress run.

**Request:**
- Path parameters:
  - `thread_id`: The thread ID
  - `run_id`: The run ID to cancel

**Response:**
```json
{
  "cancelled": true,
  "thread_id": "thread_123456",
  "run_id": "run_123456",
  "status": "cancelling"
}
```

#### POST /assistant/cancel

Cancel the most recent active run for the authenticated user.

This endpoint automatically retrieves the user's thread_id from Redis and finds the most recent active run to cancel, without requiring explicit thread_id and run_id parameters. It handles runs in various states, including "queued", "in_progress", and "requires_action".

**Response:**
```json
{
  "cancelled": true,
  "thread_id": "thread_123456",
  "run_id": "run_123456",
  "status": "cancelling",
  "original_status": "in_progress",
  "cancellation_method": "cancel_endpoint"
}
```

The response includes:
- `original_status`: The state of the run before cancellation
- `cancellation_method`: How the run was cancelled ("cancel_endpoint" or "error_submission")

**Special Handling for "requires_action" State:**

For runs in the "requires_action" state (waiting for function call results), the system uses a different approach:

1. Instead of calling the cancel endpoint directly (which would fail with "Cannot cancel run with status 'cancelled'")
2. The system attempts to submit error outputs for all required tool calls
3. This effectively cancels the run by failing the function calls

If the submission of error outputs fails (which can happen if the run state changes to "queued" or "in_progress" during the operation), the system automatically falls back to using the cancel API.

The response for a run in "requires_action" state that was successfully cancelled via error submission would look like:

```json
{
  "cancelled": true,
  "thread_id": "thread_123456",
  "run_id": "run_123456",
  "status": "failed",
  "original_status": "requires_action",
  "cancellation_method": "error_submission"
}
```

If the system had to fall back to the cancel API, the response would look like:

```json
{
  "cancelled": true,
  "thread_id": "thread_123456",
  "run_id": "run_123456",
  "status": "cancelling",
  "original_status": "requires_action",
  "cancellation_method": "cancel_endpoint"
}
```

This robust approach ensures that runs are cancelled successfully regardless of state transitions that might occur during the cancellation process.

### Asynchronous API (FastAPI)

The asynchronous API provides the same endpoints as the synchronous API, but with asynchronous processing. This includes the ability to cancel runs using the `/assistant/thread/{thread_id}/run/{run_id}/cancel` endpoint and the simplified `/assistant/cancel` endpoint.

## Implementation Details

### Assistant Initialization

When the server starts, it checks if an assistant ID is provided in the environment. If so, it verifies the assistant exists and updates it if necessary. Otherwise, it creates a new assistant with the specified configuration.

### Thread Management

Threads are created on demand and can be reused for continuing conversations. The thread ID is returned in the response and can be provided in subsequent requests to continue the conversation.

### Streaming Responses

The implementation supports streaming responses for a more responsive user experience:

1. **Incremental Text Updates**: The system streams each small piece of text as it's generated, rather than waiting for complete sentences or paragraphs.

2. **Delta Handling**: The streaming implementation uses the OpenAI `on_text_delta` event handler to provide real-time updates as the assistant generates text.

3. **Responsive UI**: This creates a typing-like experience similar to ChatGPT's interface, where text appears incrementally rather than in large chunks.

4. **Delta Flagging**: Each delta update includes an `is_delta` flag to help the frontend distinguish between complete messages and incremental updates.

### Function Calling

The implementation supports function calling through the OpenAI Assistant API:

1. **Event-Driven Architecture**: Uses the OpenAI streaming API with event handlers to detect when the assistant needs to call functions.

2. **Automatic Tool Execution**: When the assistant requires action, the system automatically executes the requested functions and submits the results back to continue the conversation.

3. **Seamless Integration**: Function calling works in both streaming and non-streaming modes, providing a consistent experience regardless of the client's configuration.

### Error Handling

Errors are logged and returned in the response with appropriate HTTP status codes. The implementation includes robust error handling for various scenarios, including function execution failures.

## Usage Examples

### Starting a New Conversation

```bash
curl -X POST http://localhost:5002/assistant \
  -H "Content-Type: application/json" \
  -d '{"query": "What is the capital of France?"}'
```

### Continuing a Conversation

```bash
curl -X POST http://localhost:5002/assistant \
  -H "Content-Type: application/json" \
  -d '{"query": "What is its population?", "thread_id": "thread_123456"}'
```

### Getting Thread Messages

```bash
curl -X GET http://localhost:5002/assistant/thread/thread_123456/messages
```

### Deleting a Thread

```bash
curl -X DELETE http://localhost:5002/assistant/thread/thread_123456
```

### Cancelling a Run

```bash
curl -X POST http://localhost:5002/assistant/thread/thread_123456/run/run_123456/cancel
```

### Cancelling the Active Run

```bash
curl -X POST http://localhost:5002/assistant/cancel
```

## Flutter Mobile Integration

Flutter applications can integrate with the Assistant API in both streaming and non-streaming modes.

### Non-Streaming Mode

For non-streaming mode, use the standard HTTP client to make POST requests:

```dart
import 'dart:convert';
import 'package:http/http.dart' as http;

Future<String> queryAssistant(String query, {String? threadId}) async {
  final url = Uri.parse('https://your-api-endpoint.com/assistant');
  
  final payload = {
    'query': query,
    if (threadId != null) 'thread_id': threadId,
    'model': 'gpt-4-turbo-preview'
  };
  
  final response = await http.post(
    url,
    headers: {'Content-Type': 'application/json'},
    body: jsonEncode(payload),
  );
  
  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    return data['response'];
  } else {
    throw Exception('Failed to get response: ${response.statusCode}');
  }
}
```

### Streaming Mode

For streaming mode, you'll need to handle the Server-Sent Events (SSE) format. Flutter doesn't have built-in SSE support, so you'll need to implement a custom solution:

```dart
import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;

Stream<String> streamAssistant(String query, {String? threadId}) async* {
  final url = Uri.parse('https://your-api-endpoint.com/assistant/stream');
  
  final payload = {
    'query': query,
    if (threadId != null) 'thread_id': threadId,
    'model': 'gpt-4-turbo-preview',
    'stream': true
  };
  
  final request = http.Request('POST', url);
  request.headers['Content-Type'] = 'application/json';
  request.headers['Accept'] = 'text/event-stream';
  request.body = jsonEncode(payload);
  
  final response = await http.Client().send(request);
  
  if (response.statusCode != 200) {
    throw Exception('Failed to connect: ${response.statusCode}');
  }
  
  // Process the stream
  final stream = response.stream
    .transform(utf8.decoder)
    .transform(const LineSplitter())
    .where((line) => line.startsWith('data: '))
    .map((line) => line.substring(6)) // Remove 'data: ' prefix
    .map((jsonStr) => jsonDecode(jsonStr) as Map<String, dynamic>)
    .where((data) => data.containsKey('content') && data['content'] != '[DONE]')
    .map((data) => data['content'] as String);
  
  await for (final content in stream) {
    yield content;
  }
}

// Usage example:
void streamExample() {
  streamAssistant('Tell me about Flutter').listen(
    (content) {
      // Update UI with each chunk of content
      print(content);
    },
    onError: (error) {
      print('Error: $error');
    },
    onDone: () {
      print('Stream completed');
    }
  );
}
```

### UI Integration

To display streaming responses in your Flutter UI:

```dart
class ChatScreen extends StatefulWidget {
  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _controller = TextEditingController();
  final List<Message> _messages = [];
  String? _threadId;
  String _currentResponse = '';
  bool _isStreaming = false;
  
  void _sendMessage() async {
    final query = _controller.text;
    if (query.isEmpty) return;
    
    setState(() {
      _messages.add(Message(content: query, isUser: true));
      _controller.clear();
      _isStreaming = true;
      _currentResponse = '';
    });
    
    try {
      streamAssistant(query, threadId: _threadId).listen(
        (content) {
          setState(() {
            _currentResponse += content;
          });
        },
        onError: (error) {
          print('Error: $error');
          setState(() {
            _isStreaming = false;
          });
        },
        onDone: () {
          setState(() {
            _messages.add(Message(content: _currentResponse, isUser: false));
            _isStreaming = false;
            _currentResponse = '';
          });
        }
      );
    } catch (e) {
      print('Failed to send message: $e');
      setState(() {
        _isStreaming = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Assistant Chat')),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              itemCount: _messages.length + (_isStreaming ? 1 : 0),
              itemBuilder: (context, index) {
                if (index < _messages.length) {
                  return MessageBubble(message: _messages[index]);
                } else {
                  // Show streaming response
                  return MessageBubble(
                    message: Message(content: _currentResponse, isUser: false),
                    isTyping: true,
                  );
                }
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controller,
                    decoration: InputDecoration(
                      hintText: 'Type a message...',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.send),
                  onPressed: _isStreaming ? null : _sendMessage,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
```

This implementation provides a smooth typing effect similar to ChatGPT, enhancing the user experience in mobile applications.
