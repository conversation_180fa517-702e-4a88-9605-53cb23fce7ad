# Caching Layer in Go GPT Backend

This document describes the caching layer implementation in the Go GPT Backend.

## Overview

The Go GPT Backend includes a robust caching layer that caches responses from LLM providers to improve performance and reduce API costs. The caching layer is implemented using a tiered approach with memory, Redis, and optional disk storage, supporting both synchronous and asynchronous operations.

## Components

### Cache Utilities

The caching layer consists of two main utility modules:

- `utils/cache.py`: Provides synchronous caching functions for use with the Flask app
- `utils/async_cache.py`: Provides asynchronous caching functions for use with the FastAPI app

Both modules provide the same functionality, but with different interfaces (synchronous vs. asynchronous).

### Tiered Caching Architecture

The caching system uses a tiered approach for optimal performance and reliability:

1. **Memory Cache (L1)**: Fastest access, limited size, short TTL
   - Implemented using `TTLCache` from the `cachetools` library
   - Provides sub-millisecond access times
   - Automatically evicts entries based on TTL and LRU policy

2. **Redis Cache (L2)**: Balanced performance and capacity
   - Persistent across application restarts
   - Shared across multiple instances
   - Supports data compression

3. **Disk Cache (L3, Optional)**: Highest capacity, slowest access
   - Implemented using `diskcache` library
   - Fully persistent storage
   - Used as a fallback when Redis misses

### Key Functions

#### Synchronous Functions

- `generate_cache_key(provider, model, messages, **kwargs)`: Generate a cache key for a model request
- `get_cached_response(provider, model, messages, **kwargs)`: Get a cached response for a model request
- `cache_response(provider, model, messages, response, **kwargs)`: Cache a response from a model
- `invalidate_cache(provider=None, model=None)`: Invalidate cached responses
- `get_cache_stats()`: Get statistics about the cache
- `warm_cache()`: Preload frequently accessed items into memory cache
- `enforce_cache_size_limits()`: Remove least recently used items when cache size exceeds limits
- `periodic_cache_maintenance()`: Perform regular cache maintenance tasks

#### Asynchronous Functions

- `async_get_cached_response(provider, model, messages, **kwargs)`: Get a cached response for a model request asynchronously
- `async_cache_response(provider, model, messages, response, **kwargs)`: Cache a response from a model asynchronously
- `async_invalidate_cache(provider=None, model=None)`: Invalidate cached responses asynchronously
- `async_get_cache_stats()`: Get statistics about the cache asynchronously
- `async_warm_cache()`: Preload frequently accessed items into memory cache asynchronously
- `async_enforce_cache_size_limits()`: Remove least recently used items when cache size exceeds limits asynchronously
- `async_periodic_cache_maintenance()`: Perform regular cache maintenance tasks asynchronously

### Integration with Model Providers

The caching layer is integrated with the model providers through the base provider classes:

- `providers/base_provider.py`: Synchronous base provider class
- `providers/async_base_provider.py`: Asynchronous base provider class

Both classes implement caching in their `chat_completion` methods, which check the cache before making an API call and cache the response after receiving it.

## Advanced Features

### Cache Compression

Responses are automatically compressed to reduce memory usage:

- Uses LZ4 compression for speed (primary)
- Falls back to zlib compression for better compression ratio when needed
- Only compresses data if it actually saves space
- Automatically decompresses data when retrieving from cache

### Similar Query Detection

The cache can detect and use responses for similar but not identical queries:

- Uses Levenshtein distance to measure similarity between cache keys
- Configurable similarity threshold (default: 90%)
- Helps with minor variations in prompts that would produce the same response
- Improves cache hit rate without sacrificing response quality

### LRU Cache Eviction

The cache implements a Least Recently Used (LRU) eviction policy:

- Tracks access frequency for each cache key
- Automatically removes least accessed items when cache size exceeds limits
- Configurable maximum cache size
- Ensures most valuable items stay in cache

### Cache Warming

The cache can be preloaded with frequently accessed items:

- Automatically tracks which items are accessed most frequently
- Periodically loads these items into memory cache
- Reduces latency for common queries
- Can be triggered manually or automatically

## Configuration

The caching layer can be configured through the following settings:

- `ENABLE_CACHE`: Whether to enable caching (default: `True`)
- `CACHE_TTL`: Time-to-live for Redis cached responses in seconds (default: `3600` = 1 hour)
- `CACHE_COMPRESSION`: Whether to compress cached data (default: `True`)
- `CACHE_MEMORY_ENABLED`: Whether to use memory cache (default: `True`)
- `CACHE_MEMORY_MAX_ITEMS`: Maximum number of items in memory cache (default: `1000`)
- `CACHE_MEMORY_TTL`: Time-to-live for memory cached responses in seconds (default: `300` = 5 minutes)
- `CACHE_PERSISTENT_ENABLED`: Whether to use disk cache (default: `False`)
- `CACHE_PERSISTENT_PATH`: Path to store disk cache (default: `./cache`)
- `CACHE_LRU_POLICY`: Whether to use LRU eviction policy (default: `True`)
- `CACHE_MAX_SIZE_MB`: Maximum cache size in MB (default: `1024` = 1GB)
- `CACHE_SIMILARITY_THRESHOLD`: Threshold for similar query detection (default: `0.9` = 90%)

These settings can be configured in the `.env` file or through environment variables.

## Cache Key Generation

Cache keys are generated based on the following parameters:

- Provider name (e.g., `openai`, `anthropic`, `deepseek`)
- Model name (e.g., `gpt-3.5-turbo`, `claude-3-haiku`)
- Messages (the conversation history)
- Additional parameters that affect the response (e.g., `temperature`, `max_tokens`)

The cache key is a hash of these parameters, which ensures that the same request will always generate the same cache key.

## Cache Invalidation

The cache can be invalidated in several ways:

- Invalidate all cached responses: `invalidate_cache()`
- Invalidate all cached responses for a specific provider: `invalidate_cache(provider='openai')`
- Invalidate all cached responses for a specific provider and model: `invalidate_cache(provider='openai', model='gpt-3.5-turbo')`

Invalidation affects all cache tiers (memory, Redis, and disk if enabled).

## Cache Statistics

The cache statistics provide detailed information about the cache usage, including:

- Total number of cached responses
- Number of cached responses per provider
- Number of cached responses per model
- Number of cached responses per provider/model combination
- Memory cache size and hit rate
- Redis cache hit rate
- Disk cache hit rate (if enabled)
- Similar query hit rate
- Compression savings
- Overall hit rate

## API Endpoints

The caching layer provides the following API endpoints:

- `GET /cache/stats`: Get cache statistics
- `POST /cache/invalidate`: Invalidate cached responses
- `POST /cache/warm`: Warm the cache by preloading frequently accessed items
- `POST /cache/enforce-limits`: Enforce cache size limits
- `POST /cache/maintenance`: Perform periodic cache maintenance tasks
- `GET /cache/config`: Get cache configuration

## Benefits of Enhanced Caching

- **Tiered Performance**: Fastest access for most frequently used items
- **Reduced Memory Usage**: Compression reduces Redis memory footprint
- **Higher Hit Rate**: Similar query detection improves cache utilization
- **Intelligent Eviction**: LRU policy keeps most valuable items in cache
- **Optimized Access Patterns**: Cache warming reduces latency for common queries
- **Detailed Analytics**: Comprehensive statistics for monitoring and optimization

## Limitations

- **Streaming Responses**: Streaming responses are not cached
- **Dynamic Content**: Responses that include dynamic content (e.g., current time, weather) may be stale when retrieved from the cache
- **Memory Usage**: Memory cache has limited capacity and should be sized appropriately

## Testing

The caching layer includes comprehensive tests in `tests/test_cache.py`. These tests cover both synchronous and asynchronous caching functions.

## Future Improvements

- **Cache Sharding**: Shard the cache across multiple Redis instances for better scalability
- **Predictive Caching**: Preload cache based on predicted user queries
- **Content-Aware TTL**: Adjust TTL based on content type and volatility
- **Distributed Cache Coordination**: Coordinate cache operations across multiple instances
