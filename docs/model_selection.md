# Intelligent Model Selection

This document describes the intelligent model selection system that automatically chooses the optimal model for each query based on multiple factors.

## Overview

The model selection system uses a sophisticated algorithm to choose the best provider and model for each query. The selection is based on:

1. **Query content analysis**: Analyzing the query text for patterns indicating specific needs
2. **Performance metrics**: Considering latency and reliability of each model
3. **Resource optimization**: Balancing cost efficiency and load distribution
4. **User preferences**: Respecting user-specified model choices when provided

## Core Components

### Model Capabilities Database

The system maintains a database of model capabilities in `config/constants.py`:

```python
MODEL_CAPABILITIES = {
    'deepseek/deepseek-chat': {'coding': 0.8, 'reasoning': 0.7, 'creativity': 0.6, 'knowledge': 0.7},
    'openai/gpt-3.5-turbo': {'coding': 0.7, 'reasoning': 0.6, 'creativity': 0.7, 'knowledge': 0.7},
    'openai/gpt-4': {'coding': 0.9, 'reasoning': 0.9, 'creativity': 0.8, 'knowledge': 0.9},
    'anthropic/claude-3-haiku': {'coding': 0.6, 'reasoning': 0.7, 'creativity': 0.8, 'knowledge': 0.7},
    'anthropic/claude-3-sonnet': {'coding': 0.8, 'reasoning': 0.9, 'creativity': 0.9, 'knowledge': 0.9},
}
```

Each model is rated on four key capabilities:
- **Coding**: Ability to understand and generate code
- **Reasoning**: Ability to solve problems and provide logical explanations
- **Creativity**: Ability to generate creative content
- **Knowledge**: Breadth and depth of factual knowledge

### Performance Metrics Tracking

The system continuously tracks performance metrics for each model:

```python
MODEL_METRICS = {
    # Format: 'provider/model': {'latency': [], 'errors': 0, 'cost_per_token': 0.00X, 'last_used': timestamp}
    'deepseek/deepseek-chat': {'latency': [], 'errors': 0, 'cost_per_token': 0.0001, 'last_used': 0},
    'openai/gpt-3.5-turbo': {'latency': [], 'errors': 0, 'cost_per_token': 0.0002, 'last_used': 0},
    'openai/gpt-4-turbo': {'latency': [], 'errors': 0, 'cost_per_token': 0.001, 'last_used': 0},
    'anthropic/claude-3-haiku': {'latency': [], 'errors': 0, 'cost_per_token': 0.00025, 'last_used': 0},
    'anthropic/claude-3-sonnet': {'latency': [], 'errors': 0, 'cost_per_token': 0.0007, 'last_used': 0},
}
```

These metrics include:
- **Latency**: Rolling average of response times (last 100 requests)
- **Errors**: Count of errors encountered
- **Cost per token**: Estimated cost for processing each token
- **Last used**: Timestamp of when the model was last used (for load balancing)

### Selection Algorithm

The model selection algorithm is implemented in `utils/model_utils.py` and works as follows:

1. **User Preference Check**: If the user has specified a preferred provider/model and it's valid, use it.

2. **Query Analysis**: Analyze the query text for patterns indicating specific needs:
   - Code-related queries → Prioritize models with strong coding capabilities
   - Reasoning/logic queries → Prioritize models with strong reasoning capabilities
   - Creative tasks → Prioritize models with strong creative capabilities
   - Knowledge-intensive queries → Prioritize models with strong knowledge capabilities

3. **Score Calculation**: Calculate a weighted score for each model based on:
   - **Capability Score (50%)**: How well the model's capabilities match the query requirements
   - **Latency Score (20%)**: How fast the model responds
   - **Reliability Score (15%)**: How often the model encounters errors
   - **Cost Factor (10%)**: The cost per token for the model (especially important for cost-sensitive requests)
   - **Load Factor (5%)**: How recently the model was used (for load balancing)

4. **Model Selection**: Select the model with the highest overall score.

## Usage

### API Parameters

When making a query, you can influence the model selection by providing these parameters:

- `provider_model`: Specify a preferred provider/model (e.g., "openai/gpt-4")
- `cost_sensitive`: Set to `true` to prioritize lower-cost models

Example request:

```json
{
  "query": "Explain how quantum computing works",
  "session_id": "your-session-id",
  "provider_model": "anthropic/claude-3-sonnet",  // Optional: specify preferred model
  "cost_sensitive": false  // Optional: prioritize lower-cost models
}
```

### Viewing Model Information

You can view information about available models and their capabilities using the `/models` endpoint:

```
GET /models
```

This returns detailed information about each model, including:
- Capabilities (coding, reasoning, creativity, knowledge)
- Average latency
- Error rate
- Cost per token

## Implementation Details

### Query Pattern Recognition

The system uses regular expressions to identify patterns in queries:

```python
# Check for code-related content
if re.search(r'(code|function|programming|algorithm|bug|error|syntax|compile)', query, re.IGNORECASE):
    required_capabilities['coding'] = 1.5  # Weight coding higher for code-related queries

# Check for reasoning/logic tasks
if re.search(r'(explain|why|how|reason|logic|analyze|solve|problem)', query, re.IGNORECASE):
    required_capabilities['reasoning'] = 1.2

# Check for creative tasks
if re.search(r'(create|generate|write|story|poem|creative|imagine)', query, re.IGNORECASE):
    required_capabilities['creativity'] = 1.3

# Check for knowledge-intensive queries
if re.search(r'(what is|who is|when|where|history|science|facts|information)', query, re.IGNORECASE):
    required_capabilities['knowledge'] = 1.4
```

### Dynamic Performance Updates

The system updates performance metrics after each model use:

```python
def update_model_metrics(provider_model, latency=None, error=False):
    """
    Update performance metrics for a specific provider/model
    
    Args:
        provider_model: The provider/model identifier (e.g., 'openai/gpt-4')
        latency: Optional latency measurement to add
        error: Whether an error occurred with this model
    """
    with metrics_lock:
        if provider_model not in MODEL_METRICS:
            MODEL_METRICS[provider_model] = {'latency': [], 'errors': 0, 'cost_per_token': 0.0005, 'last_used': 0}
        
        metrics = MODEL_METRICS[provider_model]
        
        if latency is not None:
            metrics['latency'].append(latency)
            # Keep only the last 100 latency measurements
            if len(metrics['latency']) > 100:
                metrics['latency'] = metrics['latency'][-100:]
        
        if error:
            metrics['errors'] += 1
        
        metrics['last_used'] = time.time()
```

### Provider-Specific Concurrency Control

The system uses provider-specific locks to prevent overwhelming any single provider:

```python
# Use provider-specific locks instead of a single global lock
provider_locks = {
    'deepseek': Lock(),
    'openai': Lock(),
    'anthropic': Lock()
}

# Semaphore to limit total concurrent API calls
api_semaphore = BoundedSemaphore(settings.MAX_CONCURRENT_API_CALLS)
```

## Best Practices

1. **Let the system choose**: For most queries, let the system automatically select the best model based on the query content and performance metrics.

2. **Specify models for specialized tasks**: For tasks that require specific capabilities (e.g., complex coding tasks), consider specifying a model known to excel in that area.

3. **Use cost_sensitive for bulk processing**: When processing large volumes of queries, set `cost_sensitive` to `true` to prioritize lower-cost models.

4. **Monitor model performance**: Regularly check the `/models` endpoint to see how different models are performing in terms of latency and error rates.

5. **Update model capabilities**: As models evolve, update their capability ratings in `config/constants.py` to reflect their current strengths.

## Integration with Multi-Round Conversations

The model selection system works seamlessly with the multi-round conversation system:

- Each query in a conversation can potentially use a different model based on its content
- The system maintains context across rounds even when different models are used
- Model selection takes into account the full context of the conversation, not just the current query
