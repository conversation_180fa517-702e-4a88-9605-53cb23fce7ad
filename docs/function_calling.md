# Function Calling in Go GPT Backend

This document explains how function calling works in the Go GPT Backend, including the complete function calling loop with automatic function execution and response generation.

## Overview

Function calling allows the language model to request the execution of specific functions to retrieve information or perform actions. The backend supports both synchronous and asynchronous function calling with models like GPT-4o-turbo that have automatic function calling capabilities.

## Function Calling Flow

The complete function calling flow works as follows:

1. **Initial Request**: The client sends a query to the backend.
2. **Model API Call**: The backend sends the query to the model with function definitions.
3. **Function Call Decision**: The model decides to call a function and returns a function call.
4. **Function Execution**: The backend executes the function locally and gets the result.
5. **Function Result Storage**: The backend stores the function result in the conversation history.
6. **Second API Call**: The backend makes a second API call to the model with the updated conversation history including the function result.
7. **Final Response Generation**: The model processes the function result and generates a final response.
8. **Response Delivery**: The backend returns both the function result and the model's final response to the client.

## Implementation Details

### Function Registry

Functions are registered in the `function_registry` module, which maintains a registry of available functions and their schemas. The registry provides methods to:

- Register new functions
- Get function schemas for API calls
- Execute functions by name with provided arguments

### Function Calling in Providers

The provider implementations (both synchronous and asynchronous) support function calling through:

- Passing function definitions to the model API
- Detecting function calls in the model's response
- Executing functions when requested
- Making a second API call with function results

### Streaming vs. Non-Streaming

The backend supports both streaming and non-streaming function calls:

- **Streaming**: Function calls and results are streamed to the client in real-time, allowing for a more interactive experience.
- **Non-Streaming**: The entire process happens in a single request-response cycle, with the final response including both the function result and the model's response.

### Conversation History

Function calls and their results are stored in the conversation history, allowing the model to reference them in future interactions. The conversation manager adds function results as messages with the role "function".

## Usage

To enable function calling in your requests:

```python
# Enable function calling with specific functions
response = await client.query(
    query="What's the weather in San Francisco?",
    enable_functions=True,
    functions=["get_weather"]
)

# Enable automatic function calling with all available functions
response = await client.query(
    query="What's the weather in San Francisco?",
    enable_functions=True,
    auto_functions=True
)
```

## Error Handling

The function calling implementation includes robust error handling:

- Function execution errors are caught and reported
- Invalid function arguments are handled gracefully
- If the second API call fails, the system falls back to returning the function result directly

## Supported Models

Function calling is supported by models that have this capability, such as:

- GPT-4o-turbo
- GPT-4
- Claude 3 Opus
- Claude 3 Sonnet

## Limitations

- Function calling may increase latency due to the need for a second API call
- Complex nested function calls are not currently supported
- Function results must be serializable to JSON

## Future Improvements

- Support for nested function calls
- Parallel function execution
- Custom function result formatting
- Function call caching
