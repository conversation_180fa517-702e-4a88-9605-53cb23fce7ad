# Function-Based Web Search

This document explains how the function-based web search feature works in the Go GPT Backend, particularly focusing on its support for multiple rounds of conversation.

## Overview

The function-based web search feature allows models with function calling capabilities (like GPT-4-turbo) to dynamically decide when to search for information on the web. Instead of automatically performing web searches before every model API call, the system now relies on the model to determine when a search is necessary and what to search for.

This approach has several advantages:
- More efficient use of search APIs (only searching when needed)
- More intelligent search queries formulated by the model
- Better integration of search results into the conversation flow
- Support for follow-up searches and query refinement

## Key Components

### 1. Search Function

The primary search function is implemented in `utils/sample_functions.py` and provides the following capabilities:

```python
async def search(
    query: str, 
    num_results: int = 5, 
    engines: Optional[List[str]] = None,
    include_content: bool = False,
    session_id: Optional[str] = None,
    exclude_seen: bool = True
) -> Dict[str, Any]:
    # Implementation details...
```

**Parameters:**
- `query`: The search query
- `num_results`: Number of results to return per search engine (max 10)
- `engines`: List of search engines to use (default: ['google', 'duckduckgo'])
- `include_content`: Whether to include full content extraction from web pages
- `session_id`: Optional session ID for conversation context awareness
- `exclude_seen`: Whether to exclude URLs that have been shown in previous searches in this session

### 2. Query Refinement Function

The query refinement function allows the model to refine a previous search with additional criteria:

```python
async def refine_search(
    search_id: str,
    additional_terms: Optional[str] = None,
    exclude_terms: Optional[str] = None,
    num_results: int = 5,
    include_content: bool = False,
    session_id: Optional[str] = None
) -> Dict[str, Any]:
    # Implementation details...
```

**Parameters:**
- `search_id`: The ID of a previous search to refine
- `additional_terms`: Additional search terms to add to the original query
- `exclude_terms`: Terms to exclude from the search
- `num_results`: Number of results to return per search engine
- `include_content`: Whether to include full content extraction
- `session_id`: Session ID for conversation context

### 3. Search History Function

The search history function allows the model to retrieve previous searches for the current conversation:

```python
async def get_search_history(
    session_id: str,
    limit: int = 5
) -> Dict[str, Any]:
    # Implementation details...
```

**Parameters:**
- `session_id`: Session ID for the conversation
- `limit`: Maximum number of search history entries to return

## Multi-Round Conversation Support

The function-based web search feature is designed to work effectively across multiple rounds of conversation through several mechanisms:

### 1. Conversation Context Awareness

When the `session_id` parameter is provided to the search function, it uses the conversation history to enhance the search query:

- It retrieves the conversation history for the current round
- It gets the summary of previous rounds if available
- It extracts relevant context from the conversation using `get_relevant_context_from_rounds`
- It enhances the query with key phrases from the context

This ensures that searches are informed by the full conversation context, not just the current query.

### 2. URL Tracking and Deduplication

The system tracks which URLs have been shown to the user in previous searches:

- URLs from search results are stored in Redis with the session ID
- The `exclude_seen` parameter (default: true) filters out previously seen URLs
- This prevents showing the same search results across multiple conversation rounds

### 3. Search History Tracking

Each search is recorded in the conversation's search history:

- Search queries, results, and metadata are stored in Redis
- The `get_search_history` function allows retrieving previous searches
- The `refine_search` function allows building on previous searches

### 4. Query Refinement

The `refine_search` function enables the model to refine previous searches:

- It can add additional terms to the original query
- It can exclude specific terms from the search
- It builds on the original query rather than starting from scratch

## Usage Examples

### Basic Search

```json
{
  "function_call": {
    "name": "search",
    "arguments": {
      "query": "latest developments in quantum computing",
      "num_results": 3,
      "include_content": true,
      "session_id": "user_session_123"
    }
  }
}
```

### Refined Search

```json
{
  "function_call": {
    "name": "refine_search",
    "arguments": {
      "search_id": "previous_search_id",
      "additional_terms": "applications in cryptography",
      "exclude_terms": "theoretical",
      "session_id": "user_session_123"
    }
  }
}
```

### Getting Search History

```json
{
  "function_call": {
    "name": "get_search_history",
    "arguments": {
      "session_id": "user_session_123",
      "limit": 3
    }
  }
}
```

## Implementation Details

### Search Result Storage

Search results are stored in Redis with the following structure:

- **Search History**: `search_history:{session_id}` - List of search history entries
- **Seen URLs**: `seen_urls:{session_id}` - Set of URLs that have been shown to the user

Each search history entry contains:
- Search ID
- Original query
- Enhanced query (if different from original)
- Timestamp
- Number of results
- Result IDs and URLs

### Context Extraction

The `get_relevant_context_from_rounds` function extracts relevant context from the conversation:

1. It extracts keywords from the query
2. It looks for messages in the current round with similar keywords
3. It extracts sentences from previous rounds summary that contain query keywords
4. It combines this information to create a context string

### Query Enhancement

The search query is enhanced with context in the following way:

1. Extract key phrases from the context
2. Filter out phrases that are already in the query
3. Add up to 3 most relevant phrases to the query

## Configuration

The function-based web search feature uses the following configuration options:

- `USE_SERP_API`: Whether to use SerpAPI for Google search
- `SERP_API_KEY`: API key for SerpAPI
- `GOOGLE_SEARCH_API_KEY`: API key for Google Custom Search

These options are defined in `config/settings.py`.

## Best Practices

1. **Always provide the session_id**: This enables conversation context awareness and result tracking.
2. **Use refine_search for follow-up queries**: This builds on previous searches rather than starting from scratch.
3. **Consider include_content carefully**: Setting this to true provides more detailed information but uses more tokens.
4. **Use exclude_seen appropriately**: Set to true to avoid showing the same URLs multiple times, but set to false when refining searches.
