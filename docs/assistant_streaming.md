# Assistant API Streaming

This document explains how to use the streaming functionality with the OpenAI Assistant API.

## Overview

The Assistant API now supports streaming responses, which allows for a more interactive user experience. Instead of waiting for the entire response to be generated, the API will send chunks of the response as they are generated.

## How to Use

### Running the Server in Async Mode

To use the streaming functionality, you need to run the server in async mode:

```bash
# Use the provided script
./run_async.sh

# Or run directly with the mode flag
python run.py --mode async
```

This will start the FastAPI server with the streaming functionality enabled.

### API Endpoints

The following endpoints support streaming:

- `POST /assistant/stream` - Stream a response from the assistant
- `GET /assistant/stream` - Stream a response from the assistant (for EventSource)

#### POST Endpoint

Example POST request:

```json
{
  "query": "Tell me about the solar system",
  "thread_id": "optional-thread-id",
  "model": "gpt-4-turbo-preview"
}
```

#### GET Endpoint

Example GET request:

```
/assistant/stream?query=Tell%20me%20about%20the%20solar%20system&thread_id=optional-thread-id&model=gpt-4-turbo-preview
```

The GET endpoint is particularly useful for browser-based applications using the EventSource API.

### Client Implementation

The client needs to handle Server-Sent Events (SSE) to receive the streaming response. Here's an example using JavaScript:

```javascript
const eventSource = new EventSource('/assistant/stream?query=Tell me about the solar system');

eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  if (data.content === '[DONE]') {
    eventSource.close();
    return;
  }
  
  if (data.content) {
    // Append the content to the UI
    document.getElementById('response').innerHTML += data.content;
  }
};

eventSource.onerror = (error) => {
  console.error('EventSource error:', error);
  eventSource.close();
};
```

## Testing

You can test the streaming functionality using the provided HTML test pages:

1. Advanced test page (using fetch API):
   ```
   go-gpt-backend/tests/test_assistant_stream.html
   ```

2. Simple test page (using EventSource API):
   ```
   go-gpt-backend/tests/test_assistant_stream_simple.html
   ```

3. Python test script:
   ```
   go-gpt-backend/tests/test_assistant_stream.py
   ```

The simple test page is recommended for most testing as it uses the EventSource API, which is specifically designed for Server-Sent Events (SSE).

## Troubleshooting

If you're not seeing streaming responses:

1. Make sure you're running the server in async mode (`--mode async`)
2. Check that you're using the correct endpoint (`/assistant/stream`)
3. Verify that your client is properly handling Server-Sent Events
4. Check the server logs for any errors

## Flutter Mobile Integration

Flutter applications can integrate with the streaming API to provide a smooth, interactive chat experience similar to ChatGPT.

### Implementation

Since Flutter doesn't have built-in support for Server-Sent Events (SSE), you'll need to implement a custom solution using the HTTP package:

```dart
import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;

class AssistantService {
  final String baseUrl;
  
  AssistantService({required this.baseUrl});
  
  // Non-streaming request
  Future<String> queryAssistant(String query, {String? threadId}) async {
    final url = Uri.parse('$baseUrl/assistant');
    
    final payload = {
      'query': query,
      if (threadId != null) 'thread_id': threadId,
      'model': 'gpt-4-turbo-preview'
    };
    
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(payload),
    );
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['response'];
    } else {
      throw Exception('Failed to get response: ${response.statusCode}');
    }
  }
  
  // Streaming request
  Stream<String> streamAssistant(String query, {String? threadId}) async* {
    final url = Uri.parse('$baseUrl/assistant/stream');
    
    final payload = {
      'query': query,
      if (threadId != null) 'thread_id': threadId,
      'model': 'gpt-4-turbo-preview',
      'stream': true
    };
    
    final request = http.Request('POST', url);
    request.headers['Content-Type'] = 'application/json';
    request.headers['Accept'] = 'text/event-stream';
    request.body = jsonEncode(payload);
    
    final response = await http.Client().send(request);
    
    if (response.statusCode != 200) {
      throw Exception('Failed to connect: ${response.statusCode}');
    }
    
    // Process the stream
    final stream = response.stream
      .transform(utf8.decoder)
      .transform(const LineSplitter())
      .where((line) => line.startsWith('data: '))
      .map((line) => line.substring(6)) // Remove 'data: ' prefix
      .map((jsonStr) {
        try {
          return jsonDecode(jsonStr) as Map<String, dynamic>;
        } catch (e) {
          print('Error parsing JSON: $e, content: $jsonStr');
          return <String, dynamic>{'content': ''};
        }
      })
      .where((data) => data.containsKey('content') && data['content'] != '[DONE]')
      .map((data) => data['content'] as String);
    
    // Store thread ID if it's returned
    bool threadIdCaptured = threadId != null;
    
    await for (final data in stream) {
      // If we receive a thread_id and haven't captured it yet, store it
      if (!threadIdCaptured && data is Map && data.containsKey('thread_id')) {
        threadId = data['thread_id'];
        threadIdCaptured = true;
      }
      
      if (data is String && data.isNotEmpty) {
        yield data;
      }
    }
  }
}
```

### UI Integration

Here's how to integrate the streaming functionality into a Flutter chat UI:

```dart
class ChatScreen extends StatefulWidget {
  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _controller = TextEditingController();
  final List<Message> _messages = [];
  final AssistantService _service = AssistantService(baseUrl: 'https://your-api-endpoint.com');
  
  String? _threadId;
  String _currentResponse = '';
  bool _isStreaming = false;
  
  void _sendMessage() async {
    final query = _controller.text;
    if (query.isEmpty) return;
    
    setState(() {
      _messages.add(Message(content: query, isUser: true));
      _controller.clear();
      _isStreaming = true;
      _currentResponse = '';
    });
    
    try {
      _service.streamAssistant(query, threadId: _threadId).listen(
        (content) {
          setState(() {
            _currentResponse += content;
          });
        },
        onError: (error) {
          print('Error: $error');
          setState(() {
            _isStreaming = false;
          });
        },
        onDone: () {
          setState(() {
            _messages.add(Message(content: _currentResponse, isUser: false));
            _isStreaming = false;
            _currentResponse = '';
          });
        }
      );
    } catch (e) {
      print('Failed to send message: $e');
      setState(() {
        _isStreaming = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Assistant Chat')),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              itemCount: _messages.length + (_isStreaming ? 1 : 0),
              itemBuilder: (context, index) {
                if (index < _messages.length) {
                  return MessageBubble(message: _messages[index]);
                } else {
                  // Show streaming response with typing indicator
                  return MessageBubble(
                    message: Message(content: _currentResponse, isUser: false),
                    isTyping: true,
                  );
                }
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controller,
                    decoration: InputDecoration(
                      hintText: 'Type a message...',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.send),
                  onPressed: _isStreaming ? null : _sendMessage,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class Message {
  final String content;
  final bool isUser;
  
  Message({required this.content, required this.isUser});
}

class MessageBubble extends StatelessWidget {
  final Message message;
  final bool isTyping;
  
  MessageBubble({required this.message, this.isTyping = false});
  
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: message.isUser ? Colors.blue[100] : Colors.grey[200],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message.content),
            if (isTyping)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildDot(0),
                    _buildDot(1),
                    _buildDot(2),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildDot(int index) {
    return Container(
      width: 6,
      height: 6,
      margin: EdgeInsets.symmetric(horizontal: 2),
      decoration: BoxDecoration(
        color: Colors.grey[600],
        shape: BoxShape.circle,
      ),
    );
  }
}
```

### Handling Connection Issues

For mobile applications, it's important to handle network interruptions gracefully:

```dart
// Add to your AssistantService class
Stream<String> streamWithRetry(String query, {String? threadId, int maxRetries = 3}) async* {
  int attempts = 0;
  bool success = false;
  
  while (!success && attempts < maxRetries) {
    attempts++;
    try {
      await for (final content in streamAssistant(query, threadId: threadId)) {
        success = true;
        yield content;
      }
    } catch (e) {
      if (attempts >= maxRetries) {
        throw Exception('Failed after $maxRetries attempts: $e');
      }
      
      // Wait before retrying
      await Future.delayed(Duration(seconds: 2 * attempts));
    }
  }
}
```

### Performance Considerations

1. **Buffer Management**: For long responses, consider implementing a buffer to limit the amount of text rendered at once.

2. **Throttling UI Updates**: In some cases, you may want to throttle setState calls to improve performance:

```dart
StreamSubscription? _subscription;
Timer? _updateTimer;
String _buffer = '';

void _streamWithThrottling() {
  _subscription = _service.streamAssistant(query).listen(
    (content) {
      _buffer += content;
      
      // Only update UI every 100ms
      _updateTimer ??= Timer.periodic(Duration(milliseconds: 100), (_) {
        if (_buffer.isNotEmpty) {
          setState(() {
            _currentResponse += _buffer;
            _buffer = '';
          });
        }
      });
    },
    onDone: () {
      _updateTimer?.cancel();
      _updateTimer = null;
      
      // Make sure to flush any remaining buffer
      if (_buffer.isNotEmpty) {
        setState(() {
          _currentResponse += _buffer;
          _buffer = '';
        });
      }
      
      setState(() {
        _messages.add(Message(content: _currentResponse, isUser: false));
        _isStreaming = false;
        _currentResponse = '';
      });
    }
  );
}

@override
void dispose() {
  _subscription?.cancel();
  _updateTimer?.cancel();
  super.dispose();
}
```

This implementation provides a smooth typing effect similar to ChatGPT, enhancing the user experience in mobile applications.
