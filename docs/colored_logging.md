# Colored Logging

The `go-gpt-backend` application now supports colored logging to make it easier to distinguish between different log levels in the terminal output.

## Features

- Different colors for each log level:
  - **DEBUG**: Blue
  - **INFO**: Green
  - **WARNING**: Yellow
  - **ERROR**: Red
  - **CRITICAL**: White text on red background (bold)

- Option to enable or disable colors

## Usage

### Basic Usage

The default logger is already set up with colors enabled:

```python
from utils.logging import logger

# Log messages at different levels
logger.debug("Debug message")
logger.info("Info message")
logger.warning("Warning message")
logger.error("Error message")
logger.critical("Critical message")
```

### Customizing the Logger

You can create a custom logger with specific settings:

```python
from utils.logging import setup_logging

# Create a logger with colors enabled (default)
custom_logger = setup_logging(level=logging.DEBUG)

# Create a logger with colors disabled
no_color_logger = setup_logging(level=logging.INFO, enable_colors=False)
```

### Changing Log Level

To change the log level for the application:

```python
import logging
from utils.logging import setup_logging

# Set log level to DEBUG to see all messages
logger = setup_logging(level=logging.DEBUG)
```

## Implementation Details

The colored logging is implemented using ANSI escape codes, which are supported by most modern terminals. The implementation is in `utils/logging.py` and includes:

1. A custom `ColoredFormatter` class that extends `logging.Formatter`
2. Color mappings for different log levels
3. An option to enable or disable colors

## Example

A test script is available at `tests/test_colored_logging.py` that demonstrates the colored logging functionality:

```bash
python tests/test_colored_logging.py
```

This will output log messages at different levels with and without colors.
