# Authentication and Thread Persistence

This document describes the authentication system and thread persistence mechanism used in the go-gpt-backend application.

## Authentication

The application uses a simple authentication system based on the `auth0_sub` header. This header should contain a unique identifier for the user, typically provided by an Auth0 authentication service.

### Authenticated Endpoints

The following endpoints require authentication:

- All endpoints under `/assistant` 
- The `/query` endpoint

If the `auth0_sub` header is not present or is empty, the request will be rejected with a 403 Forbidden error.

### Public Endpoints

The following endpoints are public and do not require authentication:

- All endpoints under `/pub/assistant`
- The `/pub/query` endpoint

These endpoints provide the same functionality as the authenticated endpoints but without thread persistence.

### Implementation

Authentication is implemented using middleware decorators:

- `@require_auth` for Flask routes
- `@async_require_auth` for FastAPI routes

These decorators check for the presence of the `auth0_sub` header and return a 403 error if it's not present. If the header is present, it's stored in `flask.g` (for Flask routes) or passed as a parameter (for FastAPI routes) for use in the route handler.

## Thread Persistence

Thread persistence allows the application to maintain conversation context across multiple requests for the same user. This is implemented using Redis to store the thread ID for each user.

### How It Works

1. When a user makes a request to an authenticated endpoint, the `auth0_sub` header is used to identify the user.
2. The application checks if there's a thread ID stored in Redis for this user.
3. If a thread ID is found, it's used for the current request.
4. If no thread ID is found, a new thread is created and stored in Redis for future requests.

This allows the application to maintain conversation context across multiple requests without requiring the client to store and provide the thread ID.

### Implementation

Thread persistence is implemented in the `_get_or_create_thread` method of the `AssistantService` and `AsyncAssistantService` classes. This method:

1. Checks if a thread ID was provided in the request. If so, it uses that thread ID.
2. If no thread ID was provided but an `auth0_sub` was provided, it checks Redis for a stored thread ID.
3. If a thread ID is found in Redis, it uses that thread ID.
4. If no thread ID is found in Redis, it creates a new thread and stores the thread ID in Redis.
5. If no `auth0_sub` was provided, it creates a new thread without storing it in Redis.

### Redis Keys

Thread IDs are stored in Redis using keys in the format `thread:{auth0_sub}`, where `{auth0_sub}` is the value of the `auth0_sub` header.

## Public vs. Authenticated Endpoints

The application provides two sets of endpoints:

- Authenticated endpoints that require the `auth0_sub` header and provide thread persistence:
  - `/assistant/*`: Assistant API endpoints
  - `/query`: Conversation API endpoint

- Public endpoints that do not require authentication and do not provide thread persistence:
  - `/pub/assistant/*`: Public Assistant API endpoints
  - `/pub/query`: Public Conversation API endpoint

Both sets of endpoints provide the same functionality, but the authenticated endpoints maintain conversation context across multiple requests for the same user.

## Testing

Authentication and thread persistence can be tested using the `test_authentication.py` test file. This file includes tests for:

- Authentication requirements for authenticated endpoints
- Public access to public endpoints
- Thread persistence for authenticated requests
- Thread retrieval from Redis
- Automatic thread creation when no thread ID is provided
