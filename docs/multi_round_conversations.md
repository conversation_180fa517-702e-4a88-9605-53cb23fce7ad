# Multi-Round Conversation Support

This document outlines the implementation of multi-round conversation support in the Go GPT Backend, with a focus on the streaming functionality that enables real-time responses.

## Overview

The system supports multiple rounds of conversation with advanced features like:

- Context window management
- Round tracking
- Summarization of previous rounds
- Branching conversations
- Streaming responses

## Key Components

### Conversation Management

The `ConversationManager` and `AsyncConversationManager` classes handle:

- Storing and retrieving conversation history
- Managing conversation rounds
- Preparing messages for model requests
- Summarizing previous rounds for context

### Streaming Implementation

The streaming implementation allows for real-time responses from the model, which is crucial for a responsive user experience, especially in multi-round conversations.

#### Provider Implementation

Each provider (OpenAI, Anthropic, DeepSeek) implements streaming support:

```python
async def _chat_completion_impl(self, messages, model, **kwargs):
    # ...
    if stream:
        # Ensure we're returning an async iterable
        async def response_generator():
            try:
                async for chunk in response:
                    yield chunk
            except Exception as e:
                logger.error(f"Error in streaming response: {str(e)}")
                raise
        
        return response_generator()  # Return an async generator
    # ...
```

#### API Endpoint

The `/query` endpoint in `async_app.py` handles streaming responses:

```python
if request.stream:
    return StreamingResponse(
        stream_response(provider, messages, model_name, session_id, conversation, round_id),
        media_type="text/event-stream"
    )
```

#### Stream Processing

The `stream_response` function processes the streaming response:

```python
async def stream_response(provider, messages, model, session_id, conversation, round_id):
    # ...
    try:
        async for chunk in response_stream:
            # Process each chunk
            # ...
    except Exception as e:
        # Handle errors
        # ...
    # ...
```

## Error Handling

The implementation includes robust error handling to ensure that the conversation can continue even if there are issues with the streaming response:

- Each provider has error handling in its streaming implementation
- The `stream_response` function has multiple layers of error handling:
  - Outer try/except for the entire streaming process
  - Inner try/except for iterating through the stream
  - Inner try/except for processing each chunk

## Multi-Round Support

The system supports multiple rounds of conversation through:

1. **Round Tracking**: Each message is associated with a round ID
2. **Context Management**: Messages from the current round are included in the context
3. **Summarization**: Previous rounds are summarized to maintain context without exceeding token limits
4. **Round-Aware Search**: Web search is performed with awareness of the conversation round

## Best Practices

When implementing multi-round conversations with streaming:

1. Always ensure that streaming responses are properly handled as async iterables
2. Implement robust error handling at multiple levels
3. Collect the full response for conversation history
4. Add the response to the conversation history after streaming is complete
5. Use proper round tracking to maintain context across multiple rounds

## Debugging

The system includes comprehensive debug logging to help diagnose issues with multi-round conversations and streaming:

### Model Selection Logging

```python
# Log the selected provider and model for debugging
logger.info(f"Selected provider: {provider_name}, model: {model_name} for session {session_id}")
```

### Streaming Process Logging

```python
# Log streaming request details
provider_name = provider.__class__.__name__
logger.info(f"Streaming with provider: {provider_name}, model: {model}, session: {session_id}, round: {round_id}")
```

### Provider Implementation Logging

```python
async def response_generator():
    try:
        logger.debug(f"Starting to stream response with model {model}")
        chunk_count = 0
        async for chunk in response:
            chunk_count += 1
            if chunk_count % 10 == 0:  # Log every 10th chunk to avoid excessive logging
                logger.debug(f"Streaming chunk {chunk_count} from model {model}")
            yield chunk
        logger.info(f"Completed streaming {chunk_count} chunks from model {model}")
    except Exception as e:
        logger.error(f"Error in streaming response from model {model}: {str(e)}")
        raise
```

These logs provide visibility into:

1. Which provider and model is selected for each request
2. The streaming process, including start, progress, and completion
3. Any errors that occur during streaming
4. The type of response objects being handled

## Connection Error Debugging

The system includes enhanced error handling to diagnose connection issues with model providers:

```python
try:
    response = await self.client.chat.completions.create(
        model=model,
        messages=messages,
        stream=stream,
        timeout=timeout,
        **kwargs
    )
    logger.debug(f"API request successful, response type: {type(response).__name__}")
except httpx.ConnectTimeout as e:
    logger.error(f"Connection timeout to {self.client.base_url}: {str(e)}")
    raise Exception(f"Connection timeout to API server: {str(e)}")
except httpx.ReadTimeout as e:
    logger.error(f"Read timeout from {self.client.base_url}: {str(e)}")
    raise Exception(f"Read timeout from API server: {str(e)}")
except httpx.ConnectError as e:
    logger.error(f"Connection error to {self.client.base_url}: {str(e)}")
    raise Exception(f"Cannot connect to API server: {str(e)}")
except Exception as e:
    error_type = type(e).__name__
    error_message = str(e)
    logger.error(f"Unexpected error during API request: {error_type}: {error_message}")
    
    # Check if this is a connection error
    if "Connection" in error_type or "Connection" in error_message:
        # Log more details about the connection
        logger.error(f"Connection details: URL={self.client.base_url}, API Key present: {bool(self.client.api_key)}")
        
        # Check if we can ping the API endpoint
        try:
            import socket
            from urllib.parse import urlparse
            
            parsed_url = urlparse(self.client.base_url)
            host = parsed_url.netloc
            port = 443 if parsed_url.scheme == 'https' else 80
            
            logger.info(f"Attempting to check connectivity to {host}:{port}")
            
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.settimeout(5)
            result = s.connect_ex((host, port))
            s.close()
            
            if result == 0:
                logger.info(f"Successfully connected to {host}:{port}")
            else:
                logger.error(f"Failed to connect to {host}:{port}, error code: {result}")
        except Exception as conn_e:
            logger.error(f"Error checking connectivity: {str(conn_e)}")
    
    raise
```

### Common Connection Issues

1. **Empty API Keys**: If API keys are not set in the .env file, the system will use empty values which will cause connection errors. The system now logs whether an API key is present (without revealing the full key) to help diagnose this issue.

2. **Network Connectivity**: Connection errors may occur if the server cannot reach the API endpoints. The system now includes socket-level connectivity testing that attempts to establish a direct TCP connection to the API endpoint to verify basic network connectivity.

3. **API Endpoint Availability**: The API endpoints may be temporarily unavailable. The enhanced error logging now distinguishes between different types of connection issues (timeout vs. connection refused) to help identify if the issue is with the endpoint itself.

4. **Timeouts**: Long-running requests may time out. The system now includes a configurable timeout parameter and specific error handling for both connection timeouts and read timeouts.

5. **DNS Resolution Issues**: The system can now detect if there are problems resolving the hostname of the API endpoint by attempting a direct socket connection.

### Debugging Connection Errors

When a connection error occurs, the system now:

1. Logs the specific error type and message
2. Checks if the API key is present (without revealing the full key)
3. Attempts to establish a direct socket connection to the API endpoint
4. Reports the result of the socket connection attempt
5. Provides a detailed error message that distinguishes between different types of connection issues

This enhanced error handling makes it much easier to diagnose connection issues, especially in multi-provider environments where different providers may have different connection requirements.
