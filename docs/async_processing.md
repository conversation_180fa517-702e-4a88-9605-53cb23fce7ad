# Asynchronous Processing in Go GPT Backend

This document describes the asynchronous processing implementation in the Go GPT Backend.

## Overview

The Go GPT Backend now supports asynchronous processing using FastAPI and the OpenAI async client. This allows for better performance and scalability, especially when handling multiple concurrent requests.

## Components

### Async Redis Client

The async Redis client (`utils/async_redis_client.py`) provides asynchronous access to Redis for storing conversation history and other data. It uses the `aioredis` library to provide non-blocking Redis operations.

### Async Model Providers

The async model providers (`providers/async_*_provider.py`) provide asynchronous access to the various LLM APIs. They use the OpenAI async client to make non-blocking API calls.

- `AsyncModelProvider`: Base class for all async providers
- `AsyncOpenAIProvider`: Async provider for OpenAI API
- `AsyncAnthropicProvider`: Async provider for Anthropic API
- `AsyncDeepSeekProvider`: Async provider for DeepSeek API

### Async Conversation Manager

The async conversation manager (`core/async_conversation.py`) provides asynchronous access to conversation history and context. It uses the async Redis client to store and retrieve conversation data.

### Async FastAPI App

The async FastAPI app (`async_app.py`) provides a FastAPI-based API for the Go GPT Backend. It uses the async model providers and conversation manager to handle requests asynchronously.

## Running the Async App

To run the async app, use the following command:

```bash
python run.py --mode async
```

You can also specify the port and host:

```bash
python run.py --mode async --port 5002 --host 0.0.0.0
```

## API Endpoints

The async app provides the following API endpoints:

- `GET /`: Health check endpoint
- `POST /query`: Process a query from the user
- `POST /clear`: Clear the conversation history

## Benefits of Async Processing

- **Improved Performance**: Async processing allows the server to handle more concurrent requests without blocking.
- **Better Resource Utilization**: Async processing makes better use of system resources by not blocking threads while waiting for I/O operations.
- **Scalability**: Async processing allows the server to scale better with increasing load.
- **Streaming Responses**: Async processing makes it easier to implement streaming responses, which can improve the user experience.

## Implementation Details

### Error Handling

The async implementation includes robust error handling with circuit breakers, retries, and fallbacks. This ensures that the system can recover from transient errors and continue to function.

### Streaming Responses

The async implementation supports streaming responses, which allows the client to receive partial responses as they are generated. This can improve the user experience by showing results as they become available.

### Concurrency Control

The async implementation includes concurrency control to prevent overwhelming the LLM APIs. This ensures that the system can handle high load without exceeding API rate limits.

## Testing

The async implementation includes comprehensive tests in `tests/test_async.py`. These tests cover the async Redis client, async model providers, async conversation manager, and async FastAPI app.

## Future Improvements

- **Async Web Search**: Implement async versions of the web search functions to further improve performance.
- **Async Circuit Breakers**: Implement async versions of the circuit breaker, retry, and fallback decorators.
- **Distributed Processing**: Implement distributed processing using message queues to further improve scalability.
