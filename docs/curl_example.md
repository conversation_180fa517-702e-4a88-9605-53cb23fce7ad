# Query API cURL Examples

## Basic Query

```curl
curl -X POST http://localhost:8080/query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Analyze whether Tesla stock is worth buying",
    "provider_model": "openai/deepseek-ai/DeepSeek-V3",
    "stream": "false"
  }'
```

## Streaming with Function Calling

```curl
curl -X POST http://localhost:8080/query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What is the weather in Seattle?",
    "provider_model": "openai/gpt-4",
    "stream": true,
    "enable_functions": true,
    "functions": ["get_weather"]
  }'
```

## Conversation Context

```curl
curl -X POST http://localhost:8080/query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Tell me more about that",
    "session_id": "previous-session-id",
    "include_summary": true
  }'
```
