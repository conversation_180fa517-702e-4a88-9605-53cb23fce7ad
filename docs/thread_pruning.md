# Thread Pruning for OpenAI Assistant API

This document describes the thread pruning functionality implemented in the `go-gpt-backend` project to manage thread message length and reduce token consumption for OpenAI Assistant API threads.

## Overview

The OpenAI Assistant API uses threads to maintain conversation context. As conversations grow longer, the number of messages in a thread increases, which can lead to:

1. Higher token consumption (and thus higher costs)
2. Slower response times
3. Potential context window limitations

The thread pruning functionality automatically manages thread length by removing older messages when threads exceed a configurable threshold, while preserving enough context for the conversation to remain coherent.

## Implementation

The thread pruning functionality is implemented in the `utils/thread_pruning.py` module and integrated with the `AsyncAssistantService` class. It provides several pruning strategies and configuration options.

### Key Components

1. **ThreadPruningConfig**: Configuration class that loads settings from environment variables
2. **ThreadPruningManager**: Main class that manages pruning operations
3. Pruning strategies:
   - Simple pruning (keep N most recent messages)
   - Role-based pruning (maintain balance between user and assistant messages)
   - Conversation-aware pruning (preserve complete conversation exchanges)

### Pruning Triggers

Pruning can be triggered at different points:

1. **Pre-request pruning**: Before processing a new user query
2. **Post-response pruning**: After receiving a response from the API
3. **Background pruning**: Periodic pruning of all threads

## Configuration

The thread pruning functionality can be configured using environment variables:

| Environment Variable | Default | Description |
|----------------------|---------|-------------|
| `THREAD_PRUNING_ENABLED` | `true` | Enable/disable thread pruning |
| `THREAD_MAX_MESSAGES` | `8` | Maximum number of messages before pruning is triggered |
| `THREAD_TARGET_LENGTH` | `6` | Target number of messages to keep after pruning |
| `THREAD_PRUNING_STRATEGY` | `simple` | Pruning strategy (`simple`, `role_based`, or `conversation`) |
| `THREAD_PRE_REQUEST_PRUNE` | `true` | Enable/disable pre-request pruning |
| `THREAD_POST_RESPONSE_PRUNE` | `false` | Enable/disable post-response pruning |
| `THREAD_BACKGROUND_PRUNE_INTERVAL` | `3600` | Interval in seconds for background pruning |
| `THREAD_MIN_TIME_BETWEEN_PRUNES` | `300` | Minimum time in seconds between pruning operations on the same thread |

## Pruning Strategies

### Simple Pruning

The simple pruning strategy keeps the N most recent messages and deletes the oldest ones. This is the default strategy and works well for most use cases.

```python
await prune_thread_messages(thread_id, max_messages=6)
```

### Role-Based Pruning

The role-based pruning strategy maintains a balance between user and assistant messages, ensuring that both sides of the conversation are preserved proportionally.

```python
await role_based_pruning(thread_id, max_messages=6)
```

### Conversation-Aware Pruning

The conversation-aware pruning strategy preserves complete conversation exchanges (user question + assistant response), ensuring that context is maintained in a more natural way.

```python
await conversation_aware_pruning(thread_id, max_exchanges=3)
```

## Integration with AsyncAssistantService

The thread pruning functionality is integrated with the `AsyncAssistantService` class in the following ways:

1. A `ThreadPruningManager` is initialized in the service's constructor
2. Pre-request pruning is performed in the `_get_or_create_thread` method
3. Post-response pruning is scheduled as a background task after responses are received
4. Background pruning is started when the service is initialized

## Metrics and Monitoring

The thread pruning functionality maintains metrics about pruning operations in Redis, including:

- Last pruning timestamp
- Number of messages deleted
- Number of messages before and after pruning
- Estimated tokens saved

These metrics can be retrieved using the `get_pruning_metrics` function:

```python
metrics = await get_pruning_metrics(thread_id=thread_id)
```

## Testing

A test script is provided in `tests/test_thread_pruning.py` to verify the functionality of the thread pruning module. It tests all pruning strategies and the `ThreadPruningManager` class.

To run the tests:

```bash
python -m tests.test_thread_pruning
```

## Best Practices

1. **Choose the right strategy**: The simple strategy works well for most use cases, but consider role-based or conversation-aware pruning for more complex conversations.

2. **Configure appropriate thresholds**: Set `THREAD_MAX_MESSAGES` and `THREAD_TARGET_LENGTH` based on your application's needs. Higher values preserve more context but consume more tokens.

3. **Monitor token usage**: Use the pruning metrics to monitor token savings and adjust configuration as needed.

4. **Consider pre-request vs. post-response pruning**: Pre-request pruning ensures threads are pruned before processing new queries, while post-response pruning allows the model to see the full context before pruning.

## Example Usage

```python
# Initialize the pruning manager
manager = ThreadPruningManager(client)

# Start background pruning
asyncio.create_task(manager.start_background_pruning())

# Prune a thread if needed
result = await manager.prune_if_needed(thread_id)

# Force pruning regardless of thresholds
result = await manager.prune_if_needed(thread_id, force=True)

# Get pruning metrics
metrics = await get_pruning_metrics(thread_id=thread_id)
```

## Conclusion

The thread pruning functionality provides an efficient way to manage thread length and reduce token consumption for OpenAI Assistant API threads. By automatically removing older messages while preserving enough context, it helps optimize costs and performance while maintaining conversation quality.
