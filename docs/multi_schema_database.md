# Multi-Schema Database Support

This document describes the multi-schema database support in the go-gpt-backend application. It explains how to use multiple schemas in a single database instance, why it's useful, and how to implement it in your own code.

## Overview

The multi-schema database support allows the application to use multiple schemas in a single database instance. This is useful for:

1. **Data Isolation**: Keep different types of data in separate schemas to improve organization and security.
2. **Multi-tenancy**: Support multiple tenants in a single database instance, with each tenant having its own schema.
3. **Performance**: Improve query performance by partitioning data across schemas.
4. **Maintenance**: Simplify database maintenance by allowing schema-level operations.

## Architecture

The multi-schema database support consists of the following components:

1. **Schema Registry**: A table that tracks all schemas in the database, including metadata such as description and active status.
2. **Multi-Schema DB Manager**: A utility class that manages database connections and sessions for different schemas.
3. **Multi-Schema Service**: A service that provides higher-level functionality for managing schemas and executing cross-schema queries.
4. **Database Migrations**: A system for applying migrations to multiple schemas.

## Schema Registry

The schema registry is a table in the default schema that tracks all schemas in the database. It includes the following information:

- **Schema Name**: The name of the schema.
- **Description**: A description of the schema.
- **Active Status**: Whether the schema is active or not.
- **Created At**: When the schema was created.
- **Updated At**: When the schema was last updated.

The schema registry is used to:

1. Track all schemas in the database.
2. Provide metadata about each schema.
3. Control which schemas are active.

## Multi-Schema DB Manager

The multi-schema database manager (`utils/multi_schema_db_manager.py`) is a utility class that manages database connections and sessions for different schemas. It provides the following functionality:

1. **Create Schema**: Create a new schema if it doesn't exist.
2. **List Schemas**: Get a list of all schemas in the database.
3. **Get DB Engine**: Get a database engine for a specific schema.
4. **Get DB Session**: Get a database session for a specific schema.
5. **Execute Query**: Execute a SQL query on a specific schema.
6. **Close**: Close all database connections.

The multi-schema database manager is used by the multi-schema service to provide higher-level functionality.

## Multi-Schema Service

The multi-schema service (`services/async_multi_schema_service.py`) provides higher-level functionality for managing schemas and executing cross-schema queries. It provides the following functionality:

1. **Create Schema**: Create a new schema and register it in the schema registry.
2. **Get Schema**: Get a schema from the schema registry.
3. **Get Schemas**: Get all schemas from the schema registry.
4. **Update Schema**: Update a schema in the schema registry.
5. **Activate Schema**: Activate a schema.
6. **Deactivate Schema**: Deactivate a schema.
7. **Delete Schema**: Delete a schema from the schema registry.
8. **Execute Cross-Schema Query**: Execute a SQL query that spans multiple schemas.

The multi-schema service is used by the application to manage schemas and execute cross-schema queries.

## Database Migrations

The database migrations system (`db/migrations/db_migrations.py`) is used to apply migrations to multiple schemas. It provides the following functionality:

1. **Run Migrations**: Run all migrations on a specific schema.
2. **Run Migrations on Multiple Schemas**: Run all migrations on multiple schemas.
3. **Track Migration**: Track a migration in the schema_migrations table.
4. **Get Applied Migrations**: Get a list of applied migrations.

The database migrations system is used by the application to apply migrations to multiple schemas.

## Usage

### Creating a Schema

To create a new schema, use the `create_schema` method of the multi-schema service:

```python
from services.async_multi_schema_service import multi_schema_service

# Create a new schema
await multi_schema_service.create_schema("my_schema", "My schema description")
```

### Getting a Schema

To get a schema from the schema registry, use the `get_schema` method of the multi-schema service:

```python
from services.async_multi_schema_service import multi_schema_service

# Get a schema
schema = await multi_schema_service.get_schema("my_schema")
```

### Getting All Schemas

To get all schemas from the schema registry, use the `get_schemas` method of the multi-schema service:

```python
from services.async_multi_schema_service import multi_schema_service

# Get all schemas
schemas = await multi_schema_service.get_schemas()
```

### Updating a Schema

To update a schema in the schema registry, use the `update_schema` method of the multi-schema service:

```python
from services.async_multi_schema_service import multi_schema_service

# Update a schema
await multi_schema_service.update_schema("my_schema", description="New description", is_active=True)
```

### Activating a Schema

To activate a schema, use the `activate_schema` method of the multi-schema service:

```python
from services.async_multi_schema_service import multi_schema_service

# Activate a schema
await multi_schema_service.activate_schema("my_schema")
```

### Deactivating a Schema

To deactivate a schema, use the `deactivate_schema` method of the multi-schema service:

```python
from services.async_multi_schema_service import multi_schema_service

# Deactivate a schema
await multi_schema_service.deactivate_schema("my_schema")
```

### Deleting a Schema

To delete a schema from the schema registry, use the `delete_schema` method of the multi-schema service:

```python
from services.async_multi_schema_service import multi_schema_service

# Delete a schema
await multi_schema_service.delete_schema("my_schema")
```

### Executing a Cross-Schema Query

To execute a SQL query that spans multiple schemas, use the `execute_cross_schema_query` method of the multi-schema service:

```python
from services.async_multi_schema_service import multi_schema_service

# Execute a cross-schema query
result = await multi_schema_service.execute_cross_schema_query(
    "my_schema",
    """
    SELECT s.ticker, s.name, s.price, u.username, u.email
    FROM stocks s
    CROSS JOIN other_schema.users u
    LIMIT 10
    """
)
```

### Running Migrations on Multiple Schemas

To run migrations on multiple schemas, use the `run_migrations_on_multiple_schemas` method of the database migrations system:

```python
from db.migrations.db_migrations import run_migrations_on_multiple_schemas

# Run migrations on multiple schemas
await run_migrations_on_multiple_schemas(["my_schema", "other_schema"])
```

## Examples

For examples of how to use the multi-schema database support, see:

1. **Multi-Schema Example**: `examples/multi_schema_example.py`
2. **Multi-Schema Test**: `tests/test_multi_schema_db.py`

You can run the example using the provided script:

```bash
./run_multi_schema_example.sh
```

And you can run the test using the provided script:

```bash
./run_test_multi_schema_db.sh
```

## Best Practices

When using the multi-schema database support, follow these best practices:

1. **Use the Multi-Schema Service**: Use the multi-schema service to manage schemas and execute cross-schema queries, rather than using the multi-schema database manager directly.
2. **Track Schemas in the Registry**: Always register schemas in the schema registry, so they can be tracked and managed.
3. **Use Migrations**: Use the database migrations system to apply migrations to multiple schemas, rather than executing SQL directly.
4. **Close Connections**: Always close database connections when you're done with them, to avoid connection leaks.
5. **Use Cross-Schema Queries Sparingly**: Cross-schema queries can be less efficient than single-schema queries, so use them sparingly.
6. **Consider Performance**: When designing your schema structure, consider the performance implications of your design.

## Troubleshooting

If you encounter issues with the multi-schema database support, check the following:

1. **Database Connection**: Make sure the database connection is working correctly.
2. **Schema Existence**: Make sure the schema exists in the database.
3. **Schema Registry**: Make sure the schema is registered in the schema registry.
4. **Schema Active Status**: Make sure the schema is active.
5. **Cross-Schema Permissions**: Make sure the database user has permissions to access all schemas in a cross-schema query.
6. **Migration Errors**: Check for errors in the database migrations.

## Conclusion

The multi-schema database support provides a powerful way to organize and manage data in a single database instance. By using multiple schemas, you can improve data isolation, support multi-tenancy, improve performance, and simplify database maintenance.
