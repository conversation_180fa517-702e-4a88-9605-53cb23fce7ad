# Query Flow in Go-GPT-Backend

This document outlines the code flow when a client sends a `/query` request to the Go-GPT-Backend service.

## Overview

When a client sends a query to the Go-GPT-Backend service, the request goes through several layers of processing before a response is returned. The flow involves request validation, rate limiting, model selection, caching, and interaction with LLM providers.

## Detailed Flow

### 1. HTTP Request Handling

1. The client sends a POST request to the `/query` endpoint.
2. The Flask/FastAPI server receives the request and routes it to the appropriate handler.
   - For synchronous requests: `app.py` routes to `api/routes.py`
   - For asynchronous requests: `async_app.py` routes to `api/async_routes.py`

### 2. Request Validation and Preprocessing

1. The request body is parsed and validated.
2. Required parameters are checked (messages, model, etc.).
3. Rate limiting is applied via the middleware (`middleware/rate_limiting.py`).
4. The conversation ID is extracted or generated.

### 3. Model Selection

1. The `core/model_selection.py` module determines the appropriate model to use.
2. If a specific model is requested, it's validated against available models.
3. If no model is specified, the default model is selected based on configuration.

### 4. Cache Check

1. The system checks if a response for this exact query is already cached:
   - First in memory cache (fastest)
   - Then in Redis cache
   - Finally in disk cache (if enabled)
2. If a similar query is found (using Levenshtein distance or basic string comparison), that response may be returned.
3. If a cached response is found, it's returned immediately without calling the LLM provider.

### 5. Provider Selection and Request Preparation

1. The appropriate provider is selected based on the model (`providers/provider_factory.py`).
2. The request is formatted according to the provider's requirements.
3. Any provider-specific parameters are applied.

### 6. Function Calling Detection

1. The system checks if the query might benefit from function calling.
2. If function calling is appropriate, the available functions are included in the request to the LLM.

### 7. LLM API Call

1. The request is sent to the LLM provider (OpenAI, Anthropic, DeepSeek, etc.).
2. The system handles retries and circuit breaking for reliability.
3. The response is received from the provider.

### 8. Function Execution (if applicable)

1. If the LLM response includes a function call, the function is executed.
2. The function results are sent back to the LLM for incorporation into the final response.
3. This may involve multiple rounds of communication with the LLM.

### 9. Search Enhancement (if applicable)

1. If search enhancement is enabled, the system may perform web searches.
2. Search results are incorporated into the response.
3. Round-aware search may be used to maintain context across conversation turns.

### 10. Response Caching

1. The final response is cached for future use:
   - In memory cache for fast access
   - In Redis for persistence
   - In disk cache (if enabled) for long-term storage
2. Compression is applied to reduce storage requirements.

### 11. Response Formatting and Return

1. The response is formatted according to the API contract.
2. Usage statistics and metadata are added.
3. The response is returned to the client.

## Key Components Involved

- **API Routes**: Handle HTTP requests and responses
- **Middleware**: Apply rate limiting and other cross-cutting concerns
- **Model Selection**: Choose the appropriate model
- **Providers**: Interface with different LLM providers
- **Cache System**: Store and retrieve responses
- **Conversation Service**: Manage conversation state
- **Function Registry**: Register and execute functions
- **Search Utils**: Enhance responses with web search results

## Caching System

The caching system is a critical component that improves performance and reduces costs. It includes:

1. **Multi-level Cache**:
   - Memory cache (fastest, but volatile)
   - Redis cache (persistent, shared across instances)
   - Disk cache (optional, for long-term storage)

2. **Compression**:
   - LZ4 for speed (when available)
   - zlib for better compression ratio
   - Automatic selection based on data characteristics

3. **Similar Query Detection**:
   - Uses Levenshtein distance when available
   - Falls back to basic string comparison
   - Configurable similarity threshold

4. **Cache Management**:
   - LRU (Least Recently Used) eviction policy
   - Size limits to prevent memory issues
   - TTL (Time To Live) for automatic expiration
   - Statistics for monitoring

## Async vs Sync Processing

The system supports both synchronous and asynchronous processing:

- **Synchronous** (`app.py`, `routes.py`): Traditional request-response model
- **Asynchronous** (`async_app.py`, `async_routes.py`): Non-blocking processing for better concurrency

Both paths share the same core logic but use different implementations for I/O operations.
