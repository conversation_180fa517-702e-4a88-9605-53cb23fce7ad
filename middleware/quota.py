"""
Quota middleware for API routes.
"""
from functools import wraps
from fastapi import HTTPException
from config.settings import settings
from utils.logging import logger
from utils.quota_manager import AsyncQuotaManager

def async_quota_limit(endpoint: str):
    """
    Async decorator for quota limiting specific routes.
    
    Args:
        endpoint: The endpoint name for quota tracking
        
    Returns:
        The decorated async function
    """
    def decorator(f):
        @wraps(f)
        async def decorated_function(*args, **kwargs):
            # Get auth0_sub from kwargs
            auth0_sub = kwargs.get('auth0_sub')
            
            if not auth0_sub:
                logger.warning("No auth0_sub found for quota check, skipping")
                return await f(*args, **kwargs)
            
            # Check quota
            quota_available = await AsyncQuotaManager.check_quota(auth0_sub, endpoint)
            
            if not quota_available:
                logger.warning(f"Quota exceeded for user {auth0_sub} on endpoint {endpoint}")
                raise HTTPException(
                    status_code=429,
                    detail=settings.QUOTA_RATELIMIT_MSG,
                )
            
            # Execute the function
            result = await f(*args, **kwargs)
            
            # Increment quota after successful execution
            await AsyncQuotaManager.increment_quota(auth0_sub, endpoint)
            
            return result
        
        return decorated_function
    
    return decorator
