"""
Authentication middleware for Flask and FastAPI routes.
"""
import sys
from functools import wraps
from utils.logging import logger

def require_auth(f):
    """
    Decorator for requiring authentication on routes.
    Checks for auth0_sub in request headers and returns 403 if not present.
    
    Args:
        f: The function to decorate
        
    Returns:
        The decorated function
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Import Flask modules here to avoid circular imports
        from flask import request, jsonify, g, has_request_context
        
        # Check if we're in a request context
        if not has_request_context():
            logger.warning("Auth middleware called outside request context - bypassing auth check")
            return f(*args, **kwargs)
            
        auth0_sub = request.headers.get('auth0_sub')
        if not auth0_sub:
            logger.warning("Authentication required: Missing auth0_sub header")
            return jsonify({"error": "Authentication required"}), 403
        
        # Store auth0_sub in flask.g for access in the route handler
        g.auth0_sub = auth0_sub
        return f(*args, **kwargs)
    
    return decorated_function

def async_require_auth(f):
    """
    Async decorator for requiring authentication on routes.
    Checks for auth0_sub in request headers and returns 403 if not present.
    
    Args:
        f: The async function to decorate
        
    Returns:
        The decorated async function
    """
    @wraps(f)
    async def decorated_function(*args, **kwargs):
        # For FastAPI routes, auth0_sub is passed as a Header parameter
        # This will be in kwargs if the route has auth0_sub: str = Header(None)
        logger.info(f"{kwargs}")
        auth0_sub = kwargs.get('auth0_sub')
        
        # If auth0_sub is not in kwargs and we're in a Flask context, check request headers
        if not auth0_sub and 'flask' in sys.modules:
            from flask import request, has_request_context
            
            # Only try to access request if we're in a request context
            if has_request_context():
                auth0_sub = request.headers.get('auth0_sub')
                logger.debug(f"Got auth0_sub from Flask request headers: {auth0_sub is not None}")
            else:
                logger.debug("Not in Flask request context, skipping request.headers check")
        
        # If we still don't have auth0_sub, return 403
        if not auth0_sub:
            logger.warning("Authentication required: Missing auth0_sub header")
            
            # For Flask (if we're in a request context)
            if 'flask' in sys.modules:
                from flask import request, has_request_context, jsonify
                if has_request_context() and hasattr(request, 'blueprint'):
                    return jsonify({"error": "Authentication required"}), 403
            
            # For FastAPI
            from fastapi import HTTPException
            raise HTTPException(status_code=403, detail="Authentication required")
        
        # For Flask routes, store in g if we're in a request context
        if 'flask' in sys.modules:
            from flask import g, has_request_context
            if has_request_context():
                g.auth0_sub = auth0_sub
                logger.debug(f"Stored auth0_sub in Flask g: {auth0_sub}")
            
        # Return the original function with all args
        return await f(*args, **kwargs)
    
    return decorated_function
