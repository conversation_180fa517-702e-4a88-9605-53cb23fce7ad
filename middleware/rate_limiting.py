from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from config.settings import settings
from functools import wraps

def rate_limit(f):
    """
    Decorator for rate limiting specific routes.
    This is a placeholder that will be replaced by the actual limiter when setup_rate_limiter is called.
    
    Args:
        f: The function to decorate
        
    Returns:
        The decorated function
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        return f(*args, **kwargs)
    return decorated_function

def async_rate_limit(f):
    """
    Async decorator for rate limiting specific routes.
    This is a placeholder that will be replaced by the actual limiter when setup_rate_limiter is called.
    
    Args:
        f: The async function to decorate
        
    Returns:
        The decorated async function
    """
    @wraps(f)
    async def decorated_function(*args, **kwargs):
        return await f(*args, **kwargs)
    return decorated_function

def setup_rate_limiter(app):
    """
    Set up rate limiting for the Flask application.
    
    Args:
        app: The Flask application
        
    Returns:
        Limiter: The configured rate limiter
    """
    # Default rate limits if not specified in settings
    default_rate_limit = getattr(settings, 'DEFAULT_RATE_LIMIT', "200 per day, 50 per hour")
    query_rate_limit = getattr(settings, 'QUERY_RATE_LIMIT', "100 per day, 20 per hour")
    
    limiter = Limiter(
        get_remote_address,
        app=app,
        default_limits=[default_rate_limit],
        storage_uri=f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}"
    )
    
    # Apply specific rate limits to routes
    for rule in app.url_map.iter_rules():
        if rule.rule == '/query' and 'POST' in rule.methods:
            limiter.limit(query_rate_limit)(app.view_functions[rule.endpoint])
    
    return limiter
