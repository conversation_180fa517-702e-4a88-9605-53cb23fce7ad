# Server settings
PORT=5002

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=stock_data
DB_USER=root
DB_PASSWORD=

# Cache settings
ENABLE_CACHE=true
CACHE_TTL=3600  # 1 hour in seconds
CACHE_COMPRESSION=true
CACHE_MEMORY_ENABLED=true
CACHE_MEMORY_MAX_ITEMS=1000
CACHE_MEMORY_TTL=300  # 5 minutes in seconds
CACHE_PERSISTENT_ENABLED=false
CACHE_PERSISTENT_PATH=./cache
CACHE_LRU_POLICY=true
CACHE_MAX_SIZE_MB=1024  # 1GB
CACHE_SIMILARITY_THRESHOLD=0.9  # 90% similarity

# Assistant API quota settings
ASSISTANT_DAILY_QUOTA=5  # Maximum number of requests per day
ASSISTANT_QUOTA_ENABLED=true  # Whether to enable quota limits
ASSISTANT_WHITELIST=[]  # List of user IDs that are exempt from quota limits

# Model Providers Configuration
DEEPSEEK_API_KEY=your_deepseek_api_key
DEEPSEEK_API_URL=https://api.deepseek.com/v1

OPENAI_API_KEY=your_openai_api_key
OPENAI_API_URL=https://api.openai.com/v1

# OpenAI Assistant API Configuration
OPENAI_ASSISTANT_ID=your_openai_assistant_id
OPENAI_ASSISTANT_NAME=Go GPT Assistant
OPENAI_ASSISTANT_MODEL=gpt-4-turbo-preview
OPENAI_ASSISTANT_INSTRUCTIONS=You are a helpful assistant that provides accurate and concise information.
OPENAI_ASSISTANT_TOOLS=[]
OPENAI_ASSISTANT_TOOL_RESOURCES=[]

ANTHROPIC_API_KEY=your_anthropic_api_key
ANTHROPIC_API_URL=https://api.anthropic.com/v1

# File search settings
ENABLE_FILE_SEARCH=true

# Web search settings
GOOGLE_SEARCH_API_KEY=your_google_search_api_key
GOOGLE_SEARCH_ENGINE_ID=your_google_search_engine_id
SERP_API_KEY=your_serp_api_key
USE_SERP_API=false
USE_GPT4O_SEARCH=true

# Multi-round conversation settings
MAX_CONCURRENT_SEARCHES=5
DEFAULT_SEARCH_THRESHOLD=0.5

# Round-aware search settings
SEARCH_CACHE_TTL=3600  # 1 hour in seconds
AUTO_DETECT_SEARCH=true
SEARCH_RESULT_MAX_CHARS=8000

# Flask settings
FLASK_ENV=development
FLASK_APP=run.py
