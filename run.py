import os
import argparse
import uvicorn

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run Go GPT Backend")
    parser.add_argument(
        "--mode",
        type=str,
        choices=["sync", "async"],
        default="sync",
        help="Run mode: sync (Flask) or async (FastAPI)"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=5000,
        help="Port to run the server on"
    )
    parser.add_argument(
        "--host",
        type=str,
        default="0.0.0.0",
        help="Host to run the server on"
    )
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload"
    )
    
    args = parser.parse_args()
    
    print('args: ', args)

    if args.mode == "async":
        # Run FastAPI app
        port = int(os.getenv("PORT", args.port))
        uvicorn.run(
            "async_app:app",
            host=args.host,
            port=port,
            reload=args.reload
        )
    else:
        # Run Flask app
        from app import create_app
        app = create_app()
        port = int(os.getenv("PORT", args.port))
        app.run(host=args.host, port=port, debug=args.reload)
