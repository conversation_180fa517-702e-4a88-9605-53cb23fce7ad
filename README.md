# Go GPT Backend

A scalable backend service for interacting with various LLM providers (OpenAI, Anthropic, DeepSeek) with advanced web search capabilities and multi-round conversation support.

## Project Structure

```
go-gpt-backend/
├── api/               # API routes and handlers
├── core/              # Core business logic
│   ├── conversation.py       # Sync conversation manager
│   └── async_conversation.py # Async conversation manager
├── services/          # Service layer implementations
├── models/            # Data models and schemas
├── utils/             # Utility functions and helpers
│   ├── redis_client.py         # Sync Redis client
│   ├── async_redis_client.py   # Async Redis client
│   ├── search_utils.py         # Basic web search utilities
│   ├── enhanced_search.py      # Enhanced web search with multiple providers
│   ├── round_aware_search.py   # Intelligent round-aware search
│   ├── cache.py                # Sync caching utilities
│   ├── async_cache.py          # Async caching utilities
│   └── circuit_breaker.py      # Circuit breaker pattern implementation
├── config/            # Configuration management
├── providers/         # Model provider implementations
│   ├── base_provider.py                # Sync base provider
│   ├── async_base_provider.py          # Async base provider
│   ├── openai_provider.py              # Sync OpenAI provider
│   ├── async_openai_provider.py        # Async OpenAI provider
│   ├── openai_assistant_provider.py    # Sync OpenAI Assistant provider
│   ├── async_openai_assistant_provider.py # Async OpenAI Assistant provider
│   └── ...
├── middleware/        # API middleware
├── tests/             # Test files
├── docs/              # Documentation
│   ├── async_processing.md      # Async processing documentation
│   ├── assistant_api.md         # OpenAI Assistant API documentation
│   ├── assistant_streaming.md   # Assistant streaming documentation
│   ├── assistant_streaming_fix.md # Fix for assistant streaming error
│   ├── authentication.md        # Authentication documentation
│   ├── caching.md               # Caching documentation
│   ├── colored_logging.md       # Colored logging documentation
│   ├── function_calling.md      # Function calling documentation
│   ├── function_based_search.md # Function-based web search documentation
│   ├── multi_round_conversations.md # Multi-round conversation documentation
│   └── sp500_optimization.md    # S&P 500 optimization documentation
├── app.py             # Flask application (sync)
├── async_app.py       # FastAPI application (async)
├── run.py             # Application entry point
├── requirements.txt   # Python dependencies
└── .env               # Environment variables
```

## Features

- Support for multiple LLM providers (OpenAI, Anthropic, DeepSeek)
- Colored logging for improved log readability and debugging
- Intelligent model selection based on:
  - Query content analysis (coding, reasoning, creativity, knowledge needs)
  - Performance metrics (latency, reliability)
  - Resource optimization (cost efficiency, load balancing)
  - User preferences (optional override)
- Function calling support:
  - Automatic function detection and execution
  - Custom function registration
  - Streaming function calls
  - Function result integration in conversation history
  - Support for models with automatic function calling (e.g., GPT-4o-turbo)
- Advanced web search capabilities:
  - Multi-provider search (Google, DuckDuckGo)
  - Automatic detection of search needs
  - Context-aware search query enhancement
  - Credibility scoring for search results
  - Content extraction from web pages
- Multi-round conversation support:
  - Round-based conversation tracking
  - Context window management
  - Conversation summarization
  - Conversation branching
  - Conversation analytics
  - Conversation export
- Rate limiting and concurrency control
- Streaming responses
- Metrics tracking for model performance
- Asynchronous processing with FastAPI
- Robust error handling with circuit breakers, retries, and fallbacks
- Response caching layer for improved performance and reduced API costs
- OpenAI Assistant API integration:
  - Persistent threads for maintaining conversation context
  - Thread management (create, delete, list messages)
  - Dedicated `/assistant` endpoint that uses only OpenAI models
  - Both synchronous and asynchronous implementations
  - Responsive streaming with incremental text updates
  - Automatic function calling with event-driven architecture
  - Seamless handling of "requires_action" status
  - Authentication support with enhanced thread persistence:
    - Automatic thread creation and storage in Redis for authenticated users
    - Thread retrieval based on user identity (auth0_sub)
    - Consistent thread management across streaming and non-streaming requests
  - Public endpoints for unauthenticated access
  - Robust error handling for streaming responses

## Recent Fixes

### Assistant API Streaming Fix

Fixed an issue in the OpenAI Assistant API streaming functionality where the code was encountering an error:

```
Error in streaming: 'dict' object has no attribute 'type'
```

The fix adds defensive programming techniques to check for the existence of attributes before trying to access them, improving error handling and making the streaming functionality more robust. See [Assistant Streaming Fix Documentation](docs/assistant_streaming_fix.md) for more details.

## Setup

### Local Development

1. Clone the repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Set up environment variables in `.env`:
   ```
   # Server settings
   PORT=5002
   
   # Redis settings
   REDIS_HOST=localhost
   REDIS_PORT=6379
   REDIS_DB=0
   
   # Database Configuration (for S&P 500 data)
   DB_HOST=localhost
   DB_PORT=3306
   DB_NAME=stock_data
   DB_USER=root
   DB_PASSWORD=
   
   # Cache settings
   ENABLE_CACHE=true
   CACHE_TTL=3600  # 1 hour in seconds
   
   # API keys
   OPENAI_API_KEY=your_openai_api_key
   OPENAI_API_URL=https://api.openai.com/v1
   
   ANTHROPIC_API_KEY=your_anthropic_api_key
   ANTHROPIC_API_URL=your_anthropic_api_url
   
   DEEPSEEK_API_KEY=your_deepseek_api_key
   DEEPSEEK_API_URL=your_deepseek_api_url
   
   # Web search settings (optional)
   GOOGLE_SEARCH_API_KEY=your_google_search_api_key
   GOOGLE_SEARCH_ENGINE_ID=your_google_search_engine_id
   # OR
   SERP_API_KEY=your_serp_api_key
   USE_SERP_API=true
   
   # Multi-round conversation settings
   MAX_CONCURRENT_SEARCHES=5
   DEFAULT_SEARCH_THRESHOLD=0.5
   ```
4. Start Redis server
5. Run the application:
   ```
   # Synchronous mode (Flask)
   python run.py
   
   # Asynchronous mode (FastAPI)
   python run.py --mode async
   
   # With additional options
   python run.py --mode async --port 5002 --host 0.0.0.0
   ```

### Docker Setup

#### Using Docker Compose (Recommended)

The easiest way to run the application is with Docker Compose:

```bash
# Start in sync mode
docker-compose up

# Start in async mode
APP_MODE=async docker-compose up

# Start with custom port
PORT=8000 docker-compose up

# Start with env file
docker-compose --env-file .env up
```

#### Using Docker

You can also build and run the Docker image directly:

```bash
# Build the image
docker build -t go-gpt-backend .

# Run in sync mode
docker run -p 5000:5000 -e PORT=5000 --env-file .env go-gpt-backend

# Run in async mode
docker run -p 5000:5000 -e PORT=5000 -e APP_MODE=async --env-file .env go-gpt-backend
```

#### Production Deployment

For production, it's recommended to:

1. Use an external Redis instance instead of the embedded one
2. Set appropriate environment variables for your production environment
3. Consider using a reverse proxy like Nginx for SSL termination
4. Set up proper logging and monitoring

## API Endpoints

### Health Check
```
GET /
```

### List Models
```
GET /models
```

### Query
```
POST /query
```

Request body:
```json
{
  "query": "Your question here",
  "session_id": "optional_session_id",
  "provider_model": "optional_provider/model",
  "cost_sensitive": false,
  "web_search": true,
  "stream": false,
  "context": "optional_additional_context",
  "new_round": false,
  "include_summary": true,
  "max_tokens": 2000,
  "auto_detect_search": true,
  "enable_functions": true,
  "auto_functions": false,
  "functions": ["optional_function_names"]
}
```

### Conversation Management

#### Start New Round
```
POST /conversation/new-round
```

Request body:
```json
{
  "session_id": "your_session_id"
}
```

#### Get Conversation Stats
```
GET /conversation/stats/{session_id}
```

#### Export Conversation
```
POST /conversation/export
```

Request body:
```json
{
  "session_id": "your_session_id",
  "format": "json"  // Options: json, text, markdown
}
```

#### Create Branch
```
POST /conversation/branch
```

Request body:
```json
{
  "session_id": "your_session_id",
  "branch_name": "alternative_path",
  "from_message_index": null  // Optional index to branch from
}
```

#### Clear History
```
POST /conversation/clear
```

Request body:
```json
{
  "session_id": "your_session_id",
  "round_id": null  // Optional round ID to clear only messages from that round
}
```

### Assistant API

#### Authenticated Endpoints

These endpoints require an `auth0_sub` header to be present in the request.

#### Query Assistant (Authenticated)
```
POST /assistant
```

Request body:
```json
{
  "query": "Your question here",
  "thread_id": "optional_thread_id",
  "model": "optional_model_override"
}
```

Headers:
```
auth0_sub: user_identifier
```

#### Create Thread (Authenticated)
```
POST /assistant/thread
```

Headers:
```
auth0_sub: user_identifier
```

#### Delete Thread (Authenticated)
```
DELETE /assistant/thread/{thread_id}
```

Headers:
```
auth0_sub: user_identifier
```

#### Get Thread Messages (Authenticated)
```
GET /assistant/thread/{thread_id}/messages
```

Headers:
```
auth0_sub: user_identifier
```

#### Public Endpoints

These endpoints do not require authentication.

#### Query Assistant (Public)
```
POST /pub/assistant
```

Request body:
```json
{
  "query": "Your question here",
  "thread_id": "optional_thread_id",
  "model": "optional_model_override"
}
```

#### Create Thread (Public)
```
POST /pub/assistant/thread
```

#### Delete Thread (Public)
```
DELETE /pub/assistant/thread/{thread_id}
```

#### Get Thread Messages (Public)
```
GET /pub/assistant/thread/{thread_id}/messages
```

## Asynchronous Processing

The backend now supports asynchronous processing using FastAPI and the OpenAI async client. This allows for better performance and scalability, especially when handling multiple concurrent requests.

See [Async Processing Documentation](docs/async_processing.md) for more details.

## Response Caching

The backend includes a robust caching layer that caches responses from LLM providers to improve performance and reduce API costs. The caching layer is implemented using Redis and supports both synchronous and asynchronous operations.

See [Caching Documentation](docs/caching.md) for more details.

## Multi-Round Conversation Support

The backend now supports multiple rounds of conversation with advanced features like context window management, round tracking, summarization, and branching. This allows for more complex and context-aware interactions.

Key features include:
- Round-based conversation tracking
- Context window management
- Conversation summarization
- Conversation branching
- Conversation analytics
- Conversation export
- Intelligent web search
- Intelligent model selection

### Relationship Between `/query` and `/conversation/new-round` APIs

The `/query` and `/conversation/new-round` APIs work together to manage conversation rounds:

1. **Independent but Complementary**: The two APIs are independent but work together to manage conversation rounds.

2. **Order of Operations**:
   - You can call either endpoint first, depending on your use case
   - For a new conversation, you can start directly with `/query`
   - For explicitly starting a new topic in an existing conversation, call `/conversation/new-round` first

3. **How They Work Together**:
   - **`/query` Endpoint**: 
     - Primary purpose: Send a query to the model and get a response
     - Has a `new_round` parameter (default: false)
     - If `new_round=true`, it will automatically start a new conversation round
     - If `new_round=false`, it continues in the current round

   - **`/conversation/new-round` Endpoint**:
     - Primary purpose: Explicitly start a new conversation round
     - When to use: When you want to clearly separate topics or contexts
     - Effect: Creates a new round ID and adds a system message indicating the new round

4. **Best Practices**:
   - **Starting a New Conversation**: Simply call `/query` with your first message (round ID 1 is automatically assigned)
   - **Continuing a Conversation**: Keep calling `/query` with `new_round=false` (default)
   - **Changing Topics**: Either call `/conversation/new-round` first, then call `/query`, or call `/query` with `new_round=true`
   - **Managing Context**: Each round maintains its own context; previous rounds can be summarized to maintain continuity

5. **Example Flow**:
   ```
   # Starting a conversation
   POST /query
   {
     "query": "Tell me about machine learning",
     "session_id": "user123"
   }
   → Creates round 1 automatically

   # Continuing in the same topic
   POST /query
   {
     "query": "What about neural networks?",
     "session_id": "user123"
   }
   → Continues in round 1

   # Explicitly starting a new topic
   POST /conversation/new-round
   {
     "session_id": "user123"
   }
   → Creates round 2

   # Query in the new round
   POST /query
   {
     "query": "Let's talk about climate change",
     "session_id": "user123"
   }
   → Uses round 2

   # Alternative: implicitly start a new round
   POST /query
   {
     "query": "Let's talk about climate change",
     "session_id": "user123",
     "new_round": true
   }
   → Creates round 3 and processes the query
   ```

See [Multi-Round Conversation Documentation](docs/multi_round_conversations.md) for more details.

## Intelligent Model Selection

The backend includes a sophisticated model selection system that automatically chooses the optimal model for each query based on multiple factors:

- **Query Content Analysis**: Analyzes the query text for patterns indicating specific needs (coding, reasoning, creativity, knowledge)
- **Performance Metrics**: Considers latency and reliability of each model
- **Resource Optimization**: Balances cost efficiency and load distribution
- **User Preferences**: Respects user-specified model choices when provided

The system continuously tracks performance metrics for each model and updates its selection algorithm accordingly.

See [Model Selection Documentation](docs/model_selection.md) for more details.

## Intelligent Web Search

The backend includes an intelligent web search system with two implementation approaches:

### 1. Automatic Web Search (Legacy)

The original implementation automatically detects when web search is needed and enhances queries with conversation context:

- Automatic detection of search needs
- Context-aware search query enhancement
- Multi-provider search (Google, DuckDuckGo)
- Credibility scoring for search results
- Content extraction from web pages

### 2. Function-Based Web Search (New)

The new implementation leverages function calling to allow models to decide when to search:

- Model-driven search decisions (only searches when the model determines it's necessary)
- Conversation context awareness with enhanced queries
- URL tracking and deduplication across conversation rounds
- Search history tracking for reference in later rounds
- Query refinement capabilities for follow-up searches
- Support for models with automatic function calling (e.g., GPT-4o-turbo)

The function-based approach provides several advantages:
- More efficient use of search APIs (only searching when needed)
- More intelligent search queries formulated by the model
- Better integration of search results into the conversation flow
- Support for follow-up searches and query refinement

See [Function-Based Web Search Documentation](docs/function_based_search.md) for more details.

## Function Calling

The backend now supports function calling, allowing models to call functions defined in the backend code. This enables the model to perform actions like retrieving information, making calculations, or interacting with external systems.

Key features include:
- Automatic function detection and execution
- Custom function registration with a simple decorator
- Streaming function calls with real-time results
- Function result integration in conversation history
- Support for models with automatic function calling (e.g., GPT-4o-turbo)

Function calling is particularly useful for:
- Retrieving real-time information (weather, time, etc.)
- Performing calculations
- Searching for information
- Interacting with external APIs
- Executing system commands

See [Function Calling Documentation](docs/function_calling.md) for more details.

### Optimized S&P 500 Dividend Rates Function

The backend includes an optimized function for retrieving dividend rates for S&P 500 stocks:

- Batch API requests to Yahoo Finance instead of individual requests
- Enhanced multi-level caching strategy with different TTLs for different data types
- Sector-specific caching for faster filtered queries
- Significant performance improvements for repeated queries (>99.99% faster)

See [S&P 500 Optimization Documentation](docs/sp500_optimization.md) for more details on the implementation and performance improvements.

## Development

### Adding a New Provider

1. Create a new provider class in `providers/` that implements the `ModelProvider` interface
2. Add the provider to the registry in `providers/provider_factory.py`
3. Update the provider locks in `core/model_selection.py`
4. (Optional) Create an async version of the provider in `providers/async_*_provider.py`
5. (Optional) Add the async provider to the registry in `providers/async_provider_factory.py`

### Running Tests

```
# Run all tests
pytest tests/

# Run specific test file
pytest tests/test_providers.py

# Run async tests
pytest tests/test_async.py

# Run conversation tests
pytest tests/test_conversation.py

# Run enhanced search tests
pytest tests/test_enhanced_search.py

# Test the assistant streaming fix
python test_assistant_stream_fix.py
```

## Contributing

Contributions are welcome! Here are some areas where you can contribute:

1. Adding support for new LLM providers
2. Improving the web search functionality
3. Enhancing the conversation management system
4. Adding new features to the API
5. Improving documentation
6. Writing tests

Please follow the existing code style and include tests for any new features.
