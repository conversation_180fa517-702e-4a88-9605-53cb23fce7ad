import uuid
import json
from flask import jsonify, Response
from core.conversation import ConversationManager
from core.model_selection import (
    queue_model_request,
    process_streaming_request,
    start_workers
)
from utils.model_utils import select_provider_and_model
from utils.logging import logger
from utils.function_registry import function_registry

class ConversationService:
    """
    Service for handling conversation interactions.
    Supports multiple rounds of conversation with advanced features like
    context window management, round tracking, summarization, and branching.
    """
    
    def __init__(self):
        """
        Initialize the conversation service.
        """
        # Start worker threads
        start_workers()
    
    def handle_query(self, data, auth0_sub=None):
        """
        Handle a query from a user.
        
        Args:
            data: The request data containing the query and options
            auth0_sub: Optional user identifier for authentication
            
        Returns:
            Response: The response to send back to the client
        """
        if not data or 'query' not in data:
            return jsonify({'error': 'Missing query parameter'}), 400
        
        # Get or generate session ID
        session_id = data.get('session_id') or str(uuid.uuid4())
        
        # Get user preference if specified
        user_preference = data.get('provider_model')
        
        # Check if this is a cost-sensitive request
        cost_sensitive = data.get('cost_sensitive', False)
        
        # Check if this is a new conversation round
        new_round = data.get('new_round', False)
        
        # Get max tokens if specified
        max_tokens = data.get('max_tokens')
        
        # Check if summaries should be included
        include_summary = data.get('include_summary', True)
        
        # Check if function calling should be enabled
        enable_functions = data.get('enable_functions', True)
        auto_functions = data.get('auto_functions', False)
        
        # Get specific functions if provided
        function_names = data.get('functions', [])
        functions = None
        if function_names:
            functions = [schema for schema in function_registry.get_function_schemas() 
                        if schema['name'] in function_names]
        elif auto_functions:
            functions = function_registry.get_function_schemas()
        
        # Select provider and model based on user preference
        try:
            provider_name, model_name = select_provider_and_model(
                query=data['query'],
                user_preference=user_preference
            )
        except ValueError as e:
            return jsonify({'error': str(e)}), 400
        
        # Create conversation manager
        conversation = ConversationManager(session_id)
        
        try:
            # Start a new round if requested
            if new_round:
                round_id = conversation.start_new_round()
                logger.info(f"Started new conversation round {round_id} for session {session_id}")
            else:
                round_id = conversation._get_current_round_id()
            
            # Store user message
            conversation.add_user_message(data['query'], round_id=round_id)
            
            # Prepare messages for the model
            # web_search_enabled = data.get('web_search', True)  # Default to enabled (commented out)
            # Web search is now handled via function calling instead of pre-processing
            messages = conversation.prepare_messages(
                data['query'],
                context=data.get('context'),
                web_search=False,  # Disable automatic web search before model API call
                include_summary=include_summary,
                max_tokens=max_tokens
            )
            
            # Check if streaming is requested
            stream = data.get('stream', True)  # Default to streaming
            
            # Prepare additional parameters
            additional_params = {
                'functions': functions,
                'auto_functions': auto_functions,
                'execute_functions': enable_functions
            }
            
            if stream:
                return self._handle_streaming_request(
                    session_id, 
                    messages, 
                    provider_name, 
                    model_name, 
                    conversation,
                    round_id,
                    **additional_params
                )
            else:
                return self._handle_standard_request(
                    session_id, 
                    messages, 
                    provider_name, 
                    model_name, 
                    conversation,
                    round_id,
                    **additional_params
                )
                
        except Exception as e:
            logger.error(f"API error: {str(e)}")
            return jsonify({'error': f'API error: {str(e)}'}), 500
    
    def _handle_streaming_request(self, session_id, messages, provider_name, model_name, conversation, round_id, **kwargs):
        """
        Handle a streaming request.
        
        Args:
            session_id: The conversation session ID
            messages: The prepared messages
            provider_name: The provider to use
            model_name: The model to use
            conversation: The conversation manager
            round_id: The conversation round ID
            **kwargs: Additional parameters including function definitions
            
        Returns:
            Response: A streaming response
        """
        def generate():
            full_response = ""
            function_calls = []
            
            try:
                stream_response = process_streaming_request(
                    messages, 
                    provider_name, 
                    model_name, 
                    session_id,
                    **kwargs
                )
            
                for chunk in stream_response:
                    # Handle function call chunks
                    if 'choices' in chunk and chunk['choices'][0].get('delta', {}).get('function_call'):
                        function_call = chunk['choices'][0]['delta']['function_call']
                        
                        # Track function calls
                        if 'name' in function_call:
                            function_calls.append({
                                'name': function_call['name'],
                                'arguments': ''
                            })
                        
                        # Append arguments
                        if 'arguments' in function_call and function_calls:
                            function_calls[-1]['arguments'] += function_call['arguments']
                        
                        # Send function call info to client
                        yield f"data: {json.dumps({'function_call': function_call, 'session_id': session_id, 'round_id': round_id})}\n\n"
                    
                    # Handle regular content chunks
                    elif 'choices' in chunk and chunk['choices'][0].get('delta', {}).get('content'):
                        content = chunk['choices'][0]['delta']['content']
                        full_response += content
                        yield f"data: {json.dumps({'content': content, 'session_id': session_id, 'round_id': round_id})}\n\n"
                
                # Store complete assistant response
                conversation.add_assistant_message(
                    full_response,
                    provider=provider_name,
                    model=model_name,
                    round_id=round_id,
                    function_calls=function_calls
                )
                
                # Execute functions if needed
                if function_calls and kwargs.get('execute_functions', True):
                    all_function_results = []
                    
                    for func_call in function_calls:
                        try:
                            name = func_call['name']
                            arguments = json.loads(func_call['arguments'])
                            result = function_registry.execute_function(name, arguments)
                            
                            # Send function result to client
                            yield f"data: {json.dumps({'function_result': {'name': name, 'arguments': arguments, 'result': result}, 'session_id': session_id, 'round_id': round_id})}\n\n"
                            
                            # Store function result in conversation
                            conversation.add_function_result(
                                name,
                                arguments,
                                result,
                                round_id=round_id
                            )
                            
                            all_function_results.append({
                                'name': name,
                                'arguments': arguments,
                                'result': result
                            })
                        except Exception as e:
                            logger.error(f"Error executing function {func_call.get('name')}: {str(e)}")
                            yield f"data: {json.dumps({'error': f'Error executing function {func_call.get('name')}: {str(e)}', 'session_id': session_id, 'round_id': round_id})}\n\n"
                    
                    # If we have function results, make a second API call with the updated conversation
                    if all_function_results:
                        try:
                            logger.info(f"Making second API call with function results for session {session_id}")
                            logger.info(f"Function results: {json.dumps(all_function_results)}")
                            yield f"data: {json.dumps({'status': 'Processing function results...', 'session_id': session_id, 'round_id': round_id})}\n\n"
                            
                            # Get updated messages including function results
                            updated_messages = conversation.prepare_messages(
                                data_query=None,  # No new query, just use existing conversation
                                include_summary=kwargs.get('include_summary', True),
                                max_tokens=kwargs.get('max_tokens')
                            )
                            
                            # Make second API call
                            second_response = process_streaming_request(
                                updated_messages, 
                                provider_name, 
                                model_name, 
                                session_id,
                                **kwargs
                            )
                            
                            second_full_response = ""
                            for chunk in second_response:
                                # Handle content chunks
                                if 'choices' in chunk and chunk['choices'][0].get('delta', {}).get('content'):
                                    content = chunk['choices'][0]['delta']['content']
                                    second_full_response += content
                                    yield f"data: {json.dumps({'content': content, 'session_id': session_id, 'round_id': round_id})}\n\n"
                            
                            # If we got a response, update the conversation
                            if second_full_response:
                                # Update the assistant's message with the new response
                                conversation.add_assistant_message(
                                    second_full_response,
                                    provider=provider_name,
                                    model=model_name,
                                    round_id=round_id
                                )
                        except Exception as e:
                            logger.error(f"Error in second API call: {str(e)}")
                            yield f"data: {json.dumps({'error': f'Error processing function results: {str(e)}', 'session_id': session_id, 'round_id': round_id})}\n\n"
            
            except Exception as e:
                yield f"data: {json.dumps({'error': str(e), 'session_id': session_id, 'round_id': round_id})}\n\n"
                logger.error(f"Streaming error: {str(e)}")
        
        return Response(generate(), mimetype='text/event-stream')
    
    def _handle_standard_request(self, session_id, messages, provider_name, model_name, conversation, round_id, **kwargs):
        """
        Handle a standard (non-streaming) request.
        
        Args:
            session_id: The conversation session ID
            messages: The prepared messages
            provider_name: The provider to use
            model_name: The model to use
            conversation: The conversation manager
            round_id: The conversation round ID
            **kwargs: Additional parameters including function definitions
            
        Returns:
            dict: Status information about the queued request
        """
        def handle_response(session_id, response):
            # Check for function call
            function_calls = []
            if 'choices' in response and response['choices'][0]['message'].get('function_call'):
                function_call = response['choices'][0]['message']['function_call']
                function_calls.append({
                    'name': function_call.get('name', ''),
                    'arguments': function_call.get('arguments', '{}')
                })
            
            # Get content (might be empty if there's a function call)
            content = response['choices'][0]['message'].get('content', '')
            
            # Store assistant response
            assistant_response = {
                'content': content,
                'provider': provider_name,
                'model': model_name,
                'round_id': round_id
            }
            
            # Add function calls if present
            if function_calls:
                assistant_response['function_calls'] = function_calls
            
            # Store in conversation history
            conversation.add_assistant_message(
                content,
                provider=provider_name,
                model=model_name,
                round_id=round_id,
                function_calls=function_calls
            )
            
            # Execute functions if needed
            if function_calls and kwargs.get('execute_functions', True):
                function_results = []
                for func_call in function_calls:
                    try:
                        name = func_call['name']
                        arguments = json.loads(func_call['arguments'])
                        result = function_registry.execute_function(name, arguments)
                        
                        # Store function result
                        function_results.append({
                            'name': name,
                            'arguments': arguments,
                            'result': result
                        })
                        
                        # Add to conversation history
                        conversation.add_function_result(
                            name,
                            arguments,
                            result,
                            round_id=round_id
                        )
                    except Exception as e:
                        logger.error(f"Error executing function {func_call.get('name')}: {str(e)}")
                        function_results.append({
                            'name': func_call.get('name', ''),
                            'arguments': func_call.get('arguments', '{}'),
                            'error': str(e)
                        })
                
                # Add function results to response
                if function_results:
                    assistant_response['function_results'] = function_results
                    
                    # Make a second API call with the updated conversation
                    try:
                        logger.info(f"Making second API call with function results for session {session_id}")
                        
                        # Get updated messages including function results
                        updated_messages = conversation.prepare_messages(
                            data_query=None,  # No new query, just use existing conversation
                            include_summary=kwargs.get('include_summary', True),
                            max_tokens=kwargs.get('max_tokens')
                        )
                        
                        # Create a callback for the second API call
                        def handle_second_response(session_id, second_response):
                            if 'choices' in second_response and second_response['choices'][0]['message'].get('content'):
                                second_content = second_response['choices'][0]['message']['content']
                                
                                # Update the assistant's message with the new response
                                conversation.add_assistant_message(
                                    second_content,
                                    provider=provider_name,
                                    model=model_name,
                                    round_id=round_id
                                )
                                
                                # Update the response to include the new content
                                assistant_response['content'] = second_content
                                assistant_response['processed_with_function_results'] = True
                            
                            return assistant_response
                        
                        # Queue the second request
                        return queue_model_request(
                            session_id, 
                            updated_messages, 
                            provider_name, 
                            model_name, 
                            handle_second_response,
                            **kwargs
                        )
                    except Exception as e:
                        logger.error(f"Error in second API call: {str(e)}")
                        assistant_response['error'] = f"Error processing function results: {str(e)}"
            
            # Add session_id to response
            assistant_response['session_id'] = session_id
            
            return jsonify(assistant_response)
        
        # Queue the request
        return queue_model_request(
            session_id, 
            messages, 
            provider_name, 
            model_name, 
            handle_response,
            **kwargs
        )
    
    def list_models(self):
        """
        List available models with their capabilities and metrics.
        
        Returns:
            dict: Information about available models
        """
        from config.constants import MODEL_METRICS, MODEL_CAPABILITIES
        
        models_info = {}
        for model_key in MODEL_CAPABILITIES:
            provider, model = model_key.split('/')
            metrics = MODEL_METRICS[model_key]
            avg_latency = sum(metrics['latency']) / max(len(metrics['latency']), 1) if metrics['latency'] else 0
            
            models_info[model_key] = {
                'capabilities': MODEL_CAPABILITIES[model_key],
                'avg_latency': round(avg_latency, 2),
                'error_rate': metrics['errors'] / 100.0,
                'cost_per_token': metrics['cost_per_token']
            }
        
        return jsonify(models_info)
    
    def start_new_round(self, data):
        """
        Start a new conversation round.
        
        Args:
            data: The request data containing the session ID
            
        Returns:
            dict: Information about the new round
        """
        if not data or 'session_id' not in data:
            return jsonify({'error': 'Missing session_id parameter'}), 400
        
        session_id = data['session_id']
        conversation = ConversationManager(session_id)
        
        try:
            round_id = conversation.start_new_round()
            return jsonify({
                'session_id': session_id,
                'round_id': round_id,
                'message': f'Started new conversation round {round_id}'
            })
        except Exception as e:
            logger.error(f"Error starting new round: {str(e)}")
            return jsonify({'error': f'Error starting new round: {str(e)}'}), 500
    
    def get_conversation_stats(self, session_id):
        """
        Get statistics about a conversation.
        
        Args:
            session_id: The conversation session ID
            
        Returns:
            dict: Conversation statistics
        """
        if not session_id:
            return jsonify({'error': 'Missing session_id parameter'}), 400
        
        conversation = ConversationManager(session_id)
        
        try:
            stats = conversation.get_conversation_stats()
            stats['session_id'] = session_id
            return jsonify(stats)
        except Exception as e:
            logger.error(f"Error getting conversation stats: {str(e)}")
            return jsonify({'error': f'Error getting conversation stats: {str(e)}'}), 500
    
    def export_conversation(self, data):
        """
        Export a conversation.
        
        Args:
            data: The request data containing the session ID and format
            
        Returns:
            dict: The exported conversation
        """
        if not data or 'session_id' not in data:
            return jsonify({'error': 'Missing session_id parameter'}), 400
        
        session_id = data['session_id']
        format = data.get('format', 'json')
        
        conversation = ConversationManager(session_id)
        
        try:
            exported = conversation.export_conversation(format=format)
            return jsonify({
                'session_id': session_id,
                'format': format,
                'content': exported
            })
        except Exception as e:
            logger.error(f"Error exporting conversation: {str(e)}")
            return jsonify({'error': f'Error exporting conversation: {str(e)}'}), 500
    
    def create_branch(self, data):
        """
        Create a new conversation branch.
        
        Args:
            data: The request data containing the session ID and branch name
            
        Returns:
            dict: Information about the new branch
        """
        if not data or 'session_id' not in data or 'branch_name' not in data:
            return jsonify({'error': 'Missing required parameters'}), 400
        
        session_id = data['session_id']
        branch_name = data['branch_name']
        from_message_index = data.get('from_message_index')
        
        conversation = ConversationManager(session_id)
        
        try:
            branch_session_id = conversation.create_branch(branch_name, from_message_index)
            return jsonify({
                'original_session_id': session_id,
                'branch_session_id': branch_session_id,
                'branch_name': branch_name,
                'message': f'Created branch {branch_name} from session {session_id}'
            })
        except Exception as e:
            logger.error(f"Error creating branch: {str(e)}")
            return jsonify({'error': f'Error creating branch: {str(e)}'}), 500
    
    def clear_history(self, data):
        """
        Clear conversation history.
        
        Args:
            data: The request data containing the session ID and optional round ID
            
        Returns:
            dict: Status information
        """
        if not data or 'session_id' not in data:
            return jsonify({'error': 'Missing session_id parameter'}), 400
        
        session_id = data['session_id']
        round_id = data.get('round_id')
        
        conversation = ConversationManager(session_id)
        
        try:
            conversation.clear_history(round_id)
            
            if round_id:
                message = f'Cleared conversation history for round {round_id} in session {session_id}'
            else:
                message = f'Cleared all conversation history for session {session_id}'
                
            return jsonify({
                'session_id': session_id,
                'round_id': round_id,
                'message': message
            })
        except Exception as e:
            logger.error(f"Error clearing history: {str(e)}")
            return jsonify({'error': f'Error clearing history: {str(e)}'}), 500
