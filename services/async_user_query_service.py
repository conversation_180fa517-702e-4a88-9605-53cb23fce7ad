"""
Async service for handling user query operations.
"""
import json
from typing import Dict, Any, List, Optional
from sqlalchemy import select, func, desc, update
from sqlalchemy.orm import joinedload
from investpy_client import check_stock_exists
from utils.logging import logger
from utils.multi_schema_db_manager import async_session_context
from models.user_query import UserQuery
from models.query import Query
from utils.sec_db_functions import query_ticker_concepts_from_db2
from utils.ticker_db_functions import get_yf_symbol_by_ticker

def log_sql_query(query, params=None, query_type="SQL"):
    """Log SQL query for debugging."""
    logger.info(f"Raw {query_type} Query:\n{query}")
    if params:
        logger.info(f"{query_type} Parameters: {params}")

    # Show compiled query
    try:
        compiled_query = query.compile(compile_kwargs={"literal_binds": True})
        logger.info(f"Executing Compiled {query_type}:\n{compiled_query}")
    except Exception as e:
        logger.debug(f"Could not compile query for logging: {e}")

class AsyncUserQueryService:
    """
    Async service for handling user query operations.
    """

    async def delete_user_query(self, id: int, uid: str) -> Dict[str, Any]:
        """
        Delete a user query by ID and UID.

        Args:
            id: User query ID
            uid: User identifier (auth0_sub)

        Returns:
            Dict containing success status and message
        """
        try:
            async with async_session_context('user_data') as session:
                # Find the user query
                query = select(UserQuery).where(
                    (UserQuery.id == id) &
                    (UserQuery.uid == uid)
                )
                result = await session.execute(query)
                user_query = result.scalar_one_or_none()

                # If not found, return error
                if not user_query:
                    return {
                        "success": False,
                        "message": f"User query with ID {id} not found or you don't have permission to delete it"
                    }

                # Delete the user query
                await session.delete(user_query)

                # Return success
                return {
                    "success": True,
                    "message": f"User query with ID {id} deleted successfully"
                }

        except Exception as e:
            logger.error(f"Error deleting user query: {str(e)}")
            return {
                "success": False,
                "message": f"Error deleting user query: {str(e)}"
            }

    async def get_user_queries(self, uid: str, page: int = 1, page_size: int = 10, search: Optional[str] = None) -> Dict[str, Any]:
        """
        Get queries for a specific user with pagination and optional search.

        Args:
            uid: User identifier (auth0_sub)
            page: Page number (starting from 1)
            page_size: Number of items per page
            search: Optional search term to match against user_query (case-insensitive)

        Returns:
            Dict containing queries and pagination info
        """
        try:
            # Calculate offset for pagination
            offset = (page - 1) * page_size

            async with async_session_context('user_data') as session:
                # Build base conditions
                conditions = [UserQuery.uid == uid]

                # Add search condition if provided
                if search:
                    conditions.append(Query.user_query.ilike(f"%{search}%"))

                # Count total number of queries for this user with search filter if provided
                count_query = select(func.count()).select_from(UserQuery)

                if search:
                    count_query = count_query.join(Query, UserQuery.query_id == Query.id)

                count_query = count_query.where(*conditions)
                total_count = await session.scalar(count_query)

                # If no queries found, return empty result
                if not total_count:
                    return {
                        "queries": [],
                        "pagination": {
                            "total_count": 0,
                            "page": page,
                            "page_size": page_size,
                            "total_pages": 0
                        }
                    }

                # Calculate total pages
                total_pages = (total_count + page_size - 1) // page_size

                # Query user queries with pagination and search filter if provided
                query = (
                    select(UserQuery, Query)
                    .join(Query, UserQuery.query_id == Query.id)
                    .where(*conditions)
                    .order_by(desc(UserQuery.query_time))
                    .offset(offset)
                    .limit(page_size)
                )

                result = await session.execute(query)
                rows = result.all()

                # Format the results
                queries = []
                for user_query, query in rows:
                    # Parse resources JSON if it exists
                    resources = query.resources
                    if isinstance(resources, str):
                        try:
                            resources = json.loads(resources)
                        except json.JSONDecodeError:
                            resources = {}

                    # Extract all tickers and only execute query_ticker_concepts_from_db for the first one
                    tickers = []
                    company_fact = []
                    executed_query = False
                    first_company_fact_is_empty = False

                    if resources and isinstance(resources, list):
                        for resource in resources:
                            if isinstance(resource, dict) and resource.get("function_name") == "query_ticker_concepts":
                                arguments = resource.get("arguments")
                                ticker = None
                                market = None

                                # Extract ticker from arguments
                                if arguments:
                                    if isinstance(arguments, str):
                                        try:
                                            args_dict = json.loads(arguments)
                                            ticker = args_dict.get("ticker")
                                            market = args_dict.get("market")
                                        except json.JSONDecodeError:
                                            logger.error(f"Failed to parse arguments JSON: {arguments}")
                                    elif isinstance(arguments, dict):
                                        ticker = arguments.get("ticker")
                                        market = arguments.get("market")

                                # Add ticker to the list if found and not already in the list
                                stock_exists = check_stock_exists(ticker, market)
                                if not stock_exists:
                                    continue
                                yf_symbol =  await get_yf_symbol_by_ticker(ticker, market)
                                if yf_symbol and yf_symbol not in tickers:
                                    tickers.append(yf_symbol)

                                    # Execute query_ticker_concepts_from_db only for the first ticker
                                    if not executed_query:
                                        try:
                                            logger.info(f"Executing query_ticker_concepts_from_db for ticker: {ticker}")
                                            fact_result = await query_ticker_concepts_from_db2(ticker, market)
                                            company_fact.append(fact_result)
                                            if 'data' not in fact_result or not fact_result['data']:
                                                first_company_fact_is_empty = True
                                            executed_query = True
                                        except Exception as e:
                                            logger.error(f"Error executing query_ticker_concepts_from_db: {str(e)}")
                                            # Fallback to stored output if available
                                            output = resource.get("output")
                                            if output:
                                                if isinstance(output, str):
                                                    try:
                                                        fact_result = json.loads(output)
                                                        company_fact.append(fact_result)
                                                    except json.JSONDecodeError:
                                                        # Skip adding to company_fact if JSON is invalid
                                                        pass
                                                else:
                                                    company_fact.append(output)
                                            executed_query = True  # Mark as executed even if it failed

                    # Extract metadata from resources if available
                    metadata = {}
                    if resources and isinstance(resources, list):
                        for resource in resources:
                            if isinstance(resource, dict) and resource.get("function_name") == "provide_response_metadata":
                                arguments = resource.get("arguments")
                                if arguments:
                                    if isinstance(arguments, str):
                                        try:
                                            metadata = json.loads(arguments)
                                        except json.JSONDecodeError:
                                            metadata = {}
                                    else:
                                        metadata = arguments

                    if first_company_fact_is_empty:
                        tickers = []

                    # Create query object - explicitly exclude system_prompt as requested
                    query_obj = {
                        "id": user_query.id,  # Include the user_query ID for use with DELETE endpoint
                        "query": query.user_query,
                        "type": query.type,
                        "timestamp": user_query.query_time.isoformat() if user_query.query_time else None,
                        "answer": query.llm_answer,
                        "metadata": metadata,
                        "company_fact": company_fact,
                        "tickers": tickers,  # Add the tickers array to the response
                        "read": user_query.read
                    }
                    queries.append(query_obj)

                # Return formatted response
                return {
                    "queries": queries,
                    "pagination": {
                        "total_count": total_count,
                        "page": page,
                        "page_size": page_size,
                        "total_pages": total_pages
                    }
                }

        except Exception as e:
            logger.error(f"Error getting user queries: {str(e)}")
            return {
                "error": str(e),
                "queries": [],
                "pagination": {
                    "total_count": 0,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": 0
                }
            }

    async def get_query_market_info(self, user_query_id: int) -> Dict[str, Any]:
        """
        Get market information from a query's resources by UserQuery ID.

        Args:
            user_query_id: The UserQuery ID to get market info from

        Returns:
            Dict containing market information and function_name
        """
        try:
            async with async_session_context('user_data') as session:
                # First, select from UserQuery to get the query_id
                user_query_stmt = select(UserQuery).where(UserQuery.id == user_query_id)

                # Log the SQL query
                log_sql_query(user_query_stmt, {"user_query_id": user_query_id}, "UserQuery SELECT")

                user_query_result = await session.execute(user_query_stmt)
                user_query = user_query_result.scalar_one_or_none()

                if not user_query:
                    return {
                        "success": False,
                        "error": f"UserQuery with ID {user_query_id} not found",
                        "market": None,
                        "function_name": None
                    }

                logger.info(f"Found UserQuery: id={user_query.id}, query_id={user_query.query_id}")

                # Now get the query by query_id from UserQuery
                query_stmt = select(Query).where(Query.id == user_query.query_id)

                # Log the SQL query
                log_sql_query(query_stmt, {"query_id": user_query.query_id}, "Query SELECT")

                result = await session.execute(query_stmt)
                query = result.scalar_one_or_none()

                if not query:
                    return {
                        "success": False,
                        "error": f"Query with ID {user_query.query_id} not found",
                        "market": None,
                        "function_name": None
                    }

                logger.info(f"Found Query: id={query.id}, resources type={type(query.resources)}")

                # Parse resources JSON if it exists
                resources = query.resources
                if isinstance(resources, str):
                    try:
                        resources = json.loads(resources)
                        logger.info(f"Parsed resources from JSON string: {len(resources) if isinstance(resources, list) else 'not a list'}")
                    except json.JSONDecodeError:
                        resources = {}
                        logger.warning("Failed to parse resources JSON, using empty dict")
                else:
                    logger.info(f"Resources is already parsed: {len(resources) if isinstance(resources, list) else 'not a list'}")

                logger.info(f"Resources content: {json.dumps(resources, indent=2) if resources else 'None'}")

                # Look for query_ticker_concepts function calls
                market = None
                function_name = None

                if resources and isinstance(resources, list):
                    for i, resource in enumerate(resources):
                        logger.info(f"Processing resource {i}: function_name={resource.get('function_name') if isinstance(resource, dict) else 'not a dict'}")
                        if isinstance(resource, dict) and resource.get("function_name") == "query_ticker_concepts":
                            function_name = "query_ticker_concepts"
                            arguments = resource.get("arguments")
                            logger.info(f"Found query_ticker_concepts, arguments type: {type(arguments)}")

                            # Extract market from arguments
                            if arguments:
                                if isinstance(arguments, str):
                                    try:
                                        args_dict = json.loads(arguments)
                                        market = args_dict.get("market")
                                        logger.info(f"Extracted market from JSON string: {market}")
                                    except json.JSONDecodeError:
                                        logger.error(f"Failed to parse arguments JSON: {arguments}")
                                elif isinstance(arguments, dict):
                                    market = arguments.get("market")
                                    logger.info(f"Extracted market from dict: {market}")

                            # Use the first query_ticker_concepts function found
                            break

                logger.info(f"Final result: market={market}, function_name={function_name}")

                return {
                    "success": True,
                    "market": market,
                    "function_name": function_name
                }

        except Exception as e:
            logger.error(f"Error getting query market info: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "market": None,
                "function_name": None
            }

    async def get_ticker_facts(self, ticker: str, user_query_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Get financial facts for a specific ticker.

        Args:
            ticker: The stock ticker symbol (e.g., 'AAPL', 'MSFT')
            user_query_id: Optional user query ID to get market information from

        Returns:
            Dict containing financial facts for the specified ticker
        """
        try:
            market = None

            # If user_query_id is provided, get market information from the query
            if user_query_id:
                query_info = await self.get_query_market_info(user_query_id)
                if query_info.get("success"):
                    market = query_info.get("market")
                    logger.info(f"Using market '{market}' from user_query_id {user_query_id}")
                else:
                    logger.warning(f"Could not get market info from user_query_id {user_query_id}: {query_info.get('error')}")

            # Call query_ticker_concepts_from_db with market parameter
            result = await query_ticker_concepts_from_db2(ticker, market)

            # Return formatted response
            return {
                "success": True,
                "ticker": ticker,
                "company_fact": result
            }

        except Exception as e:
            logger.error(f"Error getting ticker facts: {str(e)}")
            return {
                "success": False,
                "ticker": ticker,
                "error": str(e),
                "company_fact": None
            }

    async def update_read_status(self, id: int, uid: str) -> Dict[str, Any]:
        """
        Update the read status of a user query from 0 to 1.

        Args:
            id: User query ID
            uid: User identifier (auth0_sub)

        Returns:
            Dict containing success status and message
        """
        try:
            async with async_session_context('user_data') as session:
                # Find the user query
                query = select(UserQuery).where(
                    (UserQuery.id == id) &
                    (UserQuery.uid == uid)
                )
                result = await session.execute(query)
                user_query = result.scalar_one_or_none()

                # If not found, return error
                if not user_query:
                    return {
                        "success": False,
                        "message": f"User query with ID {id} not found or you don't have permission to update it"
                    }

                # If already read, return success without updating
                if user_query.read:
                    return {
                        "success": True,
                        "message": f"User query with ID {id} is already marked as read"
                    }

                # Update the read status
                user_query.read = True
                await session.commit()

                # Return success
                return {
                    "success": True,
                    "message": f"User query with ID {id} marked as read successfully"
                }

        except Exception as e:
            logger.error(f"Error updating read status: {str(e)}")
            return {
                "success": False,
                "message": f"Error updating read status: {str(e)}"
            }
