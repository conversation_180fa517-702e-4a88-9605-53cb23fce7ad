"""
Async service for managing shared queries.
"""
import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy import select, and_, func

from models.shared_query import SharedQuery
from models.query import Query
from models.user_query import UserQuery
from utils.multi_schema_db_manager import async_session_context
from utils.logging import logger

class AsyncSharedQueryService:
    """Service for managing shared queries."""
    
    def __init__(self):
        # Set schema for models
        SharedQuery.set_schema('user_data')
        Query.set_schema('user_data')
        UserQuery.set_schema('user_data')
    
    async def create_share(
        self,
        query_id: int,
        user_id: str,
        title: Optional[str] = None,
        description: Optional[str] = None,
        expires_in_days: Optional[int] = None
    ) -> Dict[str, Any]:
        """Legacy method for single query sharing."""
        return await self.create_multi_share(
            query_ids=[query_id],
            user_id=user_id,
            title=title,
            description=description,
            expires_in_days=expires_in_days
        )

    async def create_multi_share(
        self,
        query_ids: List[int],
        user_id: str,
        title: Optional[str] = None,
        description: Optional[str] = None,
        expires_in_days: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Create a new share for multiple queries.

        Args:
            query_ids: List of query IDs to share
            user_id: User ID (auth0_sub) of the query owner
            title: Optional custom title
            description: Optional description
            expires_in_days: Optional expiration in days

        Returns:
            Dict with success status and share data
        """
        try:
            async with async_session_context('user_data') as session:
                # Verify user owns all queries
                query_result = await session.execute(
                    select(Query)
                    .join(UserQuery)
                    .where(
                        and_(
                            Query.id.in_(query_ids),
                            UserQuery.uid == user_id
                        )
                    )
                )
                found_queries = query_result.scalars().all()
                found_query_ids = {q.id for q in found_queries}

                # Check if all requested queries were found
                missing_query_ids = set(query_ids) - found_query_ids
                if missing_query_ids:
                    return {
                        "success": False,
                        "error": f"Queries not found or access denied: {list(missing_query_ids)}"
                    }

                # Check if there's already an existing share for this exact set of queries
                # For simplicity, we'll check if any of the queries are already shared
                # and if so, return the existing share_id
                existing_shares = await session.execute(
                    select(SharedQuery).where(
                        and_(
                            SharedQuery.query_id.in_(query_ids),
                            SharedQuery.user_id == user_id,
                            SharedQuery.is_active == True
                        )
                    )
                )
                existing_list = existing_shares.scalars().all()

                # Group by share_id to find complete matches
                share_groups = {}
                for share in existing_list:
                    if share.share_id not in share_groups:
                        share_groups[share.share_id] = []
                    share_groups[share.share_id].append(share.query_id)

                # Check if any existing share contains exactly the same query_ids
                for share_id, existing_query_ids in share_groups.items():
                    if set(existing_query_ids) == set(query_ids):
                        # Found exact match
                        sample_share = next(s for s in existing_list if s.share_id == share_id)
                        return {
                            "success": True,
                            "share_id": share_id,
                            "existing": True,
                            "expires_at": sample_share.expires_at.isoformat() if sample_share.expires_at else None
                        }

                # Calculate expiration
                expires_at = None
                if expires_in_days:
                    expires_at = datetime.datetime.now() + datetime.timedelta(days=expires_in_days)

                # Generate a single share_id for all queries
                share_id = SharedQuery.generate_share_id()

                # Create share records for each query with the same share_id
                shared_queries = []
                for query_id in query_ids:
                    shared_query = SharedQuery(
                        share_id=share_id,
                        query_id=query_id,
                        user_id=user_id,
                        shared_by=user_id,
                        title=title,
                        description=description,
                        expires_at=expires_at
                    )
                    shared_queries.append(shared_query)
                    session.add(shared_query)

                await session.commit()

                return {
                    "success": True,
                    "share_id": share_id,
                    "share_url": f"https://addxgo.io/ai/{share_id}",
                    "expires_at": expires_at.isoformat() if expires_at else None,
                    "existing": False,
                    "query_count": len(query_ids)
                }

        except Exception as e:
            logger.error(f"Error creating multi-share: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_shared_query(self, share_id: str) -> Dict[str, Any]:
        """
        Get shared queries by share_id for public access.
        Now supports multiple queries with the same share_id.

        Args:
            share_id: The UUID share identifier

        Returns:
            Dict with query data or error
        """
        try:
            async with async_session_context('user_data') as session:
                # Get all shared queries with related query data for this share_id
                result = await session.execute(
                    select(SharedQuery, Query)
                    .join(Query, SharedQuery.query_id == Query.id)
                    .where(SharedQuery.share_id == share_id)
                    .order_by(Query.created_at.asc())  # Order by creation time
                )
                rows = result.all()

                if not rows:
                    return {
                        "success": False,
                        "error": "Shared query not found"
                    }

                # Check if accessible (use first record for share metadata)
                first_shared_query = rows[0][0]
                if not first_shared_query.is_accessible():
                    return {
                        "success": False,
                        "error": "Shared query is no longer accessible"
                    }

                # Increment view count for all shared queries with this share_id
                for shared_query, _ in rows:
                    shared_query.increment_view_count()
                await session.commit()

                # Prepare queries data
                queries = []
                for shared_query, query in rows:
                    query_data = {
                        "id": query.id,
                        "user_query": query.user_query,
                        "llm_answer": query.llm_answer,
                        "model": query.model,
                        "created_at": query.created_at.isoformat() if query.created_at else None,
                        # Filter resources to remove sensitive data
                        "resources": self._sanitize_resources(query.resources) if query.resources else []
                    }
                    queries.append(query_data)

                # Return sanitized data for public consumption
                return {
                    "success": True,
                    "data": {
                        "share_info": first_shared_query.to_public_dict(),
                        "queries": queries,
                        "query_count": len(queries)
                    }
                }

        except Exception as e:
            logger.error(f"Error getting shared query: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_user_shares(self, user_id: str, page: int = 1, page_size: int = 10) -> Dict[str, Any]:
        """Get all shares created by a user."""
        try:
            async with async_session_context('user_data') as session:
                offset = (page - 1) * page_size
                
                # Get total count
                count_result = await session.execute(
                    select(func.count(SharedQuery.id))
                    .where(SharedQuery.user_id == user_id)
                )
                total_count = count_result.scalar()
                
                # Get shares with query data
                result = await session.execute(
                    select(SharedQuery, Query)
                    .join(Query, SharedQuery.query_id == Query.id)
                    .where(SharedQuery.user_id == user_id)
                    .order_by(SharedQuery.created_at.desc())
                    .offset(offset)
                    .limit(page_size)
                )
                
                shares = []
                for shared_query, query in result:
                    share_data = shared_query.to_dict()
                    share_data["query_preview"] = query.user_query[:100] + "..." if len(query.user_query) > 100 else query.user_query
                    share_data["share_url"] = f"https://addxgo.io/ai/{shared_query.share_id}"
                    shares.append(share_data)
                
                return {
                    "success": True,
                    "shares": shares,
                    "pagination": {
                        "total_count": total_count,
                        "page": page,
                        "page_size": page_size,
                        "total_pages": (total_count + page_size - 1) // page_size
                    }
                }
                
        except Exception as e:
            logger.error(f"Error getting user shares: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def revoke_share(self, share_id: str, user_id: str) -> Dict[str, Any]:
        """Revoke/deactivate a share."""
        try:
            async with async_session_context('user_data') as session:
                result = await session.execute(
                    select(SharedQuery).where(
                        and_(
                            SharedQuery.share_id == share_id,
                            SharedQuery.user_id == user_id
                        )
                    )
                )
                shared_query = result.scalar_one_or_none()
                
                if not shared_query:
                    return {
                        "success": False,
                        "error": "Share not found or access denied"
                    }
                
                shared_query.is_active = False
                await session.commit()
                
                return {
                    "success": True,
                    "message": "Share revoked successfully"
                }
                
        except Exception as e:
            logger.error(f"Error revoking share: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _sanitize_resources(self, resources: List[Dict]) -> List[Dict]:
        """Remove sensitive information from resources for public sharing."""
        sanitized = []
        for resource in resources:
            # Only include safe resource types and remove sensitive data
            if resource.get("function_name") in ["web_search", "get_stock_price", "get_company_info"]:
                sanitized_resource = {
                    "function_name": resource.get("function_name"),
                    "timestamp": resource.get("timestamp"),
                    # Include only non-sensitive parts of output
                    "summary": "External data source used"
                }
                sanitized.append(sanitized_resource)
        return sanitized
