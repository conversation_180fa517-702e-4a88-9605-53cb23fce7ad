"""
Asynchronous service for handling earnings calendar data.
"""
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from models.earnings_calendar import EarningsCalendarEvent
from models.all_ticker import AllTicker
from utils.logging import logger
from utils.multi_schema_db_manager import async_session_context
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import select, and_, func, or_

class AsyncEarningsCalendarService:
    """Service for handling earnings calendar data."""

    async def get_earnings_calendar(
        self,
        days: int = 7,
        company: Optional[str] = None,
        time_filter: Optional[str] = None,
        page: int = 1,
        page_size: int = 100,
    ) -> Dict[str, Any]:
        """
        Get earnings calendar events for the specified time period.

        Args:
            days: Number of days to look ahead
            company: Filter by company name (supports partial matching across company, company_name, and ticker fields)
            time_filter: Filter by time ('upcoming', 'past', or None for all)
            page: Page number for pagination (1-based)
            page_size: Number of items per page

        Returns:
            Dictionary containing earnings calendar events and pagination info
        """
        try:
            # Input validation
            if page < 1:
                page = 1
            if page_size < 1:
                page_size = 20
            if page_size > 100:  # Prevent excessive page sizes
                page_size = 100

            async with async_session_context('stock_data') as session:
                today = datetime.now().date()
                today_start = today.strftime('%Y-%m-%d %H:%M:%S')
                prev_date = today - timedelta(days=days)
                next_date = today + timedelta(days=days)
                prev_date_start = prev_date.strftime('%Y-%m-%d %H:%M:%S')
                next_date_end = next_date.strftime('%Y-%m-%d %H:%M:%S')

                # Base query for data
                base_query = select(
                    EarningsCalendarEvent,
                    AllTicker.logo
                ).outerjoin(
                    AllTicker,
                    EarningsCalendarEvent.ticker == AllTicker.ticker
                )

                # Base query for count
                count_query = select(func.count(EarningsCalendarEvent.id))

                # Apply time filters
                if time_filter == 'upcoming':
                    date_filter = and_(
                        EarningsCalendarEvent.release_date >= today_start,
                        EarningsCalendarEvent.release_date < next_date_end,
                    )
                    base_query = base_query.where(date_filter).order_by(EarningsCalendarEvent.release_date)
                    count_query = count_query.where(date_filter)

                elif time_filter == 'past':
                    date_filter = and_(
                        EarningsCalendarEvent.release_date >= prev_date_start,
                        EarningsCalendarEvent.release_date < today_start,
                    )
                    base_query = base_query.where(date_filter).order_by(EarningsCalendarEvent.release_date.desc())
                    count_query = count_query.where(date_filter)

                else:
                    # No time filter - get all events, ordered by release date
                    base_query = base_query.order_by(EarningsCalendarEvent.release_date.desc())

                # Apply company filter if provided (supports partial matching)
                if company:
                    # Create case-insensitive LIKE pattern
                    search_pattern = f"%{company.strip()}%"

                    # Search across company, company_name, and ticker fields
                    company_filter = or_(
                        EarningsCalendarEvent.company.ilike(search_pattern),
                        EarningsCalendarEvent.company_name.ilike(search_pattern),
                    )
                    base_query = base_query.filter(company_filter)
                    count_query = count_query.filter(company_filter)

                # Get total count
                count_result = await session.execute(count_query)
                total_count = count_result.scalar()

                # Calculate pagination
                offset = (page - 1) * page_size
                total_pages = (total_count + page_size - 1) // page_size  # Ceiling division

                # Apply pagination to data query
                paginated_query = base_query.offset(offset).limit(page_size)

                # Print real SQL for debugging
                from sqlalchemy.dialects import postgresql
                sql_statement = paginated_query.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True})
                logger.info(f"Executing SQL: {sql_statement}")

                # Execute the query to get events
                result = await session.execute(paginated_query)
                result_rows = result.fetchall()

                # Format event results
                event_data = []
                for row in result_rows:
                    event = row[0]  # EarningsCalendarEvent object
                    logo = row[1]   # Logo from AllTicker

                    event_info = {
                        "id": event.id,
                        "company": event.company,
                        "company_name": event.company_name,
                        "ticker": event.ticker,
                        "logo": logo if logo else '',
                        "eps_forecast": event.eps_forecast,
                        "revenue_forecast": event.revenue_forecast,
                        "eps_actual": event.eps_actual,
                        "revenue_actual": event.revenue_actual,
                        "market_cap": event.market_cap,
                        "release_date": event.release_date.strftime('%Y-%m-%d'),
                        "release_time": event.release_time,
                        "time_filter": time_filter,
                    }
                    event_data.append(event_info)

                return {
                    'events': event_data,
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total_count': total_count,
                        'total_pages': total_pages,
                        'has_next': page < total_pages,
                        'has_previous': page > 1,
                        'offset': offset,
                    },
                }
        except ImportError as e:
            logger.error(f"Import error in get_earnings_calendar: {str(e)}")
            return {'error': f"Import error: {str(e)}"}
        except SQLAlchemyError as e:
            logger.error(f"Database error in get_earnings_calendar: {str(e)}")
            return {'error': f"Database error: {str(e)}"}
        except Exception as e:
            logger.error(f"Error in get_earnings_calendar: {str(e)}")
            return {'error': f"Error: {str(e)}"}