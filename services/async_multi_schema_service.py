"""
Async multi-schema service.

This module provides a service for managing multiple database schemas.
It provides methods for creating, updating, and querying schemas, as well as
executing cross-schema queries.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from sqlalchemy.sql import text

from models.schema_registry import SchemaRegistry
from utils.multi_schema_db_manager import db_manager
from utils.logging import logger


class AsyncMultiSchemaService:
    """
    Async multi-schema service.
    
    This class provides methods for managing multiple database schemas.
    It provides a higher-level API on top of the multi-schema database manager.
    """
    
    async def create_schema(self, schema_name: str, description: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a new schema and register it in the schema registry.
        
        Args:
            schema_name: Name of the schema to create
            description: Optional description of the schema
            
        Returns:
            Dict[str, Any]: Schema details
        """
        logger.info(f"Creating schema: {schema_name}")
        
        # Create the schema in the database
        created = await db_manager.create_schema_if_not_exists(schema_name)
        
        # Register the schema in the schema registry
        async with await db_manager.get_db_session() as session:
            # Check if the schema is already registered
            result = await session.execute(
                text("SELECT * FROM schema_registry WHERE schema_name = :schema_name"),
                {"schema_name": schema_name}
            )
            schema_exists = result.fetchone() is not None
            
            if not schema_exists:
                # Register the schema
                await session.execute(
                    text("""
                    INSERT INTO schema_registry (schema_name, description, is_active)
                    VALUES (:schema_name, :description, TRUE)
                    """),
                    {"schema_name": schema_name, "description": description}
                )
                await session.commit()
                logger.info(f"Registered schema in registry: {schema_name}")
            else:
                logger.info(f"Schema already registered: {schema_name}")
        
        # Get the schema details
        schema = await self.get_schema(schema_name)
        
        return schema
    
    async def get_schema(self, schema_name: str) -> Optional[Dict[str, Any]]:
        """
        Get a schema from the schema registry.
        
        Args:
            schema_name: Name of the schema to get
            
        Returns:
            Optional[Dict[str, Any]]: Schema details, or None if not found
        """
        logger.info(f"Getting schema: {schema_name}")
        
        # Get the schema from the registry
        async with await db_manager.get_db_session() as session:
            result = await session.execute(
                text("SELECT * FROM schema_registry WHERE schema_name = :schema_name"),
                {"schema_name": schema_name}
            )
            row = result.fetchone()
            
            if row is None:
                logger.warning(f"Schema not found: {schema_name}")
                return None
            
            # Convert to dictionary
            schema = {
                "id": row[0],
                "schema_name": row[1],
                "description": row[2],
                "is_active": bool(row[3]),
                "created_at": row[4].isoformat() if row[4] else None,
                "updated_at": row[5].isoformat() if row[5] else None
            }
            
            return schema
    
    async def get_schemas(self) -> List[Dict[str, Any]]:
        """
        Get all schemas from the schema registry.
        
        Returns:
            List[Dict[str, Any]]: List of schema details
        """
        logger.info("Getting all schemas")
        
        # Get all schemas from the registry
        async with await db_manager.get_db_session() as session:
            result = await session.execute(text("SELECT * FROM schema_registry"))
            rows = result.fetchall()
            
            # Convert to list of dictionaries
            schemas = []
            for row in rows:
                schema = {
                    "id": row[0],
                    "schema_name": row[1],
                    "description": row[2],
                    "is_active": bool(row[3]),
                    "created_at": row[4].isoformat() if row[4] else None,
                    "updated_at": row[5].isoformat() if row[5] else None
                }
                schemas.append(schema)
            
            return schemas
    
    async def update_schema(self, schema_name: str, description: Optional[str] = None, is_active: Optional[bool] = None) -> Optional[Dict[str, Any]]:
        """
        Update a schema in the schema registry.
        
        Args:
            schema_name: Name of the schema to update
            description: Optional new description of the schema
            is_active: Optional new active status of the schema
            
        Returns:
            Optional[Dict[str, Any]]: Updated schema details, or None if not found
        """
        logger.info(f"Updating schema: {schema_name}")
        
        # Get the schema from the registry
        schema = await self.get_schema(schema_name)
        
        if schema is None:
            logger.warning(f"Schema not found: {schema_name}")
            return None
        
        # Update the schema
        async with await db_manager.get_db_session() as session:
            # Build the update query
            update_parts = []
            params = {"schema_name": schema_name}
            
            if description is not None:
                update_parts.append("description = :description")
                params["description"] = description
            
            if is_active is not None:
                update_parts.append("is_active = :is_active")
                params["is_active"] = is_active
            
            if not update_parts:
                logger.info(f"No updates to apply for schema: {schema_name}")
                return schema
            
            # Execute the update query
            await session.execute(
                text(f"""
                UPDATE schema_registry
                SET {", ".join(update_parts)}
                WHERE schema_name = :schema_name
                """),
                params
            )
            await session.commit()
            logger.info(f"Updated schema: {schema_name}")
        
        # Get the updated schema
        updated_schema = await self.get_schema(schema_name)
        
        return updated_schema
    
    async def activate_schema(self, schema_name: str) -> bool:
        """
        Activate a schema.
        
        Args:
            schema_name: Name of the schema to activate
            
        Returns:
            bool: True if the schema was activated, False if not found
        """
        logger.info(f"Activating schema: {schema_name}")
        
        # Update the schema
        updated_schema = await self.update_schema(schema_name, is_active=True)
        
        return updated_schema is not None
    
    async def deactivate_schema(self, schema_name: str) -> bool:
        """
        Deactivate a schema.
        
        Args:
            schema_name: Name of the schema to deactivate
            
        Returns:
            bool: True if the schema was deactivated, False if not found
        """
        logger.info(f"Deactivating schema: {schema_name}")
        
        # Update the schema
        updated_schema = await self.update_schema(schema_name, is_active=False)
        
        return updated_schema is not None
    
    async def delete_schema(self, schema_name: str) -> bool:
        """
        Delete a schema from the schema registry.
        
        Note: This does not delete the schema from the database, it only
        removes it from the registry.
        
        Args:
            schema_name: Name of the schema to delete
            
        Returns:
            bool: True if the schema was deleted, False if not found
        """
        logger.info(f"Deleting schema from registry: {schema_name}")
        
        # Get the schema from the registry
        schema = await self.get_schema(schema_name)
        
        if schema is None:
            logger.warning(f"Schema not found: {schema_name}")
            return False
        
        # Delete the schema from the registry
        async with await db_manager.get_db_session() as session:
            await session.execute(
                text("DELETE FROM schema_registry WHERE schema_name = :schema_name"),
                {"schema_name": schema_name}
            )
            await session.commit()
            logger.info(f"Deleted schema from registry: {schema_name}")
        
        return True
    
    async def execute_cross_schema_query(self, schema_name: str, query: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """
        Execute a cross-schema query.
        
        Args:
            schema_name: Name of the schema to execute the query on
            query: SQL query to execute
            params: Optional query parameters
            
        Returns:
            Any: Query result
        """
        logger.info(f"Executing cross-schema query on schema '{schema_name}': {query}")
        
        # Execute the query
        result = await db_manager.execute_query(query, params, schema_name)
        
        return result


# Create a singleton instance of the service
multi_schema_service = AsyncMultiSchemaService()
