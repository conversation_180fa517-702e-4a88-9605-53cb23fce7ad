"""
Async service for financial calendar data.
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy import select, and_, or_, desc, func, case
from utils.logging import logger
from utils.multi_schema_db_manager import async_session_context
from sqlalchemy.exc import SQLAlchemyError
from models.financial_calendar import FinancialCalendarEvent

class AsyncFinancialCalendarService:
    """
    Async service for financial calendar data.
    """
    
    # Class constants for region-based filtering
    EUROPEAN_COUNTRIES = ["United Kingdom", "Germany", "France"]
    ASIAN_COUNTRIES = ["China", "Hong Kong", "South Korea", "Japan", "Singapore"]
    
    def __init__(self):
        """
        Initialize the financial calendar service.
        """
        logger.info("Initializing AsyncFinancialCalendarService")
    
    async def get_economic_calendar(
        self,
        days: int = 7,
        country: Optional[str] = None,
        event_type: Optional[str] = None,
        impact: Optional[str] = None,
        time_filter: Optional[str] = None,
        event_count: Optional[int] = 7,
        page: int = 1,
        page_size: int = 100
    ) -> Dict[str, Any]:
        """
        Get economic calendar events for the next N days and high impact events from previous 4 days.
        
        Args:
            days: Number of days to look ahead (default: 7)
            country: Filter by country
            event_type: Filter by event type
            impact: Filter by impact level (High, Medium, Low)
            time_filter: Filter by time ('upcoming' or 'past')
            page: Page number for pagination
            page_size: Number of items per page

        Returns:
            Dict containing economic calendar events and pagination info
        """
        try:
            async with async_session_context('stock_data') as session:
                # Calculate date ranges
                today = datetime.now().date()
                today_start = today.strftime('%Y-%m-%d %H:%M:%S')
                prev_date = today - timedelta(days=days)
                next_date = today + timedelta(days=days)
                prev_date_start = prev_date.strftime('%Y-%m-%d %H:%M:%S')
                next_date_end = next_date.strftime('%Y-%m-%d %H:%M:%S')
                
                if time_filter == 'upcoming':
                    # Create query for high and medium impact upcoming events
                    query = select(FinancialCalendarEvent).where(
                        and_(
                            FinancialCalendarEvent.date_time >= today_start,
                            FinancialCalendarEvent.date_time < next_date_end,
                            or_(
                                FinancialCalendarEvent.impact == 'High',
                                FinancialCalendarEvent.impact == 'Medium'
                            )
                        )
                    ).order_by(FinancialCalendarEvent.impact)
                    
                    # Apply additional filters if provided
                    if country:
                        query = query.filter(FinancialCalendarEvent.country == country)
                    if event_type:
                        query = query.filter(FinancialCalendarEvent.event_type == event_type)
                    if impact:
                        query = query.filter(FinancialCalendarEvent.impact == impact)
                    
                    query = query.offset(0).limit(page_size)
                    
                    # Print real SQL for debugging
                    from sqlalchemy.dialects import postgresql
                    sql_statement = query.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True})
                    logger.info(f"Executing SQL: {sql_statement}")
                        
                    # Execute the query to get all potential events
                    result = await session.execute(query)
                    all_events = result.scalars().all()
                    
                    # Process the results as per requirements
                    high_impact_events = sorted(
                        [event for event in all_events if event.impact.lower() == 'high'],
                        key=lambda x: (x.date_time, x.id)
                    )
                    medium_impact_events = sorted(
                        [event for event in all_events if event.impact.lower() == 'medium'],
                        key=lambda x: (x.date_time, x.id)
                    )
                    
                    # Decide which events to include
                    if len(high_impact_events) >= event_count:
                        # If we have more than event_count events total, return all high impact events
                        events = high_impact_events
                    else:
                        # Otherwise, return all high impact events plus enough medium impact events
                        # to get to at least event_count (or all available if fewer than event_count total)
                        needed_medium = max(0, event_count - len(high_impact_events))
                        events = high_impact_events + medium_impact_events[:needed_medium]
                elif time_filter == 'past':
                    # Create query for high and medium impact upcoming events
                    query = select(FinancialCalendarEvent).where(
                        and_(
                            FinancialCalendarEvent.date_time >= prev_date_start,
                            FinancialCalendarEvent.date_time < today_start,
                            or_(
                                FinancialCalendarEvent.impact == 'High',
                                FinancialCalendarEvent.impact == 'Medium'
                            )
                        )
                    ).order_by(FinancialCalendarEvent.impact)
                    
                    # Apply additional filters if provided
                    if country:
                        query = query.filter(FinancialCalendarEvent.country == country)
                    if event_type:
                        query = query.filter(FinancialCalendarEvent.event_type == event_type)
                    if impact:
                        query = query.filter(FinancialCalendarEvent.impact == impact)
                    
                    query = query.offset(0).limit(page_size)
                    
                    # Print real SQL for debugging
                    from sqlalchemy.dialects import postgresql
                    sql_statement = query.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True})
                    logger.info(f"Executing SQL: {sql_statement}")
                        
                    # Execute the query to get all potential events
                    result = await session.execute(query)
                    all_events = result.scalars().all()
                    
                    # Process the results as per requirements
                    high_impact_events = sorted(
                        [event for event in all_events if event.impact.lower() == 'high'],
                        key=lambda x: (-x.date_time.timestamp(), x.id)  # Date desc, ID asc
                    )
                    medium_impact_events = sorted(
                        [event for event in all_events if event.impact.lower() == 'medium'],
                        key=lambda x: (-x.date_time.timestamp(), x.id)  # Date desc, ID asc
                    )
                    
                    # Decide which events to include
                    if len(high_impact_events) >= event_count:
                        # If we have more than event_count events total, return all high impact events
                        events = high_impact_events
                    else:
                        # Otherwise, return all high impact events plus enough medium impact events
                        # to get to at least event_count (or all available if fewer than event_count total)
                        needed_medium = max(0, event_count - len(high_impact_events))
                        events = high_impact_events + medium_impact_events[:needed_medium]
                
                # Format event results
                event_data = []
                for event in events:
                    event_info = {
                        'id': event.id,
                        'event_name': event.event_name,
                        'event_type': event.event_type,
                        'country': event.country,
                        'date': event.date.isoformat() if event.date else None,
                        'time': event.time,
                        'date_time': event.date_time.isoformat() if event.date_time else None,
                        'actual': event.actual,
                        'previous': event.previous,
                        'forecast': event.forecast,
                        'impact': event.impact,
                        'description': event.description,
                        'is_released': event.is_released
                    }
                    event_data.append(event_info)
                
                return {
                    'events': event_data,
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                    },
                }
        except ImportError as e:
            logger.error(f"Import error in get_economic_calendar: {str(e)}")
            return {'error': f"Import error: {str(e)}"}
        except SQLAlchemyError as e:
            logger.error(f"Database error in get_economic_calendar: {str(e)}")
            return {'error': f"Database error: {str(e)}"}
        except Exception as e:
            logger.error(f"Error in get_economic_calendar: {str(e)}")
            return {'error': f"Error: {str(e)}"}
    
    async def get_event_details(self, event_id: int) -> Dict[str, Any]:
        """
        Get detailed information about a specific economic calendar event.
        
        Args:
            event_id: ID of the event
            
        Returns:
            Dict containing event details
        """
        try:
            async with async_session_context('stock_data') as session:
                # Get event
                event_query = select(FinancialCalendarEvent).where(FinancialCalendarEvent.id == event_id)
                result = await session.execute(event_query)
                event = result.scalar_one_or_none()
                
                if not event:
                    return {'error': f"Event with ID {event_id} not found"}
                
                # Format event details
                event_details = {
                    'id': event.id,
                    'event_name': event.event_name,
                    'event_type': event.event_type,
                    'country': event.country,
                    'date': event.date.isoformat() if event.date else None,
                    'time': event.time,
                    'date_time': event.date_time.isoformat() if event.date_time else None,
                    'actual': event.actual,
                    'previous': event.previous,
                    'forecast': event.forecast,
                    'impact': event.impact,
                    'description': event.description,
                    'is_released': event.is_released,
                    'last_updated': event.last_updated.isoformat() if event.last_updated else None
                }
                
                return {
                    'event': event_details
                }
        except ImportError as e:
            logger.error(f"Import error in get_event_details: {str(e)}")
            return {'error': f"Import error: {str(e)}"}
        except SQLAlchemyError as e:
            logger.error(f"Database error in get_event_details: {str(e)}")
            return {'error': f"Database error: {str(e)}"}
        except Exception as e:
            logger.error(f"Error in get_event_details: {str(e)}")
            return {'error': f"Error: {str(e)}"}
