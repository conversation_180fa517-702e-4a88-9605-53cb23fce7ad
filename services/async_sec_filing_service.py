"""
Async service for SEC filings data.
"""
import asyncio
import json
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy import select, and_, or_, desc, func
from utils.logging import logger
from utils.multi_schema_db_manager import async_session_context
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import text
# Add import for compiling SQL queries
from sqlalchemy.dialects import mysql
# Import Redis client for caching
from utils.async_redis_client import async_redis

class AsyncSECFilingService:
    """
    Async service for SEC filings data.
    """
    
    def __init__(self):
        """
        Initialize the SEC filing service.
        """
        logger.info("Initializing AsyncSECFilingService")
    
    async def get_filings(
        self,
        ticker: Optional[str] = None,
        filing_type: Optional[str] = None,
        fiscal_year: Optional[int] = None,
        fiscal_quarter: Optional[int] = None,
        page: int = 1,
        page_size: int = 20,
        ticker_search: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get SEC filings based on the provided parameters.
        
        Args:
            ticker: Stock ticker symbol
            filing_type: Type of filing (e.g., '10-K', '10-Q', '8-K')
            fiscal_year: Fiscal year of the filing
            fiscal_quarter: Fiscal quarter of the filing
            page: Page number for pagination
            page_size: Number of items per page
            ticker_search: Search string to filter tickers (contains search)
            
        Returns:
            Dict containing filings data and pagination info
        """
        try:
            # Import here to avoid circular imports
            from models.sec_filing import SECFiling, SECFilingSection, Stock
            
            async with async_session_context('stock_data') as session:
                # Build the query
                query = select(SECFiling)
                
                # Apply filters if provided
                filters = []
                if ticker:
                    filters.append(SECFiling.ticker == ticker)
                if filing_type:
                    filters.append(SECFiling.filing_type == filing_type)
                if fiscal_year:
                    filters.append(SECFiling.fiscal_year == fiscal_year)
                if fiscal_quarter:
                    filters.append(SECFiling.fiscal_quarter == fiscal_quarter)
                
                # Add filters to query if any
                if filters:
                    query = query.where(and_(*filters))
                
                # Order by filing date (most recent first)
                query = query.order_by(desc(SECFiling.filing_date))
                
                # Get total count for pagination
                count_query = select(func.count()).select_from(query.subquery())
                result = await session.execute(count_query)
                total_count = result.scalar()
                
                # Apply pagination
                offset = (page - 1) * page_size
                query = query.offset(offset).limit(page_size)
                
                # Execute query
                result = await session.execute(query)
                filings = result.scalars().all()
                
                # Calculate pagination info
                total_pages = (total_count + page_size - 1) // page_size
                has_next = page < total_pages
                has_prev = page > 1
                
                # If no parameters are provided or only ticker_search is provided, return ticker information in page format
                if not any([ticker, filing_type, fiscal_year, fiscal_quarter]):
                    # Generate a cache key based on the parameters
                    cache_params = {
                        'ticker': ticker,
                        'filing_type': filing_type,
                        'fiscal_year': fiscal_year,
                        'fiscal_quarter': fiscal_quarter,
                        'page': page,
                        'page_size': page_size,
                        'ticker_search': ticker_search
                    }
                    cache_key = f"sec_filings:{hashlib.md5(json.dumps(cache_params, sort_keys=True).encode()).hexdigest()}"
                    
                    # Try to get from cache first
                    try:
                        cached_data = await async_redis.get(cache_key)
                        if cached_data:
                            logger.info(f"Cache hit for SEC filings query: {cache_key}")
                            return json.loads(cached_data)
                    except Exception as e:
                        logger.warning(f"Error accessing cache: {str(e)}")
                    
                    # Get unique tickers with filing counts and company names in a single query
                    # Use collation-based JOIN to handle different collations between tables
                    ticker_query = select(
                        SECFiling.ticker,
                        func.count(SECFiling.id).label('filing_count'),
                        Stock.name.label('company_name')
                    ).outerjoin(
                        Stock, SECFiling.ticker.collate('utf8mb4_general_ci') == Stock.ticker.collate('utf8mb4_general_ci')
                    )
                    
                    # Apply ticker search filter if provided
                    if ticker_search:
                        # Search in both ticker symbol and company name
                        ticker_query = ticker_query.where(
                            or_(
                                SECFiling.ticker.like(f'%{ticker_search}%'),
                                Stock.name.like(f'%{ticker_search}%')
                            )
                        )
                    
                    ticker_query = ticker_query.group_by(
                        SECFiling.ticker, Stock.name
                    ).order_by(SECFiling.ticker)
                    
                    # Apply pagination to ticker query
                    ticker_query = ticker_query.offset(offset).limit(page_size)
                    
                    # Log the SQL query if ticker_search is provided
                    if ticker_search:
                        compiled_query = ticker_query.compile(
                            dialect=mysql.dialect(),
                            compile_kwargs={"literal_binds": True}
                        )
                        logger.info(f"Ticker and company name search SQL query: {compiled_query}")
                    
                    # Execute ticker query
                    result = await session.execute(ticker_query)
                    ticker_results = result.all()
                    
                    # Get total count of unique tickers
                    ticker_count_query = select(func.count(func.distinct(SECFiling.ticker)))
                    
                    # Apply ticker search filter to count query if provided
                    if ticker_search:
                        # Also apply the same OR condition to the count query
                        ticker_count_query = ticker_count_query.select_from(
                            SECFiling.__table__.outerjoin(
                                Stock.__table__,
                                SECFiling.ticker.collate('utf8mb4_general_ci') == Stock.ticker.collate('utf8mb4_general_ci')
                            )
                        ).where(
                            or_(
                                SECFiling.ticker.like(f'%{ticker_search}%'),
                                Stock.name.like(f'%{ticker_search}%')
                            )
                        )
                        # Log the count SQL query as well
                        compiled_count_query = ticker_count_query.compile(
                            dialect=mysql.dialect(),
                            compile_kwargs={"literal_binds": True}
                        )
                        logger.info(f"Ticker and company name count SQL query: {compiled_count_query}")
                    
                    result = await session.execute(ticker_count_query)
                    ticker_total_count = result.scalar()
                    
                    # Calculate pagination info for tickers
                    ticker_total_pages = (ticker_total_count + page_size - 1) // page_size
                    ticker_has_next = page < ticker_total_pages
                    ticker_has_prev = page > 1
                    
                    # Format ticker results
                    ticker_data = []
                    for ticker_result in ticker_results:
                        ticker_info = {
                            'ticker': ticker_result.ticker,
                            'filing_count': ticker_result.filing_count,
                            'company_name': ticker_result.company_name  # This will be None if no company name is found
                        }
                        
                        ticker_data.append(ticker_info)
                    
                    result_dict = {
                        'tickers': ticker_data,
                        'pagination': {
                            'page': page,
                            'page_size': page_size,
                            'total_count': ticker_total_count,
                            'total_pages': ticker_total_pages,
                            'has_next': ticker_has_next,
                            'has_prev': ticker_has_prev
                        }
                    }
                    
                    # Cache the result for 1 hour (3600 seconds)
                    try:
                        await async_redis.setex(cache_key, 3600, json.dumps(result_dict))
                        logger.info(f"Cached SEC filings query result: {cache_key}")
                    except Exception as e:
                        logger.warning(f"Error caching result: {str(e)}")
                    
                    return result_dict
                
                # Special case: If filing_type is 8-K, return only filing dates
                if filing_type == '8-K':
                    # If ticker is also provided, return all filing dates for this ticker and filing type
                    if ticker:
                        filing_dates = []
                        for filing in filings:
                            filing_dates.append(filing.filing_date.isoformat() if filing.filing_date else None)
                        
                        return {
                            'ticker': ticker,
                            'filing_type': filing_type,
                            'filing_dates': filing_dates,
                            'pagination': {
                                'page': page,
                                'page_size': page_size,
                                'total_count': total_count,
                                'total_pages': total_pages,
                                'has_next': has_next,
                                'has_prev': has_prev
                            }
                        }
                    # If only filing_type is 8-K (no ticker), return filing date only for each filing
                    else:
                        filing_data = []
                        for filing in filings:
                            filing_info = {
                                'id': filing.id,
                                'ticker': filing.ticker,
                                'filing_date': filing.filing_date.isoformat() if filing.filing_date else None
                            }
                            
                            # Get company name if available, using collation to avoid conflicts
                            company_query = select(Stock.name).where(
                                Stock.ticker.collate('utf8mb4_general_ci') == filing.ticker.collate('utf8mb4_general_ci')
                            )
                            result = await session.execute(company_query)
                            company_name = result.scalar()
                            if company_name:
                                filing_info['company_name'] = company_name
                            
                            filing_data.append(filing_info)
                        
                        return {
                            'filings': filing_data,
                            'pagination': {
                                'page': page,
                                'page_size': page_size,
                                'total_count': total_count,
                                'total_pages': total_pages,
                                'has_next': has_next,
                                'has_prev': has_prev
                            }
                        }
                
                # Special case: If filing_type is 10-Q and ticker is provided, return fiscal quarter and year combinations
                if filing_type == '10-Q' and ticker:
                    # Get unique combinations of fiscal quarter and year
                    fiscal_periods = []
                    seen_combinations = set()
                    
                    for filing in filings:
                        if filing.fiscal_year and filing.fiscal_quarter:
                            # Create a formatted string with Q prefix for quarter
                            period_str = f"Q{filing.fiscal_quarter} {filing.fiscal_year}"
                            
                            # Only add unique combinations
                            if period_str not in seen_combinations:
                                fiscal_periods.append(period_str)
                                seen_combinations.add(period_str)
                    
                    return {
                        'ticker': ticker,
                        'filing_type': filing_type,
                        'fiscal_periods': fiscal_periods,
                        'pagination': {
                            'page': page,
                            'page_size': page_size,
                            'total_count': total_count,
                            'total_pages': total_pages,
                            'has_next': has_next,
                            'has_prev': has_prev
                        }
                    }
                
                # Special case: If filing_type is 10-K and ticker is provided, return only fiscal years
                if filing_type == '10-K' and ticker:
                    # Get unique fiscal years
                    fiscal_years = []
                    seen_years = set()
                    
                    for filing in filings:
                        if filing.fiscal_year:
                            # Only add unique fiscal years
                            if filing.fiscal_year not in seen_years:
                                fiscal_years.append(filing.fiscal_year)
                                seen_years.add(filing.fiscal_year)
                    
                    # Sort fiscal years in descending order (most recent first)
                    fiscal_years.sort(reverse=True)
                    
                    return {
                        'ticker': ticker,
                        'filing_type': filing_type,
                        'fiscal_years': fiscal_years,
                        'pagination': {
                            'page': page,
                            'page_size': page_size,
                            'total_count': total_count,
                            'total_pages': total_pages,
                            'has_next': has_next,
                            'has_prev': has_prev
                        }
                    }

                # Special case: If only ticker is provided (no filing_type), return all unique filing types
                if ticker and not any([filing_type, fiscal_year, fiscal_quarter]):
                    # Generate a cache key based on the parameters
                    cache_params = {
                        'ticker': ticker,
                        'filing_types_only': True,
                        'page': page,
                        'page_size': page_size
                    }
                    cache_key = f"sec_filings_types:{hashlib.md5(json.dumps(cache_params, sort_keys=True).encode()).hexdigest()}"

                    # Try to get from cache first
                    try:
                        cached_data = await async_redis.get(cache_key)
                        if cached_data:
                            logger.info(f"Cache hit for SEC filing types query: {cache_key}")
                            return json.loads(cached_data)
                    except Exception as e:
                        logger.warning(f"Error accessing cache: {str(e)}")

                    # Query to get all unique filing types for this ticker
                    filing_types_query = select(
                        func.distinct(SECFiling.filing_type)
                    ).where(
                        SECFiling.ticker == ticker
                    ).order_by(
                        SECFiling.filing_type
                    )

                    # Execute query
                    result = await session.execute(filing_types_query)
                    filing_types = [row[0] for row in result.all()]

                    # Prepare response
                    response = {
                        'ticker': ticker,
                        'filing_types': filing_types
                    }

                    try:
                        await async_redis.setex(cache_key, 3600, json.dumps(response))
                        logger.info(f"Cached SEC filing types query result: {cache_key}")
                    except Exception as e:
                        logger.warning(f"Error caching result: {str(e)}")

                    return response

                # Standard case: Format filing results with all fields
                # Generate a cache key based on the parameters
                cache_params = {
                    'ticker': ticker,
                    'filing_type': filing_type,
                    'fiscal_year': fiscal_year,
                    'fiscal_quarter': fiscal_quarter,
                    'page': page,
                    'page_size': page_size,
                    'ticker_search': ticker_search
                }
                cache_key = f"sec_filings:{hashlib.md5(json.dumps(cache_params, sort_keys=True).encode()).hexdigest()}"
                
                # Try to get from cache first
                try:
                    cached_data = await async_redis.get(cache_key)
                    if cached_data:
                        logger.info(f"Cache hit for SEC filings query: {cache_key}")
                        return json.loads(cached_data)
                except Exception as e:
                    logger.warning(f"Error accessing cache: {str(e)}")
                
                filing_data = []
                for filing in filings:
                    filing_info = {
                        'id': filing.id,
                        'ticker': filing.ticker,
                        'cik': filing.cik,
                        'accession_number': filing.accession_number,
                        'filing_type': filing.filing_type,
                        'filing_date': filing.filing_date.isoformat() if filing.filing_date else None,
                        'report_date': filing.report_date.isoformat() if filing.report_date else None,
                        'fiscal_year': filing.fiscal_year,
                        'fiscal_quarter': filing.fiscal_quarter,
                        'url': filing.url
                    }
                    
                    # Get company name if available, using collation to avoid conflicts
                    company_query = select(Stock.name).where(
                        Stock.ticker.collate('utf8mb4_general_ci') == filing.ticker.collate('utf8mb4_general_ci')
                    )
                    result = await session.execute(company_query)
                    company_name = result.scalar()
                    if company_name:
                        filing_info['company_name'] = company_name
                    
                    filing_data.append(filing_info)
                
                result_dict = {
                    'filings': filing_data,
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total_count': total_count,
                        'total_pages': total_pages,
                        'has_next': has_next,
                        'has_prev': has_prev
                    }
                }
                
                # Cache the result for 1 hour (3600 seconds)
                try:
                    await async_redis.setex(cache_key, 3600, json.dumps(result_dict))
                    logger.info(f"Cached SEC filings query result: {cache_key}")
                except Exception as e:
                    logger.warning(f"Error caching result: {str(e)}")
                
                return result_dict
        except ImportError as e:
            logger.error(f"Import error in get_filings: {str(e)}")
            return {'error': f"Import error: {str(e)}"}
        except SQLAlchemyError as e:
            logger.error(f"Database error in get_filings: {str(e)}")
            return {'error': f"Database error: {str(e)}"}
        except Exception as e:
            logger.error(f"Error in get_filings: {str(e)}")
            return {'error': f"Error: {str(e)}"}
    
    async def get_filing_details(self, filing_id: int) -> Dict[str, Any]:
        """
        Get detailed information about a specific SEC filing.
        
        Args:
            filing_id: ID of the filing
            
        Returns:
            Dict containing filing details and sections
        """
        try:
            # Import here to avoid circular imports
            from models.sec_filing import SECFiling, SECFilingSection, Stock
            
            async with async_session_context('stock_data') as session:
                # Get filing
                filing_query = select(SECFiling).where(SECFiling.id == filing_id)
                result = await session.execute(filing_query)
                filing = result.scalar_one_or_none()
                
                if not filing:
                    return {'error': f"Filing with ID {filing_id} not found"}
                
                # Get filing sections
                sections_query = select(SECFilingSection).where(SECFilingSection.filing_id == filing_id)
                result = await session.execute(sections_query)
                sections = result.scalars().all()
                
                # Get company name if available, using collation to avoid conflicts
                company_query = select(Stock.name).where(
                    Stock.ticker.collate('utf8mb4_general_ci') == filing.ticker.collate('utf8mb4_general_ci')
                )
                result = await session.execute(company_query)
                company_name = result.scalar()
                
                # Format filing details
                filing_details = {
                    'id': filing.id,
                    'ticker': filing.ticker,
                    'cik': filing.cik,
                    'accession_number': filing.accession_number,
                    'filing_type': filing.filing_type,
                    'filing_date': filing.filing_date.isoformat() if filing.filing_date else None,
                    'report_date': filing.report_date.isoformat() if filing.report_date else None,
                    'fiscal_year': filing.fiscal_year,
                    'fiscal_quarter': filing.fiscal_quarter,
                    'url': filing.url
                }
                
                if company_name:
                    filing_details['company_name'] = company_name
                
                # Format sections
                section_data = []
                for section in sections:
                    section_info = {
                        'id': section.id,
                        'section_name': section.section_name,
                        'section_text': section.section_text
                    }
                    section_data.append(section_info)
                
                return {
                    'filing': filing_details,
                    'sections': section_data
                }
        except ImportError as e:
            logger.error(f"Import error in get_filing_details: {str(e)}")
            return {'error': f"Import error: {str(e)}"}
        except SQLAlchemyError as e:
            logger.error(f"Database error in get_filing_details: {str(e)}")
            return {'error': f"Database error: {str(e)}"}
        except Exception as e:
            logger.error(f"Error in get_filing_details: {str(e)}")
            return {'error': f"Error: {str(e)}"}
