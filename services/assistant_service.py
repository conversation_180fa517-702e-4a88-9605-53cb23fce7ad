"""
Service for handling OpenAI Assistant API interactions.
"""
from typing import Dict, Any, Optional, List
from openai import OpenAI
from utils.logging import logger
from utils.assistant_utils import initialize_assistant
from utils.redis_client import get_redis_client
from providers.openai_assistant_provider import OpenAIAssistantProvider

class AssistantService:
    """
    Service for handling OpenAI Assistant API interactions.
    """
    def __init__(self):
        self.provider = OpenAIAssistantProvider()
        self.client = self.provider.client
        
    def _get_or_create_thread(self, thread_id: Optional[str], auth0_sub: Optional[str]) -> str:
        """
        Get an existing thread ID from Redis or create a new one.
        
        Args:
            thread_id: Optional thread ID provided by the user
            auth0_sub: Optional user identifier for thread persistence
            
        Returns:
            The thread ID to use
        """
        # If thread_id is already provided, use it
        if thread_id:
            return thread_id
            
        # If auth0_sub is provided but thread_id is not, try to get thread_id from Redis
        if auth0_sub:
            redis_client = get_redis_client()
            stored_thread_id = redis_client.get(f"thread:{auth0_sub}")
            if stored_thread_id:
                thread_id = stored_thread_id.decode('utf-8')
                logger.info(f"Retrieved thread ID {thread_id} for user {auth0_sub} from Redis")
                return thread_id
                
            # If no thread_id in Redis, create a new one and store it
            thread = self.create_thread()
            if thread.get('created') and thread.get('thread_id'):
                new_thread_id = thread.get('thread_id')
                redis_client.set(f"thread:{auth0_sub}", new_thread_id)
                logger.info(f"Created and stored new thread ID {new_thread_id} for user {auth0_sub} in Redis")
                return new_thread_id
        
        # If no auth0_sub or Redis retrieval failed, create a thread without storing it
        thread = self.create_thread()
        if thread.get('created') and thread.get('thread_id'):
            return thread.get('thread_id')
            
        # If all else fails, return None and let the provider handle it
        return None
    
    def query(self, 
              user_query: str, 
              thread_id: Optional[str] = None, 
              model: str = "gpt-4-turbo-preview",
              auth0_sub: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a user query using the OpenAI Assistant API.
        
        Args:
            user_query: The user's query text
            thread_id: Optional thread ID for continuing a conversation
            model: The model to use (default: gpt-4-turbo-preview)
            auth0_sub: Optional user identifier for thread persistence
            
        Returns:
            Dict containing the response and metadata
        """
        try:
            # Get or create thread ID
            thread_id = self._get_or_create_thread(thread_id, auth0_sub)
            
            # Format the message for the provider
            messages = [{"role": "user", "content": user_query}]
            
            # Call the provider
            response = self.provider.chat_completion(
                messages=messages,
                model=model,
                thread_id=thread_id
            )
            
            # Extract thread_id and run_id for the client
            thread_id = response.get("thread_id")
            run_id = response.get("run_id")
            
            # If a new thread was created (different from the one we started with) and auth0_sub is provided, store in Redis
            if auth0_sub and thread_id and thread_id != thread_id:
                redis_client = get_redis_client()
                redis_client.set(f"thread:{auth0_sub}", thread_id)
                logger.info(f"Updated thread ID {thread_id} for user {auth0_sub} in Redis")
            
            # Extract the assistant's message
            assistant_message = response["choices"][0]["message"]["content"]
            
            # Return the formatted response
            return {
                "response": assistant_message,
                "thread_id": thread_id,
                "run_id": run_id,
                "model": model
            }
            
        except Exception as e:
            logger.error(f"Error in assistant service: {str(e)}")
            return {
                "response": f"Error: {str(e)}",
                "error": True
            }
    
    def create_thread(self) -> Dict[str, Any]:
        """
        Create a new conversation thread.
        
        Returns:
            Dict containing the thread ID
        """
        try:
            thread = self.client.beta.threads.create()
            return {
                "thread_id": thread.id,
                "created": True
            }
        except Exception as e:
            logger.error(f"Error creating thread: {str(e)}")
            return {
                "error": str(e),
                "created": False
            }
    
    def delete_thread(self, thread_id: str) -> Dict[str, Any]:
        """
        Delete a conversation thread.
        
        Args:
            thread_id: The thread ID to delete
            
        Returns:
            Dict containing the result
        """
        try:
            self.client.beta.threads.delete(thread_id)
            return {
                "deleted": True,
                "thread_id": thread_id
            }
        except Exception as e:
            logger.error(f"Error deleting thread {thread_id}: {str(e)}")
            return {
                "error": str(e),
                "deleted": False,
                "thread_id": thread_id
            }
    
    def get_thread_messages(self, thread_id: str) -> Dict[str, Any]:
        """
        Get all messages in a thread.
        
        Args:
            thread_id: The thread ID
            
        Returns:
            Dict containing the messages
        """
        try:
            messages = self.client.beta.threads.messages.list(thread_id=thread_id)
            
            # Format messages for the response
            formatted_messages = []
            for msg in messages.data:
                content = msg.content[0].text.value if msg.content else ""
                formatted_messages.append({
                    "id": msg.id,
                    "role": msg.role,
                    "content": content,
                    "created_at": msg.created_at
                })
            
            return {
                "messages": formatted_messages,
                "thread_id": thread_id
            }
        except Exception as e:
            logger.error(f"Error getting messages for thread {thread_id}: {str(e)}")
            return {
                "error": str(e),
                "thread_id": thread_id
            }
            
    def cancel_run(self, thread_id: str, run_id: str) -> Dict[str, Any]:
        """
        Cancel an in-progress run.
        
        Args:
            thread_id: The thread ID
            run_id: The run ID to cancel
            
        Returns:
            Dict containing the result
        """
        try:
            result = self.client.beta.threads.runs.cancel(
                thread_id=thread_id,
                run_id=run_id
            )
            return {
                "cancelled": True,
                "thread_id": thread_id,
                "run_id": run_id,
                "status": result.status
            }
        except Exception as e:
            logger.error(f"Error cancelling run {run_id} in thread {thread_id}: {str(e)}")
            return {
                "error": str(e),
                "cancelled": False,
                "thread_id": thread_id,
                "run_id": run_id
            }
            
    def cancel_active_run(self, auth0_sub: Optional[str] = None) -> Dict[str, Any]:
        """
        Cancel the most recent active run for the user.
        
        Args:
            auth0_sub: User identifier for thread retrieval
            
        Returns:
            Dict containing the result
        """
        try:
            logger.info(f"Starting cancel_active_run for user {auth0_sub}")
            
            # Get thread_id from Redis using auth0_sub
            if not auth0_sub:
                logger.warning("No auth0_sub provided for cancel_active_run")
                return {
                    "error": "User identifier (auth0_sub) is required",
                    "cancelled": False
                }
                
            redis_client = get_redis_client()
            thread_id = redis_client.get(f"thread:{auth0_sub}")
            
            if not thread_id:
                logger.warning(f"No thread found in Redis for user {auth0_sub}")
                return {
                    "error": "No active thread found for this user",
                    "cancelled": False
                }
                
            thread_id = thread_id.decode('utf-8')
            logger.info(f"Found thread_id {thread_id} for user {auth0_sub}")
            
            # List all runs for this thread
            runs = self.client.beta.threads.runs.list(thread_id=thread_id)
            logger.info(f"Found {len(runs.data)} runs for thread {thread_id}")
            
            # Find the most recent active run (in "queued", "in_progress", or "requires_action" status)
            active_run = None
            for run in runs.data:
                logger.debug(f"Examining run {run.id} with status {run.status}")
                if run.status in ["queued", "in_progress", "requires_action"]:
                    if active_run is None or run.created_at > active_run.created_at:
                        active_run = run
                        logger.debug(f"Found newer active run: {run.id} with status {run.status}")
            
            if not active_run:
                logger.warning(f"No active runs found for thread {thread_id}")
                return {
                    "error": "No active runs found for this thread",
                    "cancelled": False,
                    "thread_id": thread_id
                }
            
            logger.info(f"Selected active run {active_run.id} with status {active_run.status} for cancellation")
            
            # Handle requires_action state differently - cannot directly cancel
            if active_run.status == "requires_action" and hasattr(active_run, 'required_action') and hasattr(active_run.required_action, 'submit_tool_outputs'):
                logger.info(f"Run {active_run.id} is in requires_action state, attempting to submit error outputs")
                # Get required actions
                required_actions = active_run.required_action.submit_tool_outputs.tool_calls
                logger.info(f"Run requires {len(required_actions)} tool outputs")
                
                # Submit error responses for each required action
                import json
                error_outputs = [
                    {"tool_call_id": action.id, "output": json.dumps({"error": "Operation cancelled by user"})}
                    for action in required_actions
                ]
                
                # Submit the error outputs to fail the run
                logger.info(f"Submitting error outputs for run {active_run.id}")
                try:
                    result = self.client.beta.threads.runs.submit_tool_outputs(
                        thread_id=thread_id,
                        run_id=active_run.id,
                        tool_outputs=error_outputs
                    )
                    logger.info(f"Successfully submitted error outputs, run status: {result.status}")
                    
                    # Return success without calling cancel
                    return {
                        "cancelled": True,
                        "thread_id": thread_id,
                        "run_id": active_run.id,
                        "status": result.status,
                        "original_status": "requires_action",
                        "cancellation_method": "error_submission"
                    }
                except Exception as tool_error:
                    # If submission fails (possibly because run state changed), fall back to cancel API
                    logger.warning(f"Error submitting tool outputs: {str(tool_error)}, falling back to cancel API")
                    # Continue to the cancel code below instead of returning error
            
            # For other states (queued, in_progress), use the cancel endpoint
            logger.info(f"Cancelling run {active_run.id} in thread {thread_id}")
            result = self.client.beta.threads.runs.cancel(
                thread_id=thread_id,
                run_id=active_run.id
            )
            
            logger.info(f"Run {active_run.id} cancellation result: status={result.status}")
            return {
                "cancelled": True,
                "thread_id": thread_id,
                "run_id": active_run.id,
                "status": result.status,
                "original_status": active_run.status,
                "cancellation_method": "cancel_endpoint"
            }
            
        except Exception as e:
            logger.error(f"Error cancelling active run: {str(e)}", exc_info=True)
            return {
                "error": str(e),
                "cancelled": False
            }
