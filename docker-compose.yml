version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - APP_MODE=async
      - REDIS_HOST=host.docker.internal
      - REDIS_PORT=6379
      - REDIS_DB=0
      - ENABLE_CACHE=true
      - CACHE_TTL=3600
      # Add your API keys here or use .env file
      # - OPENAI_API_KEY=your_openai_api_key
      # - ANTHROPIC_API_KEY=your_anthropic_api_key
      # - DEEPSEEK_API_KEY=your_deepseek_api_key
    volumes:
      - .:/app
    # depends_on:
    #   - redis
    # command: python run.py --mode async --host 0.0.0.0 --port ${PORT}
    # restart: unless-stopped

  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis-data:/data
  #   restart: unless-stopped

# volumes:
#   redis-data:

# Usage:
# Start in sync mode: docker-compose up
# Start in async mode: APP_MODE=async docker-compose up
# Start with custom port: PORT=8000 docker-compose up
# Start with env file: docker-compose --env-file .env up
