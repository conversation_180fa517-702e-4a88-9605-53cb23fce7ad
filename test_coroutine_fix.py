"""
Test script to verify the fix for handling coroutines in the async_openai_assistant_provider.py file.
"""
import asyncio
import json
from providers.async_openai_assistant_provider import AsyncOpenAIAssistantProvider

# Mock an async function that returns a coroutine
async def mock_async_function(**kwargs):
    """Mock async function that returns a dictionary after a short delay."""
    await asyncio.sleep(0.1)  # Simulate some async work
    return {"result": "This is an async result", "args": kwargs}

# Mock the on_tool_calls method to test our fix
async def test_on_tool_calls():
    """Test that the on_tool_calls method can handle coroutines."""
    print("Testing coroutine handling in on_tool_calls...")
    
    # Create a mock tool call
    class MockToolCall:
        def __init__(self, function_name, arguments):
            self.id = "test_tool_call_id"
            self.function = type('obj', (object,), {
                'name': function_name,
                'arguments': json.dumps(arguments)
            })
    
    # Create a mock function map with our async function
    function_map = {
        "test_function": mock_async_function
    }
    
    # Create a mock tool calls list
    tool_calls = [MockToolCall("test_function", {"param1": "value1", "param2": "value2"})]
    
    # Define a simplified version of the on_tool_calls method with our fix
    async def on_tool_calls(tool_calls):
        tool_outputs = []
        
        for tool_call in tool_calls:
            function_name = tool_call.function.name
            function_args = json.loads(tool_call.function.arguments)
            
            print(f"Executing function: {function_name} with args: {function_args}")
            
            if function_name in function_map:
                try:
                    # Call the function
                    result = function_map[function_name](**function_args)
                    
                    # Check if the result is a coroutine (from an async function)
                    if asyncio.iscoroutine(result):
                        print(f"Function {function_name} returned a coroutine, awaiting it")
                        # Await the coroutine to get the actual result
                        result = await result
                    
                    tool_outputs.append({
                        "tool_call_id": tool_call.id,
                        "output": json.dumps(result)
                    })
                    print(f"Function {function_name} executed successfully")
                except Exception as e:
                    error_message = f"Error executing function {function_name}: {str(e)}"
                    print(error_message)
                    tool_outputs.append({
                        "tool_call_id": tool_call.id,
                        "output": json.dumps({"error": error_message})
                    })
            else:
                error_message = f"Function {function_name} not found"
                print(error_message)
                tool_outputs.append({
                    "tool_call_id": tool_call.id,
                    "output": json.dumps({"error": error_message})
                })
        
        return tool_outputs
    
    # Call the method and get the result
    result = await on_tool_calls(tool_calls)
    
    # Print the result
    print("\nResult:")
    for output in result:
        print(f"Tool call ID: {output['tool_call_id']}")
        print(f"Output: {output['output']}")
    
    # Verify the result
    output_json = json.loads(result[0]['output'])
    assert output_json['result'] == "This is an async result", "Failed to get the correct result from the async function"
    assert output_json['args'] == {"param1": "value1", "param2": "value2"}, "Failed to pass the correct arguments to the async function"
    
    print("\nTest passed! The fix for handling coroutines works correctly.")

# Run the test
if __name__ == "__main__":
    asyncio.run(test_on_tool_calls())
