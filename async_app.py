import os
import json
import uuid
import time
import traceback
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, BackgroundTasks, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
import uvicorn

from utils.assistant_utils import EARNINGS_ASSISTANT_VECTOR_STORE_KEY, create_vector_store_if_not_exist
from utils.async_redis_client import async_redis
from utils.logging import logger, setup_logging
from utils.async_redis_client import init_async_redis_client
from core.async_conversation import AsyncConversationManager
from providers.async_provider_factory import get_async_provider
from api.async_cache_routes import router as cache_router
from api.async_assistant_routes import router as assistant_router
from api.async_sec_filing_routes import router as sec_filing_router
from api.async_financial_calendar_routes import router as financial_calendar_router
from api.async_earnings_calendar_routes import router as earnings_calendar_router
from api.async_user_query_routes import router as user_query_router
from api.async_shared_query_routes import router as shared_query_router
# Import sample functions to register them
from config.settings import settings

# Initialize logging
setup_logging()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan context manager for the FastAPI application."""
    # Run database migrations
    try:
        from db.migrations.db_migrations import run_migrations
        from config.settings import get_database_url
        # Explicitly use async database URL with aiomysql driver
        connection_string = get_database_url('user_data', async_mode=True)
        await run_migrations(connection_string, 'user_data')
        logger.info("Database migrations completed successfully")
    except Exception as e:
        logger.error(f"Error running database migrations: {str(e)}")
        logger.debug(f"Migration error traceback: {traceback.format_exc()}")
        # Continue startup even if migrations fail
    
    await init_async_redis_client()
    logger.info("Async Redis client initialized")
    
    # Initialize OpenAI Assistant
    from openai import AsyncOpenAI
    from utils.assistant_utils import setup_vector_store_async
    from utils.assistant_utils import initialize_assistant_async, initialize_assistant_async_for_earning_report, initialize_assistant_async_for_stock_analyst
    client = AsyncOpenAI(
        api_key=os.getenv('OPENAI_API_KEY'),
        base_url=os.getenv('OPENAI_API_URL'),
        default_headers={"OpenAI-Beta": "assistants=v2"}
    )
    
    enable_file_search = False
    if settings.ENABLE_FILE_SEARCH:
        # Set up vector store and handle failure gracefully
        enable_file_search = await setup_vector_store_async(client, os.getenv('VECTOR_NAME'))
        if not enable_file_search:
            logger.warning("Failed to set up vector store, continuing without file_search capability")
    
    if settings.EARNING_VECTOR_NAME != '':
        # Set up vector store for earnings report
        vector_store_id = await create_vector_store_if_not_exist(client, settings.EARNING_VECTOR_NAME)
        if vector_store_id == '':
            logger.warning("Failed to set up vector store for earnings report")
        else:
            await async_redis.set(EARNINGS_ASSISTANT_VECTOR_STORE_KEY, vector_store_id)
            logger.info(f"Vector store for earnings report set up successfully with ID: {vector_store_id}")

    # Initialize assistant with file_search only if vector store setup was successful
    assistant_id = await initialize_assistant_async(client, enable_file_search=enable_file_search)
    earnings_assistant_id = await initialize_assistant_async_for_earning_report(client)
    stock_analyst_assistant_id = await initialize_assistant_async_for_stock_analyst(client, enable_file_search=enable_file_search)
    logger.info(f"Initialized OpenAI Assistant with ID: {assistant_id}, earnings Assistant ID: {earnings_assistant_id}, stock analyst Assistant ID: {stock_analyst_assistant_id}")
    
    # Yield control back to FastAPI
    yield
    
    # Cleanup code (if any) would go here
    # This will run when the application is shutting down
    logger.info("Application shutting down")

# Create FastAPI app
app = FastAPI(
    title="Go GPT Backend",
    description="Async API for Go GPT Backend",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware with more explicit configuration
# app.add_middleware(
#     CORSMiddleware,
#     allow_origins=["*"],  # In production, replace with specific origins
#     allow_credentials=True,
#     allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
#     allow_headers=["*"],
#     expose_headers=["*"],
#     max_age=86400,  # 24 hours
# )

# Include routers
app.include_router(cache_router)
app.include_router(assistant_router)
app.include_router(sec_filing_router)
app.include_router(financial_calendar_router)
app.include_router(earnings_calendar_router)
app.include_router(user_query_router)
app.include_router(shared_query_router)

# Pydantic models for request/response
class QueryRequest(BaseModel):
    query: str = Field(..., description="The user's query")
    session_id: Optional[str] = Field(None, description="Session ID for conversation context")
    provider_model: Optional[str] = Field(None, description="Provider and model to use (e.g., 'openai:gpt-4')")
    web_search: bool = Field(True, description="Whether to perform web search")
    stream: bool = Field(True, description="Whether to stream the response")  # Default to streaming
    new_round: bool = Field(False, description="Whether to start a new conversation round")
    include_summary: bool = Field(True, description="Whether to include summaries of previous rounds")
    max_tokens: Optional[int] = Field(None, description="Maximum number of tokens to include in the context")
    # Function calling parameters
    enable_functions: bool = Field(True, description="Whether to enable function calling")
    auto_functions: bool = Field(False, description="Whether to automatically use all available functions")
    functions: List[str] = Field([], description="List of specific function names to include")

class QueryResponse(BaseModel):
    response: str = Field(..., description="The model's response")
    session_id: str = Field(..., description="Session ID for the conversation")
    provider: Optional[str] = Field(None, description="The provider used")
    model: Optional[str] = Field(None, description="The model used")
    round_id: Optional[int] = Field(None, description="The conversation round ID")

class SessionRequest(BaseModel):
    session_id: str = Field(..., description="Session ID for the conversation")
    
class BranchRequest(BaseModel):
    session_id: str = Field(..., description="Session ID for the conversation")
    branch_name: str = Field(..., description="Name for the new branch")
    from_message_index: Optional[int] = Field(None, description="Optional index to branch from")
    
class ExportRequest(BaseModel):
    session_id: str = Field(..., description="Session ID for the conversation")
    format: str = Field("json", description="Export format (json, text, markdown)")
    
class ClearHistoryRequest(BaseModel):
    session_id: str = Field(..., description="Session ID for the conversation")
    round_id: Optional[int] = Field(None, description="Optional round ID to clear only messages from that round")


@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Go GPT Backend API"}

@app.get("/pub/healthcheck")
async def healthcheck():
    return {}

from middleware.auth import async_require_auth
from middleware.rate_limiting import async_rate_limit
from fastapi import Header

@app.post("/query", response_model=QueryResponse)
@async_require_auth
async def query(request: QueryRequest, background_tasks: BackgroundTasks, auth0_sub: str = Header(None)):
    """
    Process an authenticated query from the user.
    Requires auth0_sub in request headers.
    
    Args:
        request: The query request
        background_tasks: FastAPI background tasks
        auth0_sub: User identifier from request header
        
    Returns:
        The query response
    """
    # Get or create session ID
    session_id = request.session_id or str(uuid.uuid4())
    
    # Create conversation manager
    conversation = AsyncConversationManager(session_id)
    
    # Start a new round if requested
    if request.new_round:
        round_id = await conversation.start_new_round()
        logger.info(f"Started new conversation round {round_id} for session {session_id}")
    else:
        round_id = await conversation._get_current_round_id()
    
    # Add user message to conversation
    await conversation.add_user_message(request.query, round_id=round_id)
    
    # Prepare messages for the model
    messages = await conversation.prepare_messages(
        request.query,
        # web_search=request.web_search,  # Commented out as web search is now handled via function calling
        web_search=False,  # Disable automatic web search before model API call
        include_summary=request.include_summary,
        max_tokens=request.max_tokens
    )
    
    # Parse provider and model
    provider_name = "openai"  # Default provider
    model_name = "gpt-3.5-turbo"  # Default model
    
    if request.provider_model:
        parts = request.provider_model.split("/")
        if len(parts) >= 2:
            # First part is provider, rest is model
            provider_name = parts[0]
            model_name = '/'.join(parts[1:])  # Join remaining parts with slashes
    
    # Log the selected provider and model for debugging
    logger.info(f"Selected provider: {provider_name}, model: {model_name} for session {session_id}")
    
    # Get provider
    try:
        provider = await get_async_provider(provider_name)
        logger.debug(f"Provider instance: {provider.__class__.__name__}")
    except ValueError as e:
        logger.error(f"Failed to get provider {provider_name}: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    
    # Handle streaming response
    if request.stream:
        # Prepare function parameters
        additional_params = {}
        if request.auto_functions or request.functions:
            # Get function schemas
            from utils.function_registry import function_registry
            if request.functions:
                # Get specific functions
                additional_params['functions'] = [
                    schema for schema in function_registry.get_function_schemas() 
                    if schema['name'] in request.functions
                ]
            elif request.auto_functions:
                # Get all functions
                additional_params['functions'] = function_registry.get_function_schemas()
            
            additional_params['auto_functions'] = request.auto_functions
            additional_params['execute_functions'] = request.enable_functions
        
        return StreamingResponse(
            stream_response(provider, messages, model_name, session_id, conversation, round_id, **additional_params),
            media_type="text/event-stream"
        )
    
    # Non-streaming response
    try:
        # Prepare function parameters
        additional_params = {}
        if request.auto_functions or request.functions:
            # Get function schemas
            from utils.function_registry import function_registry
            if request.functions:
                # Get specific functions
                additional_params['functions'] = [
                    schema for schema in function_registry.get_function_schemas() 
                    if schema['name'] in request.functions
                ]
            elif request.auto_functions:
                # Get all functions
                additional_params['functions'] = function_registry.get_function_schemas()
            
            additional_params['auto_functions'] = request.auto_functions
            additional_params['execute_functions'] = request.enable_functions
        
        # Log the messages being sent to the model
        try:
            logger.debug(f"Messages being sent to model API: {json.dumps(messages, indent=2, default=str)}")
            logger.debug(f"Additional parameters: {json.dumps(additional_params, indent=2, default=str)}")
        except Exception as e:
            logger.warning(f"Could not serialize messages for logging: {str(e)}")
        
        # Call the model with stream=False explicitly set
        logger.debug(f"Making initial API call with stream=False")
        response = await provider.chat_completion(
            messages=messages,
            model=model_name,
            stream=False,  # Explicitly set stream=False
            **additional_params
        )
        
        # Log the full response structure for debugging
        try:
            logger.info(f"Raw API response structure: {json.dumps(response, indent=2, default=str)}")
        except Exception as e:
            logger.warning(f"Could not serialize response for logging: {str(e)}")
        
        # Extract the response text and function calls with proper error handling
        message = None
        function_calls = []
        try:
            # Validate response structure
            if not response:
                logger.error("Response is None or empty")
                raise ValueError("Empty response from provider")
                
            if 'choices' not in response:
                logger.error("Response does not contain 'choices' key")
                logger.debug(f"Response keys: {list(response.keys()) if isinstance(response, dict) else 'not a dict'}")
                raise KeyError("No 'choices' in response")
                
            if not response['choices']:
                logger.error("Response 'choices' is empty")
                raise IndexError("Empty choices array")
                
            # Log the choices structure
            logger.debug(f"Choices structure: {json.dumps(response['choices'], indent=2, default=str)}")
            
            # Extract message with detailed logging
            choice = response["choices"][0]
            logger.debug(f"First choice: {json.dumps(choice, indent=2, default=str)}")
            
            if 'message' not in choice:
                logger.error("Choice does not contain 'message' key")
                logger.debug(f"Choice keys: {list(choice.keys()) if isinstance(choice, dict) else 'not a dict'}")
                raise KeyError("No 'message' in choice")
            
            message = choice["message"]
            logger.debug(f"Message structure: {json.dumps(message, indent=2, default=str)}")
            
            if message is None:
                logger.warning("Message in response is None, using empty string for content")
                response_text = ""
            else:
                # Check if content exists in message
                if 'content' not in message and 'function_call' not in message:
                    logger.warning("Message does not contain 'content' or 'function_call' key")
                    logger.debug(f"Message keys: {list(message.keys()) if isinstance(message, dict) else 'not a dict'}")
                
                response_text = message.get("content", "")
                if response_text is None:
                    logger.warning("Content in message is None, using empty string")
                    response_text = ""
        except (KeyError, TypeError, IndexError) as e:
            logger.error(f"Error extracting message from response: {str(e)}")
            logger.debug(f"Response structure: {type(response)}, {response}")
            # Provide a fallback response
            response_text = "I'm sorry, but I encountered an issue processing your request."
        
        # Handle function calls in the response
        if message is not None and "function_call" in message:
            function_call = message["function_call"]
            
            # Log the function_call object type and structure
            logger.debug(f"Function call object type: {type(function_call).__name__}")
            try:
                logger.debug(f"Function call structure: {json.dumps(function_call, indent=2, default=str)}")
            except Exception as e:
                logger.warning(f"Could not serialize function_call for logging: {str(e)}")
            
            # Handle different types of function_call objects
            if hasattr(function_call, 'get') and callable(function_call.get):
                # Dictionary-like object
                function_name = function_call.get("name", "")
                arguments_str = function_call.get("arguments", "{}")
            elif hasattr(function_call, 'name') and hasattr(function_call, 'arguments'):
                # Object with name and arguments attributes (like FunctionCall)
                function_name = function_call.name if function_call.name is not None else ""
                arguments_str = function_call.arguments if function_call.arguments is not None else "{}"
            else:
                # Try to convert to string and extract information
                logger.warning(f"Unknown function_call object type: {type(function_call).__name__}")
                function_call_str = str(function_call)
                logger.debug(f"Function call as string: {function_call_str}")
                
                # Try to extract name and arguments from string representation
                import re
                name_match = re.search(r"name='([^']*)'", function_call_str)
                arguments_match = re.search(r"arguments='([^']*)'", function_call_str)
                
                function_name = name_match.group(1) if name_match else ""
                arguments_str = arguments_match.group(1) if arguments_match else "{}"
            
            logger.info(f"Model requested function call: {function_name}")
            logger.debug(f"Function arguments (raw): {arguments_str}")
            
            try:
                # Parse arguments
                arguments = json.loads(arguments_str)
                logger.debug(f"Parsed function arguments: {json.dumps(arguments, default=str)}")
                
                # Execute function
                from utils.function_registry import function_registry
                if function_registry.has_function(function_name):
                    logger.info(f"Executing function: {function_name}")
                    start_time = time.time()
                    function_result = await function_registry.execute_function(function_name, **arguments)
                    execution_time = time.time() - start_time
                    logger.info(f"Function {function_name} executed successfully in {execution_time:.2f}s")
                    
                    # Log the result (truncate if too large)
                    result_str = str(function_result)
                    if len(result_str) > 1000:
                        logger.debug(f"Function result (truncated): {result_str[:1000]}... [truncated]")
                    else:
                        logger.debug(f"Function result: {result_str}")
                    
                    # Add function result to conversation
                    logger.debug(f"Adding function result to conversation history")
                    await conversation.add_function_result(
                        function_name,
                        arguments,
                        function_result,
                        round_id=round_id
                    )
                    
                    # Add to function calls list
                    function_calls.append({
                        "name": function_name,
                        "arguments": arguments,
                        "result": function_result
                    })
                    
                    # Make a second API call with the function result
                    try:
                        logger.info(f"Making second API call with function results for session {session_id}")
                        
                        # Get updated messages including function results
                        updated_messages = await conversation.prepare_messages(
                            data_query=None,  # No new query, just use existing conversation
                            include_summary=request.include_summary,
                            max_tokens=request.max_tokens
                        )
                        
                        # Log the updated messages being sent to the model for the second API call
                        try:
                            logger.info(f"Updated messages for second API call: {json.dumps(updated_messages, indent=2, default=str)}")
                        except Exception as e:
                            logger.warning(f"Could not serialize updated messages for logging: {str(e)}")
                        
                        # Make second API call with stream=False explicitly set
                        logger.debug(f"Making second API call with stream=False")
                        second_response = await provider.chat_completion(
                            messages=updated_messages,
                            model=model_name,
                            stream=False,  # Explicitly set stream=False
                            **additional_params
                        )
                        
                        # Log the second API response structure
                        try:
                            logger.info(f"Second API response structure: {json.dumps(second_response, indent=2, default=str)}")
                        except Exception as e:
                            logger.warning(f"Could not serialize second API response for logging: {str(e)}")
                        
                        # Extract the response text with proper error handling
                        second_response_text = ""
                        try:
                            logger.debug(f"Second API response type: {type(second_response)}")
                            
                            # Validate response structure
                            if not second_response or 'choices' not in second_response or not second_response['choices']:
                                logger.warning("Invalid second response structure")
                                raise KeyError("Invalid response structure")
                                
                            second_message = second_response["choices"][0].get("message")
                            if second_message is None:
                                logger.warning("Message in second response is None")
                                raise KeyError("Message is None")
                                
                            second_response_text = second_message.get("content", "")
                            if second_response_text is None:
                                logger.warning("Content in second message is None, using empty string")
                                second_response_text = ""
                                
                            logger.debug(f"Successfully extracted content from second API response")
                        except (TypeError, KeyError, IndexError, AttributeError) as e:
                            logger.error(f"Error accessing second response structure: {str(e)}")
                            logger.debug(f"Second response structure: {type(second_response)}, {second_response}")
                            # Fallback to original response
                            response_text += f"\n\nFunction {function_name} result: {json.dumps(function_result, indent=2)}"
                            logger.info(f"Using fallback response due to error in second API call")
                        
                        # If we got a response, update the response text
                        if second_response_text:
                            response_text = second_response_text
                            logger.info(f"Updated response with second API call result")
                    except Exception as e:
                        logger.error(f"Error in second API call: {str(e)}")
                        logger.debug(f"Second API call error traceback: {traceback.format_exc()}")
                        # Keep the original response text and add the function result
                        response_text += f"\n\nFunction {function_name} result: {json.dumps(function_result, indent=2)}"
                        logger.info(f"Updated response with function result (second API call failed)")
                else:
                    logger.error(f"Function {function_name} not found in registry")
                    logger.debug(f"Available functions: {function_registry.get_function_names()}")
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing function arguments: {str(e)}")
                logger.debug(f"Invalid JSON: {arguments_str}")
            except Exception as e:
                logger.error(f"Error executing function {function_name}: {str(e)}")
                logger.debug(f"Function execution error traceback: {traceback.format_exc()}")
        
        # Add assistant message to conversation
        await conversation.add_assistant_message(
            response_text,
            provider=provider_name,
            model=model_name,
            round_id=round_id,
            function_calls=function_calls if function_calls else None
        )
        
        # Return the response
        return {
            "response": response_text,
            "session_id": session_id,
            "provider": provider_name,
            "model": model_name,
            "round_id": round_id
        }
    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")
    
@app.post("/pub/query", response_model=QueryResponse)
@async_rate_limit
async def public_query(request: QueryRequest, background_tasks: BackgroundTasks):
    """
    Process a public (unauthenticated) query from the user.
    
    Args:
        request: The query request
        background_tasks: FastAPI background tasks
        
    Returns:
        The query response
    """
    # Get or create session ID
    session_id = request.session_id or str(uuid.uuid4())
    
    # Create conversation manager
    conversation = AsyncConversationManager(session_id)
    
    # Start a new round if requested
    if request.new_round:
        round_id = await conversation.start_new_round()
        logger.info(f"Started new conversation round {round_id} for session {session_id}")
    else:
        round_id = await conversation._get_current_round_id()
    
    # Add user message to conversation
    await conversation.add_user_message(request.query, round_id=round_id)
    
    # Prepare messages for the model
    messages = await conversation.prepare_messages(
        request.query,
        # web_search=request.web_search,  # Commented out as web search is now handled via function calling
        web_search=False,  # Disable automatic web search before model API call
        include_summary=request.include_summary,
        max_tokens=request.max_tokens
    )
    
    # Parse provider and model
    provider_name = "openai"  # Default provider
    model_name = "gpt-3.5-turbo"  # Default model
    
    if request.provider_model:
        parts = request.provider_model.split("/")
        if len(parts) >= 2:
            # First part is provider, rest is model
            provider_name = parts[0]
            model_name = '/'.join(parts[1:])  # Join remaining parts with slashes
    
    # Log the selected provider and model for debugging
    logger.info(f"Selected provider: {provider_name}, model: {model_name} for session {session_id}")
    
    # Get provider
    try:
        provider = await get_async_provider(provider_name)
        logger.debug(f"Provider instance: {provider.__class__.__name__}")
    except ValueError as e:
        logger.error(f"Failed to get provider {provider_name}: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    
    # Handle streaming response
    if request.stream:
        # Prepare function parameters
        additional_params = {}
        if request.auto_functions or request.functions:
            # Get function schemas
            from utils.function_registry import function_registry
            if request.functions:
                # Get specific functions
                additional_params['functions'] = [
                    schema for schema in function_registry.get_function_schemas() 
                    if schema['name'] in request.functions
                ]
            elif request.auto_functions:
                # Get all functions
                additional_params['functions'] = function_registry.get_function_schemas()
            
            additional_params['auto_functions'] = request.auto_functions
            additional_params['execute_functions'] = request.enable_functions
        
        return StreamingResponse(
            stream_response(provider, messages, model_name, session_id, conversation, round_id, **additional_params),
            media_type="text/event-stream"
        )
    
    # Non-streaming response
    try:
        # Prepare function parameters
        additional_params = {}
        if request.auto_functions or request.functions:
            # Get function schemas
            from utils.function_registry import function_registry
            if request.functions:
                # Get specific functions
                additional_params['functions'] = [
                    schema for schema in function_registry.get_function_schemas() 
                    if schema['name'] in request.functions
                ]
            elif request.auto_functions:
                # Get all functions
                additional_params['functions'] = function_registry.get_function_schemas()
            
            additional_params['auto_functions'] = request.auto_functions
            additional_params['execute_functions'] = request.enable_functions
        
        # Log the messages being sent to the model
        try:
            logger.info(f"Messages being sent to model API: {json.dumps(messages, indent=2, default=str)}")
            logger.info(f"Additional parameters: {json.dumps(additional_params, indent=2, default=str)}")
        except Exception as e:
            logger.warning(f"Could not serialize messages for logging: {str(e)}")
        
        # Call the model with stream=False explicitly set
        logger.debug(f"Making initial API call with stream=False")
        response = await provider.chat_completion(
            messages=messages,
            model=model_name,
            stream=False,  # Explicitly set stream=False
            **additional_params
        )
        
        # Log the full response structure for debugging
        try:
            logger.info(f"Raw API response structure: {json.dumps(response, indent=2, default=str)}")
        except Exception as e:
            logger.warning(f"Could not serialize response for logging: {str(e)}")
        
        # Extract the response text and function calls with proper error handling
        message = None
        function_calls = []
        try:
            # Validate response structure
            if not response:
                logger.error("Response is None or empty")
                raise ValueError("Empty response from provider")
                
            if 'choices' not in response:
                logger.error("Response does not contain 'choices' key")
                logger.debug(f"Response keys: {list(response.keys()) if isinstance(response, dict) else 'not a dict'}")
                raise KeyError("No 'choices' in response")
                
            if not response['choices']:
                logger.error("Response 'choices' is empty")
                raise IndexError("Empty choices array")
                
            # Log the choices structure
            logger.debug(f"Choices structure: {json.dumps(response['choices'], indent=2, default=str)}")
            
            # Extract message with detailed logging
            choice = response["choices"][0]
            logger.debug(f"First choice: {json.dumps(choice, indent=2, default=str)}")
            
            if 'message' not in choice:
                logger.error("Choice does not contain 'message' key")
                logger.debug(f"Choice keys: {list(choice.keys()) if isinstance(choice, dict) else 'not a dict'}")
                raise KeyError("No 'message' in choice")
            
            message = choice["message"]
            logger.debug(f"Message structure: {json.dumps(message, indent=2, default=str)}")
            
            if message is None:
                logger.warning("Message in response is None, using empty string for content")
                response_text = ""
            else:
                # Check if content exists in message
                if 'content' not in message and 'function_call' not in message:
                    logger.warning("Message does not contain 'content' or 'function_call' key")
                    logger.debug(f"Message keys: {list(message.keys()) if isinstance(message, dict) else 'not a dict'}")
                
                response_text = message.get("content", "")
                if response_text is None:
                    logger.warning("Content in message is None, using empty string")
                    response_text = ""
        except (KeyError, TypeError, IndexError) as e:
            logger.error(f"Error extracting message from response: {str(e)}")
            logger.debug(f"Response structure: {type(response)}, {response}")
            # Provide a fallback response
            response_text = "I'm sorry, but I encountered an issue processing your request."
        
        # Handle function calls in the response
        if message is not None and "function_call" in message:
            function_call = message["function_call"]
            
            # Log the function_call object type and structure
            logger.debug(f"Function call object type: {type(function_call).__name__}")
            try:
                logger.debug(f"Function call structure: {json.dumps(function_call, indent=2, default=str)}")
            except Exception as e:
                logger.warning(f"Could not serialize function_call for logging: {str(e)}")
            
            # Handle different types of function_call objects
            if hasattr(function_call, 'get') and callable(function_call.get):
                # Dictionary-like object
                function_name = function_call.get("name", "")
                arguments_str = function_call.get("arguments", "{}")
            elif hasattr(function_call, 'name') and hasattr(function_call, 'arguments'):
                # Object with name and arguments attributes (like FunctionCall)
                function_name = function_call.name if function_call.name is not None else ""
                arguments_str = function_call.arguments if function_call.arguments is not None else "{}"
            else:
                # Try to convert to string and extract information
                logger.warning(f"Unknown function_call object type: {type(function_call).__name__}")
                function_call_str = str(function_call)
                logger.debug(f"Function call as string: {function_call_str}")
                
                # Try to extract name and arguments from string representation
                import re
                name_match = re.search(r"name='([^']*)'", function_call_str)
                arguments_match = re.search(r"arguments='([^']*)'", function_call_str)
                
                function_name = name_match.group(1) if name_match else ""
                arguments_str = arguments_match.group(1) if arguments_match else "{}"
            
            logger.info(f"Model requested function call: {function_name}")
            logger.debug(f"Function arguments (raw): {arguments_str}")
            
            try:
                # Parse arguments
                arguments = json.loads(arguments_str)
                logger.debug(f"Parsed function arguments: {json.dumps(arguments, default=str)}")
                
                # Execute function
                from utils.function_registry import function_registry
                if function_registry.has_function(function_name):
                    logger.info(f"Executing function: {function_name}")
                    start_time = time.time()
                    function_result = await function_registry.execute_function(function_name, **arguments)
                    execution_time = time.time() - start_time
                    logger.info(f"Function {function_name} executed successfully in {execution_time:.2f}s")
                    
                    # Log the result (truncate if too large)
                    result_str = str(function_result)
                    if len(result_str) > 1000:
                        logger.debug(f"Function result (truncated): {result_str[:1000]}... [truncated]")
                    else:
                        logger.debug(f"Function result: {result_str}")
                    
                    # Add function result to conversation
                    logger.debug(f"Adding function result to conversation history")
                    await conversation.add_function_result(
                        function_name,
                        arguments,
                        function_result,
                        round_id=round_id
                    )
                    
                    # Add to function calls list
                    function_calls.append({
                        "name": function_name,
                        "arguments": arguments,
                        "result": function_result
                    })
                    
                    # Make a second API call with the function result
                    try:
                        logger.info(f"Making second API call with function results for session {session_id}")
                        
                        # Get updated messages including function results
                        updated_messages = await conversation.prepare_messages(
                            data_query=None,  # No new query, just use existing conversation
                            include_summary=request.include_summary,
                            max_tokens=request.max_tokens
                        )
                        
                        # Log the updated messages being sent to the model for the second API call
                        try:
                            logger.info(f"Updated messages for second API call: {json.dumps(updated_messages, indent=2, default=str)}")
                        except Exception as e:
                            logger.warning(f"Could not serialize updated messages for logging: {str(e)}")
                        
                        # Make second API call with stream=False explicitly set
                        logger.debug(f"Making second API call with stream=False")
                        second_response = await provider.chat_completion(
                            messages=updated_messages,
                            model=model_name,
                            stream=False,  # Explicitly set stream=False
                            **additional_params
                        )
                        
                        # Log the second API response structure
                        try:
                            logger.info(f"Second API response structure: {json.dumps(second_response, indent=2, default=str)}")
                        except Exception as e:
                            logger.warning(f"Could not serialize second API response for logging: {str(e)}")
                        
                        # Extract the response text with proper error handling
                        second_response_text = ""
                        try:
                            logger.debug(f"Second API response type: {type(second_response)}")
                            
                            # Validate response structure
                            if not second_response or 'choices' not in second_response or not second_response['choices']:
                                logger.warning("Invalid second response structure")
                                raise KeyError("Invalid response structure")
                                
                            second_message = second_response["choices"][0].get("message")
                            if second_message is None:
                                logger.warning("Message in second response is None")
                                raise KeyError("Message is None")
                                
                            second_response_text = second_message.get("content", "")
                            if second_response_text is None:
                                logger.warning("Content in second message is None, using empty string")
                                second_response_text = ""
                                
                            logger.debug(f"Successfully extracted content from second API response")
                        except (TypeError, KeyError, IndexError, AttributeError) as e:
                            logger.error(f"Error accessing second response structure: {str(e)}")
                            logger.debug(f"Second response structure: {type(second_response)}, {second_response}")
                            # Fallback to original response
                            response_text += f"\n\nFunction {function_name} result: {json.dumps(function_result, indent=2)}"
                            logger.info(f"Using fallback response due to error in second API call")
                        
                        # If we got a response, update the response text
                        if second_response_text:
                            response_text = second_response_text
                            logger.info(f"Updated response with second API call result")
                    except Exception as e:
                        logger.error(f"Error in second API call: {str(e)}")
                        logger.debug(f"Second API call error traceback: {traceback.format_exc()}")
                        # Keep the original response text and add the function result
                        response_text += f"\n\nFunction {function_name} result: {json.dumps(function_result, indent=2)}"
                        logger.info(f"Updated response with function result (second API call failed)")
                else:
                    logger.error(f"Function {function_name} not found in registry")
                    logger.debug(f"Available functions: {function_registry.get_function_names()}")
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing function arguments: {str(e)}")
                logger.debug(f"Invalid JSON: {arguments_str}")
            except Exception as e:
                logger.error(f"Error executing function {function_name}: {str(e)}")
                logger.debug(f"Function execution error traceback: {traceback.format_exc()}")
        
        # Add assistant message to conversation
        await conversation.add_assistant_message(
            response_text,
            provider=provider_name,
            model=model_name,
            round_id=round_id,
            function_calls=function_calls if function_calls else None
        )
        
        # Return the response
        return {
            "response": response_text,
            "session_id": session_id,
            "provider": provider_name,
            "model": model_name,
            "round_id": round_id
        }
    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")

async def stream_response(provider, messages, model, session_id, conversation, round_id, **kwargs):
    """
    Stream the response from the model.
    
    Args:
        provider: The model provider
        messages: The messages to send to the model
        model: The model to use
        session_id: The session ID
        conversation: The conversation manager
        round_id: The conversation round ID
        **kwargs: Additional parameters including function definitions
        
    Yields:
        Chunks of the response
    """
    try:
        # Log streaming request details
        provider_name = provider.__class__.__name__
        logger.info(f"Streaming with provider: {provider_name}, model: {model}, session: {session_id}, round: {round_id}")
        
        # Log the messages being sent to the model
        try:
            logger.info(f"Streaming: Messages being sent to model API: {json.dumps(messages, indent=2, default=str)}")
            logger.info(f"Streaming: Additional parameters: {json.dumps(kwargs, indent=2, default=str)}")
        except Exception as e:
            logger.warning(f"Streaming: Could not serialize messages for logging: {str(e)}")
        
        # Call the model with streaming
        response_stream = await provider.chat_completion(
            messages=messages,
            model=model,
            stream=True,
            **kwargs
        )
        logger.debug(f"Got streaming response object of type: {type(response_stream).__name__}")
        
        # Collect the full response for conversation history
        full_response = ""
        
        # Stream the response
        try:
            # Track function calls
            function_calls = []
            current_function_call = None
            
            async for chunk in response_stream:
                try:
                    # Handle function calls
                    if (hasattr(chunk, 'choices') and chunk.choices and 
                        hasattr(chunk.choices[0], 'delta') and 
                        hasattr(chunk.choices[0].delta, 'function_call')):
                        # Process function call
                        function_delta = chunk.choices[0].delta.function_call
                        
                        # Log the function_delta object type
                        logger.debug(f"Streaming: Function delta object type: {type(function_delta).__name__}")
                        
                        # Extract name and arguments based on object type
                        delta_name = ""
                        delta_arguments = ""
                        
                        if hasattr(function_delta, 'get') and callable(function_delta.get):
                            # Dictionary-like object
                            delta_name = function_delta.get("name", "")
                            delta_arguments = function_delta.get("arguments", "")
                        elif hasattr(function_delta, 'name') and hasattr(function_delta, 'arguments'):
                            # Object with name and arguments attributes (like FunctionCall)
                            delta_name = function_delta.name if function_delta.name is not None else ""
                            delta_arguments = function_delta.arguments if function_delta.arguments is not None else ""
                        else:
                            # Try to convert to string and extract information
                            logger.warning(f"Streaming: Unknown function_delta object type: {type(function_delta).__name__}")
                            function_delta_str = str(function_delta)
                            logger.debug(f"Streaming: Function delta as string: {function_delta_str}")
                            
                            # Try to extract name and arguments from string representation
                            import re
                            name_match = re.search(r"name='([^']*)'", function_delta_str)
                            arguments_match = re.search(r"arguments='([^']*)'", function_delta_str)
                            
                            delta_name = name_match.group(1) if name_match else ""
                            delta_arguments = arguments_match.group(1) if arguments_match else ""
                        
                        if current_function_call is None:
                            current_function_call = {
                                "name": delta_name,
                                "arguments": delta_arguments
                            }
                        else:
                            if delta_name:
                                current_function_call["name"] = delta_name
                            
                            if delta_arguments:
                                current_function_call["arguments"] += delta_arguments
                        
                        # Send function call chunk to client
                        yield f"data: {json.dumps({'function_call': current_function_call, 'session_id': session_id, 'round_id': round_id})}\n\n"
                        
                        # Check if function call is complete
                        if hasattr(chunk.choices[0], 'finish_reason') and chunk.choices[0].finish_reason == 'function_call':
                            # Function call is complete, execute it
                            try:
                                from utils.function_registry import function_registry
                                
                                function_name = current_function_call["name"]
                                arguments_str = current_function_call["arguments"]
                                
                                logger.info(f"Streaming: Model completed function call request: {function_name}")
                                logger.debug(f"Streaming: Function arguments (raw): {arguments_str}")
                                
                                # Parse arguments
                                try:
                                    arguments = json.loads(arguments_str)
                                    logger.debug(f"Streaming: Parsed function arguments: {json.dumps(arguments, default=str)}")
                                except json.JSONDecodeError as e:
                                    logger.error(f"Streaming: Failed to parse function arguments: {arguments_str}")
                                    logger.debug(f"Streaming: JSON parse error: {str(e)}")
                                    arguments = {}
                                
                                # Execute function
                                if function_registry.has_function(function_name):
                                    logger.info(f"Streaming: Executing function: {function_name}")
                                    start_time = time.time()
                                    function_result = await function_registry.execute_function(function_name, **arguments)
                                    execution_time = time.time() - start_time
                                    logger.info(f"Streaming: Function {function_name} executed successfully in {execution_time:.2f}s")
                                    
                                    # Log the result (truncate if too large)
                                    result_str = str(function_result)
                                    if len(result_str) > 1000:
                                        logger.debug(f"Streaming: Function result (truncated): {result_str[:1000]}... [truncated]")
                                    else:
                                        logger.debug(f"Streaming: Function result: {result_str}")
                                    
                                    # Add function result to conversation
                                    logger.debug(f"Streaming: Adding function result to conversation history")
                                    await conversation.add_function_result(
                                        function_name,
                                        arguments,
                                        function_result,
                                        round_id=round_id
                                    )
                                    
                                    # Send function result to client
                                    logger.debug(f"Streaming: Sending function result to client")
                                    yield f"data: {json.dumps({'function_result': {'name': function_name, 'result': function_result}, 'session_id': session_id, 'round_id': round_id})}\n\n"
                                    
                                    # Add to function calls list
                                    function_calls.append({
                                        "name": function_name,
                                        "arguments": arguments,
                                        "result": function_result
                                    })
                                    logger.debug(f"Streaming: Added function call to function_calls list (total: {len(function_calls)})")
                                    
                                    # Make a second API call with the function result
                                    try:
                                        logger.info(f"Making second API call with function results for session {session_id}")
                                        yield f"data: {json.dumps({'status': 'Processing function results...', 'session_id': session_id, 'round_id': round_id})}\n\n"
                                        
                                        # Get updated messages including function results
                                        updated_messages = await conversation.prepare_messages(
                                            data_query=None,  # No new query, just use existing conversation
                                            include_summary=kwargs.get('include_summary', True),
                                            max_tokens=kwargs.get('max_tokens')
                                        )
                                        
                                        # Log the updated messages being sent to the model for the second API call
                                        try:
                                            logger.info(f"Streaming: Updated messages for second API call: {json.dumps(updated_messages, indent=2, default=str)}")
                                        except Exception as e:
                                            logger.warning(f"Streaming: Could not serialize updated messages for logging: {str(e)}")
                                        
                                        # Make second API call
                                        second_response_stream = await provider.chat_completion(
                                            messages=updated_messages,
                                            model=model,
                                            stream=True,
                                            **kwargs
                                        )
                                        
                                        second_full_response = ""
                                        async for second_chunk in second_response_stream:
                                            if (hasattr(second_chunk, 'choices') and second_chunk.choices and 
                                                hasattr(second_chunk.choices[0], 'delta') and 
                                                hasattr(second_chunk.choices[0].delta, 'content')):
                                                content = second_chunk.choices[0].delta.content
                                                if content:
                                                    second_full_response += content
                                                    yield f"data: {json.dumps({'content': content, 'session_id': session_id, 'round_id': round_id})}\n\n"
                                        
                                        # If we got a response, update the conversation
                                        if second_full_response:
                                            # Update the assistant's message with the new response
                                            await conversation.add_assistant_message(
                                                second_full_response,
                                                provider=provider.__class__.__name__.replace('Provider', '').lower(),
                                                model=model,
                                                round_id=round_id
                                            )
                                            
                                            # Update the full response for the conversation history
                                            full_response = second_full_response
                                    except Exception as e:
                                        logger.error(f"Error in second API call: {str(e)}")
                                        logger.debug(f"Second API call error traceback: {traceback.format_exc()}")
                                        yield f"data: {json.dumps({'error': f'Error processing function results: {str(e)}', 'session_id': session_id, 'round_id': round_id})}\n\n"
                                else:
                                    logger.error(f"Streaming: Function {function_name} not found in registry")
                                    logger.debug(f"Streaming: Available functions: {function_registry.get_function_names()}")
                                    yield f"data: {json.dumps({'error': f'Function {function_name} not found', 'session_id': session_id, 'round_id': round_id})}\n\n"
                            except Exception as e:
                                logger.error(f"Streaming: Error executing function {function_name}: {str(e)}")
                                logger.debug(f"Streaming: Function execution error traceback: {traceback.format_exc()}")
                                yield f"data: {json.dumps({'error': f'Error executing function: {str(e)}', 'session_id': session_id, 'round_id': round_id})}\n\n"
                            
                            # Reset current function call
                            current_function_call = None
                    
                    # Handle regular content
                    elif (hasattr(chunk, 'choices') and chunk.choices and 
                          hasattr(chunk.choices[0], 'delta') and 
                          hasattr(chunk.choices[0].delta, 'content')):
                        content = chunk.choices[0].delta.content
                        if content:
                            full_response += content
                            yield f"data: {json.dumps({'content': content, 'session_id': session_id, 'round_id': round_id})}\n\n"
                except Exception as e:
                    logger.error(f"Error processing chunk: {str(e)}")
                    # Continue processing other chunks even if one fails
                    continue
        except Exception as e:
            logger.error(f"Error iterating through stream: {str(e)}")
            # If we have any response so far, we'll still save it
            if not full_response:
                full_response = f"Error during streaming: {str(e)}"
                yield f"data: {json.dumps({'content': full_response, 'session_id': session_id, 'round_id': round_id})}\n\n"
        
        # Add the full response to the conversation history
        if full_response:
            await conversation.add_assistant_message(
                full_response,
                provider=provider.__class__.__name__.replace('Provider', '').lower(),
                model=model,
                round_id=round_id,
                function_calls=function_calls if function_calls else None
            )
        
        # Send the end of stream marker
        yield f"data: {json.dumps({'content': '[DONE]', 'session_id': session_id, 'round_id': round_id})}\n\n"
    except Exception as e:
        logger.error(f"Error streaming response: {str(e)}")
        yield f"data: {json.dumps({'error': str(e), 'session_id': session_id, 'round_id': round_id})}\n\n"

@app.post("/conversation/new-round")
async def start_new_round(request: SessionRequest):
    """
    Start a new conversation round.
    
    Args:
        request: The request containing the session ID
        
    Returns:
        Information about the new round
    """
    session_id = request.session_id
    conversation = AsyncConversationManager(session_id)
    
    try:
        round_id = await conversation.start_new_round()
        return {
            "session_id": session_id,
            "round_id": round_id,
            "message": f"Started new conversation round {round_id}"
        }
    except Exception as e:
        logger.error(f"Error starting new round: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error starting new round: {str(e)}")

@app.get("/conversation/stats/{session_id}")
async def get_conversation_stats(session_id: str):
    """
    Get statistics about a conversation.
    
    Args:
        session_id: The conversation session ID
        
    Returns:
        Conversation statistics
    """
    if not session_id:
        raise HTTPException(status_code=400, detail="Session ID is required")
    
    conversation = AsyncConversationManager(session_id)
    
    try:
        stats = await conversation.get_conversation_stats()
        stats["session_id"] = session_id
        return stats
    except Exception as e:
        logger.error(f"Error getting conversation stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting conversation stats: {str(e)}")

@app.post("/conversation/export")
async def export_conversation(request: ExportRequest):
    """
    Export a conversation.
    
    Args:
        request: The request containing the session ID and format
        
    Returns:
        The exported conversation
    """
    session_id = request.session_id
    format = request.format
    
    conversation = AsyncConversationManager(session_id)
    
    try:
        exported = await conversation.export_conversation(format=format)
        return {
            "session_id": session_id,
            "format": format,
            "content": exported
        }
    except Exception as e:
        logger.error(f"Error exporting conversation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error exporting conversation: {str(e)}")

@app.post("/conversation/branch")
async def create_branch(request: BranchRequest):
    """
    Create a new conversation branch.
    
    Args:
        request: The request containing the session ID and branch name
        
    Returns:
        Information about the new branch
    """
    session_id = request.session_id
    branch_name = request.branch_name
    from_message_index = request.from_message_index
    
    conversation = AsyncConversationManager(session_id)
    
    try:
        branch_session_id = await conversation.create_branch(branch_name, from_message_index)
        return {
            "original_session_id": session_id,
            "branch_session_id": branch_session_id,
            "branch_name": branch_name,
            "message": f"Created branch {branch_name} from session {session_id}"
        }
    except Exception as e:
        logger.error(f"Error creating branch: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating branch: {str(e)}")

@app.post("/conversation/clear")
async def clear_history(request: ClearHistoryRequest):
    """
    Clear conversation history.
    
    Args:
        request: The request containing the session ID and optional round ID
        
    Returns:
        Success message
    """
    session_id = request.session_id
    round_id = request.round_id
    
    if not session_id:
        raise HTTPException(status_code=400, detail="Session ID is required")
    
    conversation = AsyncConversationManager(session_id)
    
    try:
        await conversation.clear_history(round_id)
        
        if round_id:
            message = f"Cleared conversation history for round {round_id} in session {session_id}"
        else:
            message = f"Cleared all conversation history for session {session_id}"
            
        return {
            "session_id": session_id,
            "round_id": round_id,
            "message": message
        }
    except Exception as e:
        logger.error(f"Error clearing history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error clearing history: {str(e)}")

if __name__ == "__main__":
    port = int(os.getenv("PORT", 5002))
    uvicorn.run("async_app:app", host="0.0.0.0", port=port, reload=True)
