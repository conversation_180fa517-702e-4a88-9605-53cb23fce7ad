from typing import Dict, Type, Optional
from .async_base_provider import Async<PERSON>odelProvider
from .async_openai_provider import Async<PERSON><PERSON><PERSON>IProvider
from .async_anthropic_provider import Async<PERSON><PERSON>hropic<PERSON>rovider
from .async_deepseek_provider import Async<PERSON>eepSeekProvider
from .async_openai_assistant_provider import AsyncOpenAIAssistantProvider
from utils.logging import logger

# Registry of available async providers
ASYNC_PROVIDERS: Dict[str, Type[AsyncModelProvider]] = {
    'openai': AsyncOpenAIProvider,
    'anthropic': AsyncAnthropicProvider,
    'deepseek': AsyncDeepSeekProvider,
    'openai_assistant': AsyncOpenAIAssistantProvider
}

# Cache of provider instances
_provider_instances: Dict[str, AsyncModelProvider] = {}

async def get_async_provider(provider_name: str) -> AsyncModelProvider:
    """
    Get an async provider instance by name.
    
    Args:
        provider_name: The name of the provider to get
        
    Returns:
        An instance of the requested provider
        
    Raises:
        ValueError: If the provider is not found
    """
    provider_key = provider_name.lower()
    
    # Check if we already have an instance
    if provider_key in _provider_instances:
        return _provider_instances[provider_key]
    
    # Get the provider class
    provider_class = ASYNC_PROVIDERS.get(provider_key)
    if not provider_class:
        logger.error(f"Unknown provider: {provider_name}")
        raise ValueError(f"Unknown provider: {provider_name}")
    
    # Create a new instance
    provider = provider_class()
    _provider_instances[provider_key] = provider
    
    return provider

def register_async_provider(name: str, provider_class: Type[AsyncModelProvider]) -> None:
    """
    Register a new async provider.
    
    Args:
        name: The name to register the provider under
        provider_class: The provider class to register
    """
    ASYNC_PROVIDERS[name.lower()] = provider_class
    # Clear the instance cache for this provider
    if name.lower() in _provider_instances:
        del _provider_instances[name.lower()]
    
    logger.info(f"Registered async provider: {name}")

def get_available_async_providers() -> Dict[str, Type[AsyncModelProvider]]:
    """
    Get all available async providers.
    
    Returns:
        A dictionary of provider names to provider classes
    """
    return ASYNC_PROVIDERS.copy()
