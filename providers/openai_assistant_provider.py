import os
import json
import time
from typing import Dict, Any, List, Optional, Generator
from openai import OpenAI
from .base_provider import Model<PERSON>rovider
from utils.circuit_breaker import with_circuit_breaker, with_retry, with_fallback
from utils.logging import logger
from utils.assistant_utils import initialize_assistant
import datetime
from utils.sample_functions import search, refine_search, get_search_history, get_tickers_dividend, query_stocks, query_ticker_concepts, query_sec_filings, query_sec_filing_sections

class OpenAIAssistantProvider(ModelProvider):
    """
    Provider implementation for OpenAI Assistant API.
    """
    def __init__(self):
        # Initialize the OpenAI client with the v2 Assistants API header
        self.client = OpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_API_URL'),
            default_headers={"OpenAI-Beta": "assistants=v2"}
        )
        
        # Get assistant ID from Redis
        from utils.redis_client import redis_client
        from utils.assistant_utils import ASSISTANT_ID_KEY
        self.assistant_id = redis_client.get(ASSISTANT_ID_KEY)
        if not self.assistant_id:
            logger.warning("Assistant ID not found in Redis. This should have been initialized during app startup.")
            # Fallback to initializing the assistant
            self.assistant_id = initialize_assistant(self.client)
        
        logger.info(f"Using assistant ID: {self.assistant_id}")

    @with_circuit_breaker('openai_assistant')
    @with_retry(max_attempts=3, min_wait=1, max_wait=10)
    def _chat_completion_impl(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Implementation of chat completion for OpenAI Assistant API.
        
        Args:
            messages: List of message objects with role and content
            model: The model identifier to use (ignored, using assistant's model)
            **kwargs: Additional parameters
                - thread_id: Optional thread ID to continue conversation
                - stream: Whether to stream the response
                
        Returns:
            Dict containing the model response
        """
        try:
            # Get parameters
            thread_id = kwargs.pop('thread_id', None)
            stream = kwargs.pop('stream', False)
            assistant_id = kwargs.pop('assistant_id', self.assistant_id)
            
            # Create or retrieve assistant
            if not assistant_id:
                # Create a new assistant if ID not provided
                assistant = self.client.beta.assistants.create(
                    name="Temporary Assistant",
                    instructions="You are a helpful assistant.",
                    model=model or "gpt-4-turbo-preview"
                )
                assistant_id = assistant.id
                logger.info(f"Created new assistant with ID: {assistant_id}")
            
            # Thread ID must be provided by the service layer
            if not thread_id:
                logger.error("Thread ID not provided. Thread creation should happen in the service layer.")
                raise ValueError("Thread ID is required. Thread creation should happen in the service layer.")
            
            # Add messages to thread
            for message in messages:
                if message['role'] == 'user':
                    today_date = datetime.datetime.now().strftime("%d-%m-%Y")
                    self.client.beta.threads.messages.create(
                        thread_id=thread_id,
                        role="user",
                        content=f"{message['content']}. Today is {today_date}"
                    )
            
            # Check if there's an active run for this thread
            try:
                runs = self.client.beta.threads.runs.list(thread_id=thread_id)
                active_run = None
                
                # Find any active runs
                for run in runs.data:
                    if run.status in ["queued", "in_progress", "requires_action"]:
                        active_run = run
                        break
                
                # If there's an active run, try to cancel it first if it's not in a final state
                if active_run:
                    logger.info(f"Found existing active run {active_run.id} for thread {thread_id}")
                    
                    # If the run is in a state that can be cancelled, cancel it
                    if active_run.status in ["queued", "in_progress"]:
                        try:
                            logger.info(f"Cancelling existing run {active_run.id}")
                            self.client.beta.threads.runs.cancel(
                                thread_id=thread_id,
                                run_id=active_run.id
                            )
                            # Wait a moment for the cancellation to take effect
                            time.sleep(1)
                        except Exception as e:
                            logger.warning(f"Failed to cancel run {active_run.id}: {str(e)}")
                    
                    # For runs in "requires_action" state, we need to submit empty tool outputs to complete them
                    elif active_run.status == "requires_action":
                        try:
                            logger.info(f"Completing existing run {active_run.id} that requires action")
                            required_action = active_run.required_action
                            if required_action and required_action.type == "submit_tool_outputs":
                                tool_calls = required_action.submit_tool_outputs.tool_calls
                                tool_outputs = []
                                
                                # Create empty outputs for each tool call
                                for tool_call in tool_calls:
                                    tool_outputs.append({
                                        "tool_call_id": tool_call.id,
                                        "output": json.dumps({"error": "Operation cancelled by user"})
                                    })
                                
                                # Submit the tool outputs to complete the run
                                self.client.beta.threads.runs.submit_tool_outputs(
                                    thread_id=thread_id,
                                    run_id=active_run.id,
                                    tool_outputs=tool_outputs
                                )
                                # Wait a moment for the submission to take effect
                                time.sleep(1)
                        except Exception as e:
                            logger.warning(f"Failed to complete run {active_run.id}: {str(e)}")
                
                # Now check again if there are any active runs after our cleanup attempt
                runs = self.client.beta.threads.runs.list(thread_id=thread_id)
                active_run = None
                
                for run in runs.data:
                    if run.status in ["queued", "in_progress", "requires_action"]:
                        active_run = run
                        break
                
                # If there's still an active run, we need to wait for it to complete or cancel it
                if active_run:
                    logger.info(f"Found active run {active_run.id} that couldn't be cancelled or completed")
                    # Wait for the run to complete
                    max_wait_time = 30  # Maximum wait time in seconds
                    wait_time = 0
                    wait_interval = 2  # Wait interval in seconds
                    
                    while wait_time < max_wait_time:
                        # Check if the run is still active
                        run = self.client.beta.threads.runs.retrieve(
                            thread_id=thread_id,
                            run_id=active_run.id
                        )
                        
                        if run.status not in ["queued", "in_progress", "requires_action"]:
                            logger.info(f"Run {active_run.id} is now in state {run.status}")
                            break
                        
                        logger.info(f"Waiting for run {active_run.id} to complete, current status: {run.status}")
                        time.sleep(wait_interval)
                        wait_time += wait_interval
                    
                    # If we've waited too long, try one more time to cancel the run
                    if wait_time >= max_wait_time:
                        logger.warning(f"Timed out waiting for run {active_run.id} to complete")
                        try:
                            logger.info(f"Attempting to cancel run {active_run.id} one more time")
                            self.client.beta.threads.runs.cancel(
                                thread_id=thread_id,
                                run_id=active_run.id
                            )
                            # Wait a moment for the cancellation to take effect
                            time.sleep(2)
                        except Exception as e:
                            logger.warning(f"Failed to cancel run {active_run.id}: {str(e)}")
                
                # Check one more time if there are any active runs
                runs = self.client.beta.threads.runs.list(thread_id=thread_id)
                active_run = None
                
                for run in runs.data:
                    if run.status in ["queued", "in_progress", "requires_action"]:
                        active_run = run
                        break
                
                # If there's still an active run, we can't proceed with a new run
                # Instead, we'll return an error message
                if active_run:
                    logger.error(f"Thread {thread_id} still has an active run {active_run.id} that can't be cancelled")
                    raise Exception(f"Thread {thread_id} already has an active run {active_run.id} that can't be cancelled")
            except Exception as e:
                logger.error(f"Error checking for active runs: {str(e)}")
                # Don't create a run here, we'll do it below if needed
            
            # If streaming, return a generator
            if stream:
                return self._stream_assistant_response(thread_id, assistant_id)
            
            # Otherwise, wait for completion and return messages
            return self._wait_for_assistant_response(thread_id, assistant_id)
            
        except Exception as e:
            logger.error(f"OpenAI Assistant API error: {str(e)}")
            raise

    def _stream_assistant_response(self, thread_id: str, assistant_id: str) -> Generator[Dict[str, Any], None, None]:
        """
        Stream the assistant response using the official streaming API.
        
        Args:
            thread_id: The thread ID
            assistant_id: The assistant ID
            
        Returns:
            Generator yielding response chunks
        """
        import json
        
        # Function mapping for tool execution
        function_map = {
            "search": search,
            "refine_search": refine_search,
            "get_search_history": get_search_history,
            "get_tickers_dividend": get_tickers_dividend,
            "query_stocks": query_stocks,
            "query_ticker_concepts": query_ticker_concepts,
            "query_sec_filings": query_sec_filings,
            "query_sec_filing_sections": query_sec_filing_sections,
        }
        
        # Define event handler for the stream
        class EventHandler:
            def __init__(self):
                self.response_queue = []
                self.error = None
                self.run_id = None
                self._init = True  # Add this attribute to fix the 'EventHandler' object has no attribute '_init' error
            
            def on_run_created(self, run):
                self.run_id = run.id
                logger.info(f"Run created with ID: {run.id}")
            
            def on_tool_calls(self, tool_calls):
                tool_outputs = []
                
                for tool_call in tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)
                    
                    logger.info(f"Executing function: {function_name} with args: {function_args}")
                    
                    if function_name in function_map:
                        try:
                            result = function_map[function_name](**function_args)
                            tool_outputs.append({
                                "tool_call_id": tool_call.id,
                                "output": json.dumps(result)
                            })
                            logger.info(f"Function {function_name} executed successfully")
                        except Exception as e:
                            error_message = f"Error executing function {function_name}: {str(e)}"
                            logger.error(error_message)
                            tool_outputs.append({
                                "tool_call_id": tool_call.id,
                                "output": json.dumps({"error": error_message})
                            })
                    else:
                        error_message = f"Function {function_name} not found"
                        logger.error(error_message)
                        tool_outputs.append({
                            "tool_call_id": tool_call.id,
                            "output": json.dumps({"error": error_message})
                        })
                
                return tool_outputs
            
            def on_text_created(self, text):
                # Add the response to the queue
                run_id = getattr(stream, 'current_run', {}).id if hasattr(stream, 'current_run') else self.run_id
                self.response_queue.append({
                    "choices": [
                        {
                            "delta": {
                                "content": text.value
                            }
                        }
                    ],
                    "thread_id": thread_id,
                    "run_id": run_id
                })
            
            def on_text_delta(self, delta, snapshot):
                # Add each delta to the response queue for a more responsive experience
                if delta.value:  # Only add if there's actual content
                    run_id = getattr(stream, 'current_run', {}).id if hasattr(stream, 'current_run') else self.run_id
                    self.response_queue.append({
                        "choices": [
                            {
                                "delta": {
                                    "content": delta.value
                                }
                            }
                        ],
                        "thread_id": thread_id,
                        "run_id": run_id,
                        "is_delta": True  # Flag to indicate this is a partial update
                    })
            
            def on_error(self, error):
                error_message = f"Assistant run failed with error: {error}"
                logger.error(error_message)
                self.error = error_message
                self.response_queue.append({
                    "choices": [
                        {
                            "delta": {
                                "content": f"Error: {error_message}"
                            }
                        }
                    ],
                    "error": error_message
                })
        
        # Create a generator to yield results
        def generate():
            handler = EventHandler()
            
            try:
                logger.info(f"[_stream_assistant_response] Starting stream for thread_id={thread_id}")
                
                # Stream and create a new run in one step
                logger.info(f"[_stream_assistant_response] Creating and streaming run for thread_id={thread_id}")
                with self.client.beta.threads.runs.stream(
                    thread_id=thread_id,
                    assistant_id=assistant_id,
                    event_handler=handler
                ) as stream:
                    stream.until_done()
                    
                    # Yield any responses that were added to the queue
                    for response in handler.response_queue:
                        yield response
                
                # Get the run_id from the handler or stream
                run_id = handler.run_id or (stream.current_run.id if hasattr(stream, 'current_run') else None)
                logger.info(f"[_stream_assistant_response] Stream completed for run_id={run_id}")
                        
                # After streaming, check if the run is in require_action status
                if run_id:
                    run = self.client.beta.threads.runs.retrieve(
                        thread_id=thread_id,
                        run_id=run_id
                    )
                    
                    logger.info(f"[_stream_assistant_response] Run status after streaming: {run.status}")
                    
                    # If the run is in require_action status, handle the required actions
                    if run.status == "requires_action":
                        logger.info(f"[_stream_assistant_response] Run {run_id} requires action")
                        
                        # Get the required actions
                        required_action = run.required_action
                        if required_action and required_action.type == "submit_tool_outputs":
                            tool_calls = required_action.submit_tool_outputs.tool_calls
                            tool_outputs = []
                            
                            # Process each tool call
                            for tool_call in tool_calls:
                                function_name = tool_call.function.name
                                function_args = json.loads(tool_call.function.arguments)
                                
                                logger.info(f"Executing function: {function_name} with args: {function_args}")
                                
                                if function_name in function_map:
                                    try:
                                        result = function_map[function_name](**function_args)
                                        tool_outputs.append({
                                            "tool_call_id": tool_call.id,
                                            "output": json.dumps(result)
                                        })
                                        logger.info(f"Function {function_name} executed successfully")
                                    except Exception as e:
                                        error_message = f"Error executing function {function_name}: {str(e)}"
                                        logger.error(error_message)
                                        tool_outputs.append({
                                            "tool_call_id": tool_call.id,
                                            "output": json.dumps({"error": error_message})
                                        })
                                else:
                                    error_message = f"Function {function_name} not found"
                                    logger.error(error_message)
                                    tool_outputs.append({
                                        "tool_call_id": tool_call.id,
                                        "output": json.dumps({"error": error_message})
                                    })
                            
                            # Submit the tool outputs
                            if tool_outputs:
                                logger.info(f"[_stream_assistant_response] Submitting {len(tool_outputs)} tool outputs for run_id={run_id}")
                                for i, output in enumerate(tool_outputs):
                                    logger.debug(f"[_stream_assistant_response] Tool output {i+1}: tool_call_id={output['tool_call_id']}, output={output['output'][:100]}...")
                                
                                self.client.beta.threads.runs.submit_tool_outputs(
                                    thread_id=thread_id,
                                    run_id=run_id,
                                    tool_outputs=tool_outputs
                                )
                                logger.info(f"[_stream_assistant_response] Tool outputs submitted successfully for run_id={run_id}")
                                
                                # Continue streaming the run after submitting tool outputs
                                logger.info(f"[_stream_assistant_response] Starting second stream after tool outputs for run_id={run_id}")
                                with self.client.beta.threads.runs.stream(
                                    thread_id=thread_id,
                                    assistant_id=assistant_id,
                                    event_handler=handler
                                ) as stream:
                                    logger.info(f"[_stream_assistant_response] Second stream connection established")
                                    stream.until_done()
                                    
                                    # Yield any additional responses
                                    for response in handler.response_queue:
                                        yield response
                                logger.info(f"[_stream_assistant_response] Second stream completed for run_id={run_id}")
                    
            except Exception as e:
                logger.error(f"Error in streaming: {str(e)}")
                yield {
                    "choices": [
                        {
                            "delta": {
                                "content": f"Error: {str(e)}"
                            }
                        }
                    ],
                    "error": str(e)
                }
        
        return generate()

    def _wait_for_assistant_response(self, thread_id: str, assistant_id: str) -> Dict[str, Any]:
        """
        Wait for the assistant response using the streaming API.
        
        Args:
            thread_id: The thread ID
            assistant_id: The assistant ID
            
        Returns:
            Dict containing the model response
            
        Note:
            - The stream method will create a new run automatically
            - Handles the case where the run might require tool calls
        """
        import json
        
        # Function mapping for tool execution
        function_map = {
            "search": search,
            "refine_search": refine_search,
            "get_search_history": get_search_history,
            "get_tickers_dividend": get_tickers_dividend,
            "query_stocks": query_stocks,
            "query_ticker_concepts": query_ticker_concepts,
            "query_sec_filings": query_sec_filings,
            "query_sec_filing_sections": query_sec_filing_sections,
        }
        
        # Define event handler for the stream
        class EventHandler:
            def __init__(self):
                self.final_content = None
                self.error = None
                self.tool_calls_received = False
                self.run_id = None
                self._init = True  # Add this attribute to fix the 'EventHandler' object has no attribute '_init' error
            
            def on_run_created(self, run):
                self.run_id = run.id
                logger.info(f"Run created with ID: {run.id}")
            
            def on_tool_calls(self, tool_calls):
                self.tool_calls_received = True
                tool_outputs = []
                
                for tool_call in tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)
                    
                    logger.info(f"Executing function: {function_name} with args: {function_args}")
                    
                    if function_name in function_map:
                        try:
                            result = function_map[function_name](**function_args)
                            tool_outputs.append({
                                "tool_call_id": tool_call.id,
                                "output": json.dumps(result)
                            })
                            logger.info(f"Function {function_name} executed successfully")
                        except Exception as e:
                            error_message = f"Error executing function {function_name}: {str(e)}"
                            logger.error(error_message)
                            tool_outputs.append({
                                "tool_call_id": tool_call.id,
                                "output": json.dumps({"error": error_message})
                            })
                    else:
                        error_message = f"Function {function_name} not found"
                        logger.error(error_message)
                        tool_outputs.append({
                            "tool_call_id": tool_call.id,
                            "output": json.dumps({"error": error_message})
                        })
                
                return tool_outputs
            
            def on_text_created(self, text):
                self.final_content = text.value
            
            def on_error(self, error):
                self.error = f"Assistant run failed with error: {error}"
                logger.error(self.error)
        
        try:
            handler = EventHandler()
            
            logger.info(f"[_wait_for_assistant_response] Starting stream for thread_id={thread_id}")
            
            # Stream and create a new run in one step
            logger.info(f"[_wait_for_assistant_response] Creating and streaming run for thread_id={thread_id}")
            with self.client.beta.threads.runs.stream(
                assistant_id=assistant_id,
                thread_id=thread_id,
                event_handler=handler
            ) as stream:
                logger.info(f"[_wait_for_assistant_response] Stream connection established, waiting for completion")
                stream.until_done()
                # Get the run_id from the handler or stream
                run_id = handler.run_id or (stream.current_run.id if hasattr(stream, 'current_run') else None)
                logger.info(f"[_wait_for_assistant_response] Stream completed for run_id={run_id}")
                
            # After the stream is done, check if the run is in require_action status
            if run_id:
                run = self.client.beta.threads.runs.retrieve(
                    thread_id=thread_id,
                    run_id=run_id
                )
                
                logger.info(f"[_wait_for_assistant_response] Run status after streaming: {run.status}")
                
                # If the run is in require_action status, handle the required actions
                if run.status == "requires_action":
                    logger.info(f"[_wait_for_assistant_response] Run {run_id} requires action")
                    
                    # Get the required actions
                    required_action = run.required_action
                    if required_action and required_action.type == "submit_tool_outputs":
                        tool_calls = required_action.submit_tool_outputs.tool_calls
                        tool_outputs = []
                        
                        # Process each tool call
                        for tool_call in tool_calls:
                            function_name = tool_call.function.name
                            function_args = json.loads(tool_call.function.arguments)
                            
                            logger.info(f"Executing function: {function_name} with args: {function_args}")
                            
                            if function_name in function_map:
                                try:
                                    result = function_map[function_name](**function_args)
                                    tool_outputs.append({
                                        "tool_call_id": tool_call.id,
                                        "output": json.dumps(result)
                                    })
                                    logger.info(f"Function {function_name} executed successfully")
                                except Exception as e:
                                    error_message = f"Error executing function {function_name}: {str(e)}"
                                    logger.error(error_message)
                                    tool_outputs.append({
                                        "tool_call_id": tool_call.id,
                                        "output": json.dumps({"error": error_message})
                                    })
                            else:
                                error_message = f"Function {function_name} not found"
                                logger.error(error_message)
                                tool_outputs.append({
                                    "tool_call_id": tool_call.id,
                                    "output": json.dumps({"error": error_message})
                                })
                        
                        # Submit the tool outputs
                        if tool_outputs:
                            logger.info(f"[_wait_for_assistant_response] Submitting {len(tool_outputs)} tool outputs for run_id={run_id}")
                            for i, output in enumerate(tool_outputs):
                                logger.debug(f"[_wait_for_assistant_response] Tool output {i+1}: tool_call_id={output['tool_call_id']}, output={output['output'][:100]}...")
                            
                            self.client.beta.threads.runs.submit_tool_outputs(
                                thread_id=thread_id,
                                run_id=run_id,
                                tool_outputs=tool_outputs
                            )
                            logger.info(f"[_wait_for_assistant_response] Tool outputs submitted successfully for run_id={run_id}")
                            
                            # Continue waiting for the run to complete
                            return self._wait_for_assistant_response(thread_id, assistant_id)
            
            # Check for errors
            if handler.error:
                return {
                    "choices": [
                        {
                            "message": {
                                "role": "assistant",
                                "content": f"Error: {handler.error}"
                            },
                            "finish_reason": "stop",
                            "index": 0
                        }
                    ],
                    "created": int(time.time()),
                    "model": "openai-assistant",
                    "object": "chat.completion",
                    "error": handler.error,
                    "thread_id": thread_id,
                    "run_id": run_id
                }
            
            # Return the final content if available
            if handler.final_content:
                return {
                    "choices": [
                        {
                            "message": {
                                "role": "assistant",
                                "content": handler.final_content
                            },
                            "finish_reason": "stop",
                            "index": 0
                        }
                    ],
                    "created": int(time.time()),
                    "model": "openai-assistant",
                    "object": "chat.completion",
                    "thread_id": thread_id,
                    "run_id": run_id
                }
            
            # No content found
            return {
                "choices": [
                    {
                        "message": {
                            "role": "assistant",
                            "content": "No response generated."
                        },
                        "finish_reason": "stop",
                        "index": 0
                    }
                ],
                "created": int(time.time()),
                "model": "openai-assistant",
                "object": "chat.completion",
                "thread_id": thread_id,
                "run_id": run_id
            }
            
        except Exception as e:
            logger.error(f"Error in streaming: {str(e)}")
            return {
                "choices": [
                    {
                        "message": {
                            "role": "assistant",
                            "content": f"Error: {str(e)}"
                        },
                        "finish_reason": "stop",
                        "index": 0
                    }
                ],
                "created": int(time.time()),
                "model": "openai-assistant",
                "object": "chat.completion",
                "error": str(e),
                "thread_id": thread_id,
                "run_id": None
            }
