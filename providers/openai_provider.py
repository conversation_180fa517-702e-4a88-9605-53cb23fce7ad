import os
from typing import Dict, Any, List
from openai import OpenAI
from .base_provider import <PERSON><PERSON>rovider
from utils.circuit_breaker import with_circuit_breaker, with_retry, with_fallback
from utils.logging import logger
from config.settings import settings

class OpenAIProvider(ModelProvider):
    """
    Provider implementation for OpenAI API.
    """
    def __init__(self):
        self.client = OpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_API_URL')
        )

    @with_circuit_breaker('openai')
    @with_retry(max_attempts=3, min_wait=1, max_wait=10)
    def _chat_completion_impl(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Implementation of chat completion for OpenAI.
        
        Args:
            messages: List of message objects with role and content
            model: The model identifier to use
            **kwargs: Additional parameters to pass to the OpenAI API
            
        Returns:
            Dict containing the model response
        """
        try:
            stream = kwargs.pop('stream', False)
            
            # Extract function-related parameters
            functions = kwargs.pop('functions', None)
            function_call = kwargs.pop('function_call', None)
            
            # Prepare API call parameters
            api_params = {
                "model": model,
                "messages": messages,
                "stream": stream,
                **kwargs
            }
            
            # Add function calling parameters if provided
            if functions:
                api_params["functions"] = functions
                
                # Add function_call parameter if provided
                if function_call:
                    api_params["function_call"] = function_call
                elif kwargs.get('auto_functions', False):
                    # Use "auto" mode if auto_functions is True
                    api_params["function_call"] = "auto"
            
            response = self.client.chat.completions.create(**api_params)
            
            if stream:
                return response  # Return the streaming response directly
            else:
                return response.model_dump()
        except Exception as e:
            logger.error(f"OpenAI API error: {str(e)}")
            raise

    def chat_completion(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Generate a chat completion with error recovery.
        
        This method applies circuit breaker, retry, and fallback patterns
        to the chat completion implementation.
        
        Args:
            messages: List of message objects
            model: The model to use
            **kwargs: Additional arguments to pass to the provider
            
        Returns:
            Dict: The response from the provider
        """
        try:
            return self._chat_completion_impl(messages, model, **kwargs)
        except Exception as e:
            logger.warning(f"OpenAI provider failed: {str(e)}. Using fallback.")
            return self.fallback_response(messages, model, **kwargs)
