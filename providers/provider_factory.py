from typing import Dict, Type
from .base_provider import ModelProvider
from .openai_provider import OpenAIProvider
from .anthropic_provider import AnthropicProvider
from .deepseek_provider import DeepSeekProvider
from .openai_assistant_provider import OpenAIAssistantProvider

# Registry of available providers
PROVIDERS: Dict[str, Type[ModelProvider]] = {
    'deepseek': DeepSeekProvider,
    'openai': OpenAIProvider,
    'anthropic': AnthropicProvider,
    'openai_assistant': OpenAIAssistantProvider
}

def get_provider(provider_name: str) -> ModelProvider:
    """
    Factory function to get the appropriate provider instance.
    
    Args:
        provider_name: The name of the provider to use
        
    Returns:
        An instance of the requested provider
        
    Raises:
        ValueError: If the provider is not found
    """
    provider_class = PROVIDERS.get(provider_name.lower())
    if not provider_class:
        raise ValueError(f'Unknown provider: {provider_name}')
    
    return provider_class()
