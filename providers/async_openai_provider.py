import os
import j<PERSON>
from typing import Dict, Any, List
import httpx
from openai import Async<PERSON>penA<PERSON>
from .async_base_provider import <PERSON>yncModelProvider
from utils.circuit_breaker import with_circuit_breaker, with_retry, with_fallback
from utils.logging import logger
from config.settings import settings

class Async<PERSON><PERSON><PERSON><PERSON><PERSON>ider(AsyncModelProvider):
    """
    Async provider implementation for OpenAI API.
    """
    def __init__(self):
        api_key = os.getenv('OPENAI_API_KEY')
        base_url = os.getenv('OPENAI_API_URL')
        
        # Log API configuration for debugging
        if not api_key:
            logger.warning("OPENAI_API_KEY is not set or empty")
        else:
            logger.info(f"Using OpenAI API key: {api_key[:5]}...{api_key[-4:] if len(api_key) > 9 else ''}")
        
        logger.info(f"Using OpenAI API URL: {base_url}")
        
        self.client = AsyncOpenAI(
            api_key=api_key,
            base_url=base_url
        )

    async def _chat_completion_impl(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Async implementation of chat completion for OpenAI.
        
        Args:
            messages: List of message objects with role and content
            model: The model identifier to use
            **kwargs: Additional parameters to pass to the OpenAI API
            
        Returns:
            Dict containing the model response or an async generator for streaming
        """
        try:
            stream = kwargs.pop('stream', True)  # Default to streaming
            timeout = kwargs.pop('timeout', 30)  # Default timeout of 30 seconds
            
            # Extract function-related parameters
            functions = kwargs.pop('functions', None)
            function_call = kwargs.pop('function_call', None)
            
            # Extract custom parameters that shouldn't be passed to the API
            auto_functions = kwargs.pop('auto_functions', False)
            execute_functions = kwargs.pop('execute_functions', True)
            
            # Log function calling configuration
            if functions:
                logger.debug(f"Function calling enabled with {len(functions)} functions")
                logger.debug(f"Auto functions: {auto_functions}, Execute functions: {execute_functions}")
            
            # Prepare API call parameters
            api_params = {
                "model": model,
                "messages": messages,
                "stream": stream,
                "timeout": timeout,
                **kwargs
            }
            
            # Add function calling parameters if provided
            if functions:
                api_params["functions"] = functions
                
                # Add function_call parameter if provided
                if function_call:
                    api_params["function_call"] = function_call
                elif auto_functions:
                    # Use "auto" mode if auto_functions is True
                    api_params["function_call"] = "auto"
                    logger.debug(f"Using 'auto' function_call mode")
            
            logger.info(f"Making API request to {self.client.base_url} with model {model}, stream={stream}")
            
            try:
                response = await self.client.chat.completions.create(**api_params)
                logger.debug(f"API request successful, response type: {type(response).__name__}")
            except httpx.ConnectTimeout as e:
                logger.error(f"Connection timeout to {self.client.base_url}: {str(e)}")
                raise Exception(f"Connection timeout to API server: {str(e)}")
            except httpx.ReadTimeout as e:
                logger.error(f"Read timeout from {self.client.base_url}: {str(e)}")
                raise Exception(f"Read timeout from API server: {str(e)}")
            except httpx.ConnectError as e:
                logger.error(f"Connection error to {self.client.base_url}: {str(e)}")
                raise Exception(f"Cannot connect to API server: {str(e)}")
            except Exception as e:
                error_type = type(e).__name__
                error_message = str(e)
                logger.error(f"Unexpected error during API request: {error_type}: {error_message}")
                
                # Check if this is a connection error
                if "Connection" in error_type or "Connection" in error_message:
                    # Log more details about the connection
                    logger.error(f"Connection details: URL={self.client.base_url}, API Key present: {bool(self.client.api_key)}")
                    
                    # Check if we can ping the API endpoint
                    try:
                        import socket
                        from urllib.parse import urlparse
                        
                        parsed_url = urlparse(self.client.base_url)
                        host = parsed_url.netloc
                        port = 443 if parsed_url.scheme == 'https' else 80
                        
                        logger.info(f"Attempting to check connectivity to {host}:{port}")
                        
                        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        s.settimeout(5)
                        result = s.connect_ex((host, port))
                        s.close()
                        
                        if result == 0:
                            logger.info(f"Successfully connected to {host}:{port}")
                        else:
                            logger.error(f"Failed to connect to {host}:{port}, error code: {result}")
                    except Exception as conn_e:
                        logger.error(f"Error checking connectivity: {str(conn_e)}")
                
                raise
            
            if stream:
                # Ensure we're returning an async iterable
                async def response_generator():
                    try:
                        logger.debug(f"Starting to stream response with model {model}")
                        chunk_count = 0
                        async for chunk in response:
                            chunk_count += 1
                            if chunk_count % 10 == 0:  # Log every 10th chunk to avoid excessive logging
                                logger.debug(f"Streaming chunk {chunk_count} from model {model}")
                            yield chunk
                        logger.info(f"Completed streaming {chunk_count} chunks from model {model}")
                    except Exception as e:
                        logger.error(f"Error in streaming response from model {model}: {str(e)}")
                        raise
                
                logger.debug(f"Returning streaming generator for model {model}")
                return response_generator()  # Return an async generator
            else:
                # For non-streaming responses, ensure we return a dictionary
                logger.debug(f"Processing non-streaming response of type: {type(response)}")
                try:
                    # Convert response to dictionary
                    response_dict = response.model_dump()
                    logger.debug(f"Successfully converted response to dictionary")
                    # Log the full response structure for debugging
                    logger.debug(f"OpenAI API response structure: {json.dumps(response_dict, indent=2, default=str)}")
                    return response_dict
                except Exception as e:
                    logger.error(f"Error converting response to dictionary: {str(e)}")
                    # Fallback to manual conversion if model_dump fails
                    try:
                        # Try to access as a dictionary
                        if hasattr(response, 'choices'):
                            logger.debug(f"Manually extracting response data")
                            # Log the response structure for debugging
                            logger.debug(f"Response structure: choices={hasattr(response, 'choices')}, "
                                        f"message={hasattr(response.choices[0], 'message') if hasattr(response, 'choices') and response.choices else 'N/A'}")
                            
                            # Check if message exists and has required attributes
                            message = response.choices[0].message if hasattr(response, 'choices') and response.choices else None
                            if not message:
                                logger.warning("Message object is None or not accessible")
                                # Return a minimal valid response
                                return {
                                    "choices": [
                                        {
                                            "message": {
                                                "role": "assistant",
                                                "content": ""
                                            },
                                            "finish_reason": "stop",
                                            "index": 0
                                        }
                                    ],
                                    "created": getattr(response, 'created', 0),
                                    "model": getattr(response, 'model', model),
                                    "object": "chat.completion"
                                }
                            
                            # Extract role with fallback
                            role = message.role if hasattr(message, 'role') else "assistant"
                            
                            # Extract content with null check
                            content = ""
                            if hasattr(message, 'content') and message.content is not None:
                                content = message.content
                            else:
                                logger.warning("Content is None or not present in message")
                            
                            # Check for function_call
                            function_call_dict = {}
                            if hasattr(message, 'function_call') and message.function_call is not None:
                                function_call_dict = {"function_call": message.function_call}
                                logger.debug("Function call found in response")
                            
                            # Extract finish_reason with fallback
                            finish_reason = response.choices[0].finish_reason if hasattr(response.choices[0], 'finish_reason') else "stop"
                            
                            return {
                                "choices": [
                                    {
                                        "message": {
                                            "role": role,
                                            "content": content,
                                            **function_call_dict
                                        },
                                        "finish_reason": finish_reason,
                                        "index": 0
                                    }
                                ],
                                "created": getattr(response, 'created', 0),
                                "model": getattr(response, 'model', model),
                                "object": "chat.completion"
                            }
                        else:
                            logger.error(f"Response object does not have expected structure")
                            raise ValueError("Invalid response structure")
                    except Exception as e2:
                        logger.error(f"Error in fallback response conversion: {str(e2)}")
                        raise ValueError(f"Could not process response: {str(e)}, {str(e2)}")
        except Exception as e:
            logger.error(f"OpenAI API error: {str(e)}")
            raise

    # Apply circuit breaker and retry decorators
    # Note: These decorators need to be applied differently for async functions
    # We'll implement async versions of these decorators in a real implementation
    # For now, we'll use the async method directly
    async def chat_completion(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Generate a chat completion with error recovery.
        
        This method applies circuit breaker, retry, and fallback patterns
        to the chat completion implementation.
        
        Args:
            messages: List of message objects
            model: The model to use
            **kwargs: Additional arguments to pass to the provider
            
        Returns:
            Dict: The response from the provider
        """
        try:
            # In a real implementation, we would use async versions of the circuit breaker and retry decorators
            return await self._chat_completion_impl(messages, model, **kwargs)
        except Exception as e:
            logger.warning(f"OpenAI provider failed: {str(e)}. Using fallback.")
            return await self.fallback_response(messages, model, **kwargs)
