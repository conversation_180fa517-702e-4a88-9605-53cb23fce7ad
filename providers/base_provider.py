from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import json
from utils.circuit_breaker import with_circuit_breaker, with_retry, with_fallback
from utils.cache import get_cached_response, cache_response
from utils.logging import logger
from utils.function_registry import function_registry

class ModelProvider(ABC):
    """
    Abstract base class for model providers.
    """
    
    @abstractmethod
    def _chat_completion_impl(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Implementation of chat completion.
        
        Args:
            messages: List of message objects
            model: The model to use
            **kwargs: Additional arguments to pass to the provider
            
        Returns:
            Dict: The response from the provider
        """
        pass
    
    def chat_completion(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Generate a chat completion with error recovery and caching.
        
        This method applies circuit breaker, retry, and fallback patterns
        to the chat completion implementation. It also caches responses
        to improve performance and reduce API costs.
        
        Args:
            messages: List of message objects
            model: The model to use
            functions: Optional list of function definitions
            function_call: Optional control for function calling (auto, none, or specific function)
            **kwargs: Additional arguments to pass to the provider
            
        Returns:
            Dict: The response from the provider
        """
        # Check if streaming is requested
        stream = kwargs.get('stream', False)
        
        # Get functions if provided
        functions = kwargs.get('functions')
        if functions is None and kwargs.get('auto_functions', False):
            # Use all registered functions if auto_functions is True
            functions = function_registry.get_function_schemas()
            kwargs['functions'] = functions
        
        # Don't use cache for streaming responses or when using functions
        if not stream and not functions:
            # Try to get cached response
            provider_name = self.__class__.__name__.replace('Provider', '').lower()
            cached_response = get_cached_response(provider_name, model, messages, **kwargs)
            if cached_response:
                return cached_response
        
        # Get response from provider
        response = self._chat_completion_impl(messages, model, **kwargs)
        
        # Handle function call if present in response
        if not stream and self._has_function_call(response):
            response = self._handle_function_call(response, **kwargs)
        
        # Cache the response if not streaming and not using functions
        if not stream and not functions and not response.get('is_fallback', False):
            provider_name = self.__class__.__name__.replace('Provider', '').lower()
            cache_response(provider_name, model, messages, response, **kwargs)
        
        return response
        
    def _has_function_call(self, response: Dict[str, Any]) -> bool:
        """
        Check if a response contains a function call.
        
        Args:
            response: The response from the provider
            
        Returns:
            bool: True if the response contains a function call
        """
        if not response or 'choices' not in response:
            return False
            
        for choice in response['choices']:
            message = choice.get('message', {})
            if message.get('function_call'):
                return True
                
        return False
    
    def _handle_function_call(self, response: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Handle a function call in a response.
        
        Args:
            response: The response from the provider
            **kwargs: Additional arguments
            
        Returns:
            Dict: The updated response
        """
        # Check if function execution is enabled
        if not kwargs.get('execute_functions', True):
            return response
            
        try:
            # Get the function call
            choice = response['choices'][0]
            message = choice['message']
            function_call = message.get('function_call')
            
            if not function_call:
                return response
                
            # Extract function name and arguments
            function_name = function_call.get('name')
            arguments_str = function_call.get('arguments', '{}')
            
            try:
                arguments = json.loads(arguments_str)
            except json.JSONDecodeError:
                logger.error(f"Invalid function arguments: {arguments_str}")
                arguments = {}
            
            # Execute the function
            function_result = function_registry.execute_function(function_name, arguments)
            
            # Add function result to response
            if 'function_results' not in response:
                response['function_results'] = []
                
            response['function_results'].append({
                'name': function_name,
                'arguments': arguments,
                'result': function_result
            })
            
            return response
        except Exception as e:
            logger.error(f"Error handling function call: {str(e)}")
            return response
    
    def fallback_response(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Generate a fallback response when the primary provider fails.
        
        Args:
            messages: List of message objects
            model: The model to use
            **kwargs: Additional arguments to pass to the provider
            
        Returns:
            Dict: A fallback response
        """
        # Simple fallback that returns an error message
        return {
            "choices": [
                {
                    "message": {
                        "role": "assistant",
                        "content": "I'm sorry, but I'm having trouble connecting to the service. Please try again later."
                    },
                    "finish_reason": "stop",
                    "index": 0
                }
            ],
            "created": 0,
            "model": model,
            "object": "chat.completion",
            "is_fallback": True
        }
