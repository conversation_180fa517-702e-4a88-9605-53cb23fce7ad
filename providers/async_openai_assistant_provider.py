import os
import json
import time
import asyncio
import traceback
from typing import Dict, Any, List, Optional, AsyncGenerator
from openai.types.beta.threads.runs import RunStep, ToolCall
from openai import AsyncOpenAI, OpenAI, AssistantEventHandler, AsyncAssistantEventHandler
from typing_extensions import override
from .async_base_provider import AsyncModelProvider
from utils.logging import logger
from utils.assistant_utils import initialize_assistant_async, initialize_assistant_async_for_earning_report, initialize_assistant_async_for_stock_analyst
import datetime
from utils.sample_functions import search, refine_search, get_search_history, get_tickers_dividend, query_stocks, query_ticker_concepts, query_sec_filings, query_sec_filing_sections, query_stock_price_ratings
from utils.response_metadata import metadata_instructions
from config.settings import settings

class AsyncOpenAIAssistantProvider(AsyncModelProvider):
    """
    Async provider implementation for OpenAI Assistant API.
    """
    def __init__(self):
        # Initialize the OpenAI client with the v2 Assistants API header
        self.client = AsyncOpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_API_URL'),
            default_headers={"OpenAI-Beta": "assistants=v2"}
        )
        
        # Initialize assistant
        # Note: We need to initialize the assistant in an async context
        # This will be done when the first request is made
        self.assistant_id = None
        self._assistant_initialized = False
        
        # Store the current query classification
        self.current_classification = None
        
    async def _ensure_assistant_initialized(self):
        """
        Ensure the assistant is initialized.
        This is called before processing any request.
        """
        if not self._assistant_initialized:
            # Get assistant ID from Redis
            from utils.async_redis_client import async_redis
            from utils.assistant_utils import ASSISTANT_ID_KEY, EARNINGS_ASSISTANT_ID_KEY, STOCK_ANALYST_ASSISTANT_ID_KEY

            self.assistant_id = await async_redis.get(ASSISTANT_ID_KEY)
            if not self.assistant_id:
                logger.warning("Assistant ID not found in Redis. This should have been initialized during app startup.")
                # Fallback to initializing the assistant
                self.assistant_id = await initialize_assistant_async(self.client)
            
            self.earnings_assistant_id = await async_redis.get(EARNINGS_ASSISTANT_ID_KEY)
            if not self.earnings_assistant_id:
                logger.warning("Earnings assistant ID not found in Redis. This should have been initialized during app startup.")
                self.earnings_assistant_id = await initialize_assistant_async_for_earning_report(self.client)

            self.stock_analyst_assistant_id = await async_redis.get(STOCK_ANALYST_ASSISTANT_ID_KEY)
            if not self.stock_analyst_assistant_id:
                logger.warning("Stock analyst assistant ID not found in Redis. This should have been initialized during app startup.")
                self.stock_analyst_assistant_id = await initialize_assistant_async_for_stock_analyst(self.client)
            
            self._assistant_initialized = True
            logger.info(f"Using assistant ID: {self.assistant_id}, earnings assistant ID {self.earnings_assistant_id}, stock analyst assistant ID {self.stock_analyst_assistant_id}")

    async def _chat_completion_impl(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Async implementation of chat completion for OpenAI Assistant API.
        
        Args:
            messages: List of message objects with role and content
            model: The model identifier to use (ignored, using assistant's model)
            **kwargs: Additional parameters
                - thread_id: Optional thread ID to continue conversation
                - stream: Whether to stream the response
                - auth0_sub: Optional user identifier for user_query tracking

        Returns:
            Dict containing the model response or an async generator for streaming
        """
        try:
            # Ensure assistant is initialized
            await self._ensure_assistant_initialized()
            
            # Get parameters
            thread_id = kwargs.pop('thread_id', None)
            stream = kwargs.pop('stream', False)
            classification = kwargs.pop('classification', None)
            # Store the classification for later use in tool calls
            self.current_classification = classification
            auth0_sub = kwargs.pop('auth0_sub', None)  # Extract auth0_sub from kwargs
            # By default, use the generic assistant 
            assistant_id = kwargs.pop('assistant_id', self.assistant_id)
            if classification and classification['type'] == 'Summarising Company Financial Filings':
                # Use earnings assistant ID for this classification
                assistant_id = self.earnings_assistant_id
                logger.info("Using earnings assistant ID for summarising company financial filings")
            elif classification and classification['type'] == 'Analysing Stocks':
                # Use stock analyst assistant ID for this classification
                assistant_id = self.stock_analyst_assistant_id
                logger.info("Using stock analyst assistant ID for analysing stocks")
            
            # Create or retrieve assistant
            if not assistant_id:
                # Create a new assistant if ID not provided
                assistant = await self.client.beta.assistants.create(
                    name="Temporary Assistant",
                    instructions="You are a helpful assistant.",
                    model=model or "gpt-4-turbo-preview"
                )
                assistant_id = assistant.id
                logger.info(f"Created new assistant with ID: {assistant_id}")
            
            # Thread ID must be provided by the service layer
            if not thread_id:
                logger.error("Thread ID not provided. Thread creation should happen in the service layer.")
                raise ValueError("Thread ID is required. Thread creation should happen in the service layer.")
            
            file_id = None
            if classification and classification['type'] == 'Summarising Company Financial Filings' and 'ticker' in classification:
                from utils.sec_db_functions import query_sec_filing_file_id_from_db
                fiscal_year = None
                fiscal_quarter = None
                if 'fiscal_year' in classification:
                    fiscal_year = classification['fiscal_year']
                if 'fiscal_quarter' in classification:
                    fiscal_quarter = classification['fiscal_quarter']
                file_id = await query_sec_filing_file_id_from_db(
                    ticker=classification['ticker'],
                    fiscal_year=fiscal_year,
                    fiscal_quarter=fiscal_quarter,
                    retrieve_file=True
                )
                logger.info(f"File ID for SEC filing: {file_id}")

            # Check if there's an active run for this thread
            try:
                runs = await self.client.beta.threads.runs.list(thread_id=thread_id)
                active_run = None
                
                # Find any active runs
                for run in runs.data:
                    if run.status in ["queued", "in_progress", "requires_action"]:
                        active_run = run
                        logger.info(f"Found existing active run {active_run.id} for thread {thread_id}")
                        break
                
                # If no active run found in the list, double-check the most recent run directly
                if not active_run and runs.data:
                    most_recent_run = runs.data[0]  # Assuming runs are sorted by recency
                    # Explicitly check the status of the most recent run
                    run_details = await self.client.beta.threads.runs.retrieve(
                        thread_id=thread_id,
                        run_id=most_recent_run.id
                    )
                    logger.info(f"Double-checking most recent run {most_recent_run.id}, status: {run_details.status}")
                    
                    if run_details.status in ["queued", "in_progress", "requires_action"]:
                        active_run = run_details
                        logger.info(f"Found active run {active_run.id} with status {active_run.status} through direct check")

                # If there's an active run, try to cancel it first if it's not in a final state
                max_wait_time = 30  # Maximum wait time in seconds
                wait_time = 0
                wait_interval = 2  # Wait interval in seconds

                while active_run is not None and wait_time < max_wait_time:
                    # If the run is in a state that can be cancelled, cancel it
                    if active_run.status in ["queued", "in_progress"]:
                        try:
                            logger.info(f"Cancelling existing run {active_run.id}")
                            await self.client.beta.threads.runs.cancel(
                                thread_id=thread_id,
                                run_id=active_run.id
                            )
                            # Wait a moment for the cancellation to take effect
                            await asyncio.sleep(1)
                        except Exception as e:
                            logger.warning(f"Failed to cancel run {active_run.id}: {str(e)}")
                    
                    # For runs in "requires_action" state, we need to submit empty tool outputs to complete them
                    elif active_run.status == "requires_action":
                        try:
                            logger.info(f"Completing existing run {active_run.id} that requires action")
                            required_action = active_run.required_action
                            if required_action and required_action.type == "submit_tool_outputs":
                                tool_calls = required_action.submit_tool_outputs.tool_calls
                                tool_outputs = []
                                
                                # Create empty outputs for each tool call
                                for tool_call in tool_calls:
                                    tool_outputs.append({
                                        "tool_call_id": tool_call.id,
                                        "output": json.dumps({"error": "Operation cancelled by user"})
                                    })
                                
                                # Submit the tool outputs to complete the run
                                await self.client.beta.threads.runs.submit_tool_outputs(
                                    thread_id=thread_id,
                                    run_id=active_run.id,
                                    tool_outputs=tool_outputs
                                )
                                # Wait a moment for the submission to take effect
                                await asyncio.sleep(1)
                        except Exception as e:
                            logger.warning(f"Failed to complete run {active_run.id}: {str(e)}")

                        await asyncio.sleep(1)

                    # runs = await self.client.beta.threads.runs.list(thread_id=thread_id)
                    # Check if the run is still active
                    run = await self.client.beta.threads.runs.retrieve(
                        thread_id=thread_id,
                        run_id=active_run.id
                    )

                    if run.status not in ["queued", "in_progress", "requires_action"]:
                        logger.info(f"Run {active_run.id} is now in state {run.status}")
                        active_run = None
                        break
                    else:   
                        logger.info(f"Waiting for run {active_run.id} to complete, current status: {run.status}")
                        active_run = run
                        await asyncio.sleep(wait_interval)
                        wait_time += wait_interval
                    
                # If there's still an active run, we can't proceed with a new run
                # Instead, we'll return an error message
                if active_run:
                    logger.error(f"Thread {thread_id} still has an active run {active_run.id} that can't be cancelled")
                    raise Exception(f"Thread {thread_id} already has an active run {active_run.id} that can't be cancelled")
                else:
                    # Before adding messages, do one final check to ensure no new runs have been created
                    final_check = await self.client.beta.threads.runs.list(thread_id=thread_id, limit=1)
                    if final_check.data and final_check.data[0].status in ["queued", "in_progress", "requires_action"]:
                        active_run = final_check.data[0]
                        logger.error(f"New active run {active_run.id} detected during final check")
                        raise Exception(f"Thread {thread_id} has a new active run {active_run.id} that was created during cancellation")
                        
                    # Add messages to thread
                    for message in messages:
                        if message['role'] == 'user':
                            today_date = datetime.datetime.now().strftime("%Y %B %-d")
                            logger.debug(f"For content, the today_date is: {today_date}")
                            # fixed prompt
                            content = f"{message['content']}. Today is {today_date} {settings.OPENAI_ASSISTANT_FIXED_PROMPT}"
                            try:
                                if file_id:
                                    logger.info(f"Adding message with attachments file_id: {file_id}")
                                    await self.client.beta.threads.messages.create(
                                        thread_id=thread_id,
                                        role="user",
                                        content=f"{content}. All the analysis is based on the attached file(file_id={file_id}). Extract and summarize the latest earnings figures and financial highlights STRICTLY from this file.",
                                        attachments=[{
                                            "file_id": file_id,
                                            "tools": [{"type": "file_search"}]
                                        }]
                                    )
                                else:
                                    await self.client.beta.threads.messages.create(
                                        thread_id=thread_id,
                                        role="user",
                                        content=content
                                    )
                                logger.info(f"message content: {content}")
                            except Exception as e:
                                error_str = str(e)
                                # Check if this is the specific error about active runs
                                if "Can't add messages to thread" in error_str and "while a run" in error_str and "is active" in error_str:
                                    logger.info(f"Detected attempt to add message while a run is active: {error_str}")
                                    # Extract run ID from error message for debugging
                                    import re
                                    run_id_match = re.search(r"run_([\w]+) is active", error_str)
                                    if run_id_match:
                                        run_id = f"run_{run_id_match.group(1)}"
                                        logger.error(f"Detected active run {run_id} that wasn't found in previous checks")
                                        
                                        # Try to get details about this run
                                        try:
                                            run_details = await self.client.beta.threads.runs.retrieve(
                                                thread_id=thread_id,
                                                run_id=run_id
                                            )
                                            logger.error(f"Run {run_id} details: status={run_details.status}, created_at={run_details.created_at}")
                                        except Exception as detail_error:
                                            logger.error(f"Failed to get details for run {run_id}: {str(detail_error)}")
                                    
                                    # Return a special generator that yields a waiting message
                                    return self._create_waiting_response_generator(thread_id)
                                else:
                                    # For other errors, re-raise
                                    raise
            except Exception as e:
                logger.error(f"Error checking for active runs: {str(e)}")
                # If we failed to check for active runs, we should assume there might be one
                # and not create a new run to avoid conflicts
                raise Exception(f"Failed to check for active runs: {str(e)}. Cannot safely create new run.")
            
            # If streaming, return a generator
            # if stream:
            return self._stream_assistant_response(thread_id, assistant_id, auth0_sub)
            
            # Otherwise, wait for completion and return messages
            # return await self._wait_for_assistant_response(thread_id, assistant_id)
            
        except Exception as e:
            logger.error(f"OpenAI Assistant API error: {str(e)}")
            raise

    def _create_waiting_response_generator(self, thread_id: str) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Create a generator that yields a waiting message for when a run is already active.
        
        Args:
            thread_id: The thread ID
            
        Returns:
            AsyncGenerator yielding a waiting message
        """
        async def generate():
            # Send a message indicating that a run is already active
            waiting_message = {
                "content": "You have a query ongoing, please wait a moment.",
                "thread_id": thread_id,
                "run_id": None,
                "timestamp": time.time(),
                "is_waiting_message": True
            }
            logger.info(f"Sending waiting message for active run in thread {thread_id}")
            yield waiting_message
            
            # Send a done message to indicate the end of the stream
            done_message = {
                "content": "[DONE]",
                "thread_id": thread_id,
                "run_id": None,
                "timestamp": time.time()
            }
            logger.info("Sending done message for waiting response")
            yield done_message
        
        return generate()
    
    def _stream_assistant_response(self, thread_id: str, assistant_id: str, auth0_sub: Optional[str] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream the assistant response.
        
        Args:
            thread_id: The thread ID
            assistant_id: The assistant ID
            auth0_sub: Optional user identifier for user_query tracking

        Returns:
            AsyncGenerator yielding response chunks
        """
        import json
        
        # Function mapping for tool execution
        function_map = {
            "search": search,
            "refine_search": refine_search,
            "get_search_history": get_search_history,
            "get_tickers_dividend": get_tickers_dividend,
            "query_stocks": query_stocks,
            "query_ticker_concepts": query_ticker_concepts,
            "query_sec_filings": query_sec_filings,
            "query_sec_filing_sections": query_sec_filing_sections,
            "query_stock_price_ratings": query_stock_price_ratings
        }
        
        # Create a generator to yield results
        async def generate():
            # Define event handler for the stream
            class EventHandler(AsyncAssistantEventHandler):
                def __init__(self, provider, auth0_sub=None):
                    # Initialize the base class
                    super().__init__()
                    # Queue to store deltas for yielding
                    self.delta_queue = asyncio.Queue()
                    # Store the current run ID
                    self.current_run_id = None
                    # Add a variable to store the thread ID
                    self.current_thread_id = None
                    # Store the stream reference
                    self._stream = None
                    # Store the provider directly
                    self._provider = provider
                    # Flag to track if run_created has been called
                    self.run_created_called = False
                    # Store the auth0_sub for user_query tracking
                    self.auth0_sub = auth0_sub

                @override
                async def on_event(self, event):
                    if event.event == "thread.run.created":
                        logger.info(f"[ThreadRunCreated] event detected! Run ID: {event.data.id}")
                        # Store the run ID for later use
                        self.current_run_id = event.data.id
                        # Store the thread ID for later use
                        self.current_thread_id = event.data.thread_id
                        self.run_created_called = True
                        
                        # Update or create a query record in the database
                        try:
                            # Get the thread ID from the run
                            thread_id = event.data.thread_id
                            if thread_id:
                                # Get the OpenAI client from the parent provider
                                client = None
                                if hasattr(self, '_provider') and hasattr(self._provider, 'client'):
                                    client = self._provider.client
                                        
                                if not client:
                                    # Fallback to creating a new client
                                    from openai import AsyncOpenAI
                                    client = AsyncOpenAI(
                                        api_key=os.getenv('OPENAI_API_KEY'),
                                        base_url=os.getenv('OPENAI_API_URL'),
                                        default_headers={"OpenAI-Beta": "assistants=v2"}
                                    )
                                    logger.info("[on_event] Created new OpenAI client as fallback")
                                    
                                # Get the last user message from the thread
                                try:
                                    messages = await client.beta.threads.messages.list(
                                        thread_id=thread_id,
                                        order="desc",
                                        limit=10
                                    )
                                    
                                    user_query = None
                                    for message in messages.data:
                                        if message.role == "user":
                                            # Extract the user's query
                                            user_query = message.content[0].text.value
                                            break
                                    
                                    if user_query:
                                        # Store in database using async SQL
                                        from sqlalchemy import text as sql_text
                                        from sqlalchemy.ext.asyncio import create_async_engine
                                        from config.settings import get_database_url
                                        import json
                                        
                                        # Get the async database URL
                                        db_url = get_database_url('user_data', async_mode=True)
                                        engine = create_async_engine(db_url)
                                        
                                        try:
                                            # Use an async context manager for the connection
                                            async with engine.begin() as conn:
                                                # First check if there's a pending record with NULL run_id for this thread
                                                result = await conn.execute(
                                                    sql_text("SELECT id FROM query WHERE thread_id = :thread_id AND run_id IS NULL AND status = 'pending' AND user_query = :origin_query"),
                                                    {"thread_id": thread_id, "origin_query": self._provider.current_classification.get('origin_query', user_query)}
                                                )
                                                pending_query = result.fetchone()
                                                
                                                if pending_query:
                                                    # Update the existing record with the run_id
                                                    await conn.execute(
                                                        sql_text("UPDATE query SET run_id = :run_id WHERE id = :id"),
                                                        {"run_id": event.data.id, "id": pending_query[0]}
                                                    )
                                                    logger.info(f"[on_event] Updated existing query record with run_id {event.data.id} for thread {thread_id}")
                                                    
                                                    # If auth0_sub is available, check if a user_query record exists and update it if needed
                                                    if hasattr(self, 'auth0_sub') and self.auth0_sub:
                                                        try:
                                                            # Check if a user_query record already exists
                                                            result = await conn.execute(
                                                                sql_text("SELECT id FROM user_query WHERE uid = :uid AND query_id = :query_id AND status = 'pending'"),
                                                                {"uid": self.auth0_sub, "query_id": pending_query[0]}
                                                            )
                                                            user_query_record = result.fetchone()
                                                            
                                                            if not user_query_record:
                                                                # Create a user_query record if it doesn't exist
                                                                await conn.execute(
                                                                    sql_text("""
                                                                        INSERT INTO user_query (uid, query_id, query_time, status)
                                                                        VALUES (:uid, :query_id, NOW(), 'pending')
                                                                    """),
                                                                    {
                                                                        "uid": self.auth0_sub,
                                                                        "query_id": pending_query[0]
                                                                    }
                                                                )
                                                                logger.info(f"[on_event] Created user_query record for user {self.auth0_sub}, query {pending_query[0]}")
                                                        except Exception as e:
                                                            logger.error(f"[on_event] Error checking/creating user_query record: {str(e)}")
                                                            logger.error(f"[on_event] Exception details: {traceback.format_exc()}")
                                        finally:
                                            await engine.dispose()
                                except Exception as e:
                                    logger.error(f"[on_event] Error retrieving messages or storing in database: {str(e)}")
                                    logger.error(f"[on_event] Exception details: {traceback.format_exc()}")
                        except Exception as e:
                            logger.error(f"[on_event] Error updating/creating query record: {str(e)}")
                            logger.error(f"[on_event] Exception details: {traceback.format_exc()}")
                    if event.event == "thread.run.in_progress":
                        # Store the run ID for later use
                        self.current_run_id = event.data.id
                        # Store the thread ID for later use
                        self.current_thread_id = event.data.thread_id
                @override
                async def on_tool_call_created(self, tool_call: ToolCall) -> None:
                    if tool_call.type == 'function':
                        logger.debug(f"[on_tool_call_created]: {tool_call.type} - {tool_call.function.name} - {tool_call.function.arguments}")

                @override
                async def on_tool_call_done(self, tool_call):
                    logger.info(f"[on_tool_call_done]: {tool_call.type}")
                    if tool_call.type == 'function':
                        logger.info(f"[on_tool_call_done]: function - {tool_call.function.name} - argument: {tool_call.function.arguments}")
                    else:
                        logger.info(f"[on_tool_call_done] Ignore for {tool_call.type}")
                        return
                    
                    try:
                        # Get the thread ID and run ID from the current context
                        run_id = getattr(self, 'current_run_id', None)
                        thread_id = getattr(self, 'current_thread_id', None)
                        if not run_id:
                            logger.error("[on_tool_call_done] No run_id available in handler")
                            return
                        if not thread_id:
                            logger.error("[on_tool_call_done] No thread_id available in handler")
                            return
                            
                        # Get the OpenAI client from the parent provider
                        client = None
                        if hasattr(self, '_provider') and hasattr(self._provider, 'client'):
                            client = self._provider.client
                                
                        if not client:
                            # Fallback to creating a new client
                            from openai import AsyncOpenAI
                            client = AsyncOpenAI(
                                api_key=os.getenv('OPENAI_API_KEY'),
                                base_url=os.getenv('OPENAI_API_URL'),
                                default_headers={"OpenAI-Beta": "assistants=v2"}
                            )
                            logger.info("[on_tool_call_done] Created new OpenAI client as fallback")
                        
                        # Update the query record in the database with function call details
                        if thread_id and run_id and tool_call:
                            from sqlalchemy import text as sql_text
                            from sqlalchemy.ext.asyncio import create_async_engine
                            from config.settings import get_database_url
                            import json
                            
                            # Get the async database URL
                            db_url = get_database_url('user_data', async_mode=True)
                            engine = create_async_engine(db_url)
                            
                            try:
                                # Use an async context manager for the connection
                                async with engine.begin() as conn:
                                    # First check if the record exists
                                    result = await conn.execute(
                                        sql_text("SELECT id, resources FROM query WHERE thread_id = :thread_id AND run_id = :run_id"),
                                        {"thread_id": thread_id, "run_id": run_id}
                                    )
                                    record = result.fetchone()
                                    
                                    if record:
                                        # Get current resources
                                        current_resources = []
                                        if record[1]:  # resources column
                                            try:
                                                current_resources = json.loads(record[1])
                                            except:
                                                current_resources = []
                                        
                                        # Add the function call details to resources
                                        if tool_call.type == 'function':
                                            function_call_details = {
                                                "function_name": tool_call.function.name,
                                                "arguments": tool_call.function.arguments,
                                                "timestamp": time.time()
                                            }
                                        current_resources.append(function_call_details)
                                        
                                        # Update the record with new resources and status
                                        await conn.execute(
                                            sql_text("UPDATE query SET resources = :resources, status = 'processing' WHERE thread_id = :thread_id AND run_id = :run_id"),
                                            {
                                                "resources": json.dumps(current_resources),
                                                "thread_id": thread_id,
                                                "run_id": run_id
                                            }
                                        )
                                        logger.info(f"[on_tool_call_done] Updated query record with function call details for thread {thread_id}, run {run_id}")
                            finally:
                                await engine.dispose()
                    except Exception as e:
                        logger.error(f"[on_tool_call_done] Error updating query record: {str(e)}")
                        logger.error(f"[on_tool_call_done] Exception details: {traceback.format_exc()}")
                    
                    # Process metadata if it's the metadata function
                    if tool_call.type == 'function' and tool_call.function and tool_call.function.name == "provide_response_metadata":
                        try:
                            # Parse the function arguments
                            metadata = json.loads(tool_call.function.arguments)

                            # Store the metadata for later use
                            reference_urls = metadata.get("reference_urls", [])
                            # Convert old format to new format if needed
                            processed_urls = []
                            for ref in reference_urls:
                                if isinstance(ref, str):
                                    # Old format - just URL strings
                                    processed_urls.append({
                                        "url": ref,
                                        "title": "Reference",
                                        "snippet": ""
                                    })
                                else:
                                    # New format - objects with url, title, snippet
                                    processed_url = {
                                        "url": ref.get("url", ""),
                                        "title": ref.get("title", "Reference"),
                                        "snippet": ref.get("snippet", "")
                                    }
                                    processed_urls.append(processed_url)

                            self.reference_urls = processed_urls
                            self.follow_up_questions = metadata.get("follow_up_questions", [])

                            # Log the extracted metadata
                            logger.debug(f"Extracted metadata: URLs: {self.reference_urls}, Questions: {self.follow_up_questions}")

                            # Add metadata to the queue in a special format for the client
                            metadata_message = {
                                "type": "metadata",
                                "reference_urls": self.reference_urls,
                                "follow_up_questions": self.follow_up_questions,
                                "thread_id": thread_id,
                                "run_id": self.current_run_id,
                                "timestamp": time.time()
                            }
                            await self.delta_queue.put(metadata_message)
                        except Exception as e:
                            logger.error(f"Error processing metadata function call: {str(e)}")
                            logger.error(f"Function arguments: {tool_call.function.arguments}")
                
                @override
                async def on_text_created(self, text):
                    # For our current API, we'll add the complete message to the queue
                    # Get run_id from the current run context
                    run_id = None
                    if hasattr(self, 'current_run_id') and self.current_run_id:
                        run_id = self.current_run_id
                    
                    logger.info(f"[on_text_created] ${text.value}")
                    
                    # Add to delta queue for yielding
                    delta_message = {
                        "type": "progress",
                        "content": "Generating your answer ...",
                        "thread_id": thread_id,
                        "run_id": run_id,
                        "timestamp": time.time()
                    }
                    await self.delta_queue.put(delta_message)
                    logger.info(f"[on_text_created] Added to queue: {json.dumps(delta_message)}")
                
                @override
                async def on_text_delta(self, delta, snapshot):
                    # Add each delta to the queue as it comes in for a more responsive experience
                    
                    # Check if delta has annotations and if any of them are FileCitationDeltaAnnotation with non-empty file_citation
                    if hasattr(delta, 'annotations') and delta.annotations is not None:
                        for annotation in delta.annotations:
                            if hasattr(annotation, 'type') and annotation.type == 'file_citation':
                                if hasattr(annotation, 'file_citation') and annotation.file_citation is not None:
                                    # Log the annotation value for debugging before ignoring
                                    logger.debug(f"[on_text_delta] Ignoring delta with FileCitationDeltaAnnotation: {annotation}")
                                    return

                    # Get run_id from the current run context
                    run_id = None
                    if hasattr(self, 'current_run_id') and self.current_run_id:
                        run_id = self.current_run_id
                    
                    # Enhanced logging for debugging
                    logger.debug(f"[on_text_delta] Received delta: '{delta.value if delta.value else 'empty'}', thread_id={thread_id}, run_id={run_id}")
                    logger.debug(f"[on_text_delta] Delta type: {type(delta).__name__}, value type: {type(delta.value).__name__ if delta.value else 'None'}")
                    logger.debug(f"[on_text_delta] Snapshot: {snapshot.value if hasattr(snapshot, 'value') else 'No value attribute'}")
                    
                    # Only add non-empty deltas to the queue
                    if delta.value:
                        delta_message = {
                            "content": delta.value,
                            "thread_id": thread_id,
                            "run_id": run_id,
                            "timestamp": time.time()
                        }
                        await self.delta_queue.put(delta_message)
                        logger.debug(f"[on_text_delta] Added to queue: {json.dumps(delta_message)}")
                
                @override
                async def on_text_done(self, text):
                    logger.debug(f"[on_text_done] The final text is: {text}")
                    
                    try:
                        # Get the thread ID and run ID from the current context
                        run_id = getattr(self, 'current_run_id', None)
                        thread_id = getattr(self, 'current_thread_id', None)

                        if not run_id:
                            logger.error("[on_text_done] No run_id available in handler")
                            return
                        if not thread_id:
                            logger.error("[on_text_done] No thread_id available in handler")
                            return

                            
                        # Get the OpenAI client from the parent provider
                        client = None
                        if hasattr(self, '_provider') and hasattr(self._provider, 'client'):
                            client = self._provider.client
                                
                        if not client:
                            # Fallback to creating a new client
                            from openai import AsyncOpenAI
                            client = AsyncOpenAI(
                                api_key=os.getenv('OPENAI_API_KEY'),
                                base_url=os.getenv('OPENAI_API_URL'),
                                default_headers={"OpenAI-Beta": "assistants=v2"}
                            )
                            logger.info("[on_text_done] Created new OpenAI client as fallback")
                            
                        if thread_id and run_id and text:
                            # Update the query record in the database
                            from sqlalchemy import text as sql_text
                            from sqlalchemy.ext.asyncio import create_async_engine
                            from config.settings import get_database_url
                            
                            # Get the async database URL
                            db_url = get_database_url('user_data', async_mode=True)
                            engine = create_async_engine(db_url)
                            
                            try:
                                # Use an async context manager for the connection
                                async with engine.begin() as conn:
                                    # First check if the record exists
                                    result = await conn.execute(
                                        sql_text("SELECT id, user_query FROM query WHERE thread_id = :thread_id AND run_id = :run_id"),
                                        {"thread_id": thread_id, "run_id": run_id}
                                    )
                                    record = result.fetchone()
                                    
                                    if record:
                                        # Update the existing record
                                        await conn.execute(
                                            sql_text("UPDATE query SET llm_answer = :llm_answer WHERE thread_id = :thread_id AND run_id = :run_id"),
                                            {"llm_answer": text.value, "thread_id": thread_id, "run_id": run_id}
                                        )
                                        logger.info(f"[on_text_done] Updated query record with answer for thread {thread_id}, run {run_id}")

                                        # If auth0_sub is available, update the user_query record status to 'completed'
                                        # if hasattr(self, 'auth0_sub') and self.auth0_sub:
                                        #     try:
                                        #         # Update the user_query record
                                        #         await conn.execute(
                                        #             sql_text("""
                                        #                 UPDATE user_query 
                                        #                 SET status = 'completed', completed_time = NOW() 
                                        #                 WHERE uid = :uid AND query_id = :query_id AND status = 'pending'
                                        #             """),
                                        #             {
                                        #                 "uid": self.auth0_sub,
                                        #                 "query_id": record[0]
                                        #             }
                                        #         )
                                        #         logger.info(f"[on_text_done] Updated user_query record status to completed for user {self.auth0_sub}, query {record[0]}")
                                        #     except Exception as e:
                                        #         logger.error(f"[on_text_done] Error updating user_query record: {str(e)}")
                                        #         logger.error(f"[on_text_done] Exception details: {traceback.format_exc()}")
                                        pass
                                    else:
                                        # If no record exists (shouldn't happen if on_run_created worked properly)
                                        # Get the last user message from the thread
                                        try:
                                            messages = await client.beta.threads.messages.list(
                                                thread_id=thread_id,
                                                order="desc",
                                                limit=10
                                            )
                                            
                                            user_query = None
                                            for message in messages.data:
                                                if message.role == "user":
                                                    # Extract the user's query
                                                    user_query = message.content[0].text.value
                                                    break
                                            
                                            if user_query:
                                                # Create a new record
                                                await conn.execute(
                                                    sql_text("""
                                                        INSERT INTO query (thread_id, run_id, user_query, llm_answer, status, model)
                                                        VALUES (:thread_id, :run_id, :user_query, :llm_answer, 'processing', :model)
                                                    """),
                                                    {
                                                        "thread_id": thread_id,
                                                        "run_id": run_id,
                                                        "user_query": user_query,
                                                        "llm_answer": text.value,
                                                        "model": run.model or "gpt-4-turbo"
                                                    }
                                                )
                                                logger.info(f"[on_text_done] Created new query record for thread {thread_id}, run {run_id}")
                                        except Exception as e:
                                            logger.error(f"[on_text_done] Error retrieving messages: {str(e)}")
                            finally:
                                await engine.dispose()
                    except Exception as e:
                        logger.error(f"[on_text_done] Error updating query in database: {str(e)}")
                        logger.error(f"[on_text_done] Exception details: {traceback.format_exc()}")

                @override
                async def on_end(self):
                    """
                    Handle the end of a run.
                    
                    Args:
                        run: The run object
                    """
                    try:
                        # Get the run ID and thread ID
                        run_id = getattr(self, 'current_run_id', None)
                        thread_id = getattr(self, 'current_thread_id', None)
                        
                        if not run_id:
                            logger.error("[on_end] No run_id available in handler")
                            return
                        if not thread_id:
                            logger.error("[on_end] No thread_id available in handler")
                            return
                            
                        # Get the OpenAI client from the parent provider
                        client = None
                        if hasattr(self, '_provider') and hasattr(self._provider, 'client'):
                            client = self._provider.client
                                
                        if not client:
                            # Fallback to creating a new client
                            from openai import AsyncOpenAI
                            client = AsyncOpenAI(
                                api_key=os.getenv('OPENAI_API_KEY'),
                                base_url=os.getenv('OPENAI_API_URL'),
                                default_headers={"OpenAI-Beta": "assistants=v2"}
                            )
                            logger.info("[on_end] Created new OpenAI client as fallback")
                            
                        # Retrieve the run information to get token usage
                        run_details = await client.beta.threads.runs.retrieve(
                            thread_id=thread_id,
                            run_id=run_id
                        )

                        # Check if the run is in a terminal state before updating token usage
                        terminal_states = ["completed", "failed", "cancelled", "incomplete", "expired"]
                        if run_details.status not in terminal_states:
                            logger.info(f"[on_end] Run {run_id} is in non-terminal state: {run_details.status}. Skipping token usage update.")
                            return
                        
                        # Extract token usage information
                        if hasattr(run_details, 'usage') and run_details.usage:
                            usage = run_details.usage
                            prompt_tokens = usage.prompt_tokens
                            completion_tokens = usage.completion_tokens
                            total_tokens = usage.total_tokens
                            # Get the model used
                            model = run_details.model
                            
                            logger.info(f"[on_end] prompt_token: {prompt_tokens}, completion_tokens: {completion_tokens}, total_tokens: {total_tokens} via model {model}")
                            # Calculate cost based on model and token usage
                            from config.constants import MODEL_METRICS
                            
                            # Map the model name to the format used in MODEL_METRICS
                            model_key = f"openai/{model}"
                            if model_key in MODEL_METRICS:
                                input_cost_per_million = MODEL_METRICS[model_key]['input_cost_per_million']
                                output_cost_per_million = MODEL_METRICS[model_key]['output_cost_per_million']
                                
                                # Calculate cost based on separate input and output token prices
                                input_cost = prompt_tokens * (input_cost_per_million / 1_000_000)
                                output_cost = completion_tokens * (output_cost_per_million / 1_000_000)
                                cost = input_cost + output_cost
                            else:
                                # Default cost calculation if model not found in MODEL_METRICS
                                # Using gpt-4-turbo as fallback
                                input_cost_per_million = MODEL_METRICS.get('openai/gpt-4-turbo', {}).get('input_cost_per_million', 10.0)
                                output_cost_per_million = MODEL_METRICS.get('openai/gpt-4-turbo', {}).get('output_cost_per_million', 30.0)
                                
                                # Calculate cost based on separate input and output token prices
                                input_cost = prompt_tokens * (input_cost_per_million / 1_000_000)
                                output_cost = completion_tokens * (output_cost_per_million / 1_000_000)
                                cost = input_cost + output_cost
                                
                                logger.warning(f"[on_end] Model {model} not found in MODEL_METRICS, using default input_cost_per_million: {input_cost_per_million}, output_cost_per_million: {output_cost_per_million}")
                            
                            # Log token usage and cost
                            logger.info(f"[on_end] Run {run_id} token usage: prompt_tokens={prompt_tokens}, completion_tokens={completion_tokens}, total_tokens={total_tokens}")
                            logger.info(f"[on_end] Run {run_id} cost: ${cost:.6f} (model: {model}, input_cost_per_million: {input_cost_per_million}, output_cost_per_million: {output_cost_per_million})")
                            
                            # Update the database record with token usage and cost
                            try:
                                from sqlalchemy import text as sql_text
                                from sqlalchemy.ext.asyncio import create_async_engine
                                from config.settings import get_database_url
                                
                                # Get the async database URL
                                db_url = get_database_url('user_data', async_mode=True)
                                engine = create_async_engine(db_url)
                                
                                try:
                                    # Use an async context manager for the connection
                                    async with engine.begin() as conn:
                                        # Update the query record with token usage and cost
                                        await conn.execute(
                                            sql_text("""
                                                UPDATE query 
                                                SET status = :status, token_usage = :token_usage, cost = :cost 
                                                WHERE thread_id = :thread_id AND run_id = :run_id
                                            """),
                                            {
                                                "status": run_details.status,
                                                "token_usage": json.dumps({
                                                    "prompt_tokens": prompt_tokens,
                                                    "completion_tokens": completion_tokens,
                                                    "total_tokens": total_tokens
                                                }),
                                                "cost": cost,
                                                "thread_id": thread_id,
                                                "run_id": run_id
                                            }
                                        )
                                        logger.info(f"[on_end] Updated query record with token usage and cost for thread {thread_id}, run {run_id}")

                                        # If auth0_sub is available, update the user_query record status to 'completed'
                                        if hasattr(self, 'auth0_sub') and self.auth0_sub:
                                            # First check if the record exists
                                            result = await conn.execute(
                                                sql_text("SELECT id, user_query FROM query WHERE thread_id = :thread_id AND run_id = :run_id"),
                                                {"thread_id": thread_id, "run_id": run_id}
                                            )
                                            record = result.fetchone()
                                            
                                            if record:
                                                try:
                                                    # Update the user_query record
                                                    await conn.execute(
                                                        sql_text("""
                                                            UPDATE user_query 
                                                            SET status = :status, completed_time = NOW() 
                                                            WHERE uid = :uid AND query_id = :query_id AND status = 'pending'
                                                        """),
                                                        {
                                                            "status": run_details.status,
                                                            "uid": self.auth0_sub,
                                                            "query_id": record[0]
                                                        }
                                                    )
                                                    logger.info(f"[on_end] Updated user_query record status to {run_details.status} for user {self.auth0_sub}, query {record[0]}")
                                                except Exception as e:
                                                    logger.error(f"[on_end] Error updating user_query record: {str(e)}")
                                                    logger.error(f"[on_end] Exception details: {traceback.format_exc()}")
                                finally:
                                    await engine.dispose()
                            except Exception as e:
                                logger.error(f"[on_end] Error updating query record with token usage: {str(e)}")
                                logger.error(f"[on_end] Exception details: {traceback.format_exc()}")
                        else:
                            logger.warning(f"[on_end] No usage information available for run {run_id}")
                    except Exception as e:
                        logger.error(f"[on_end] Error handling run end: {str(e)}")
                        logger.error(f"[on_end] Exception details: {traceback.format_exc()}")

                @override
                async def on_exception(self, exception):
                    error_message = f"Assistant run failed with error: {exception}"
                    logger.error(error_message)
                    
                    # Update the query record with error status
                    run_id = getattr(self, 'current_run_id', None)
                    thread_id = getattr(self, 'current_thread_id', None)

                    if not run_id:
                        logger.error("[on_exception] No run_id available in handler")
                        return
                    if not thread_id:
                        logger.error("[on_exception] No thread_id available in handler")
                        return

                    if run_id:
                        try:
                            # Get the OpenAI client from the parent provider
                            client = None
                            if hasattr(self, '_provider') and hasattr(self._provider, 'client'):
                                client = self._provider.client
                                    
                            if not client:
                                # Fallback to creating a new client
                                from openai import AsyncOpenAI
                                client = AsyncOpenAI(
                                    api_key=os.getenv('OPENAI_API_KEY'),
                                    base_url=os.getenv('OPENAI_API_URL'),
                                    default_headers={"OpenAI-Beta": "assistants=v2"}
                                )
                                logger.info("[on_exception] Created new OpenAI client as fallback")
                            
                            # Update the database record using async SQL
                            from sqlalchemy import text as sql_text
                            from sqlalchemy.ext.asyncio import create_async_engine
                            from config.settings import get_database_url
                            
                            # Get the async database URL
                            db_url = get_database_url('user_data', async_mode=True)
                            engine = create_async_engine(db_url)
                            
                            try:
                                # Use an async context manager for the connection
                                async with engine.begin() as conn:
                                    # Update the record
                                    await conn.execute(
                                        sql_text("UPDATE query SET status = 'failed' WHERE thread_id = :thread_id AND run_id = :run_id"),
                                        {"thread_id": thread_id, "run_id": run_id}
                                    )
                                    logger.info(f"[on_exception] Updated query record with error status for thread {thread_id}, run {run_id}")

                                    # If auth0_sub is available, update the user_query record status to 'error'
                                    if hasattr(self, 'auth0_sub') and self.auth0_sub:
                                        try:
                                            # Get the query ID
                                            result = await conn.execute(
                                                sql_text("SELECT id FROM query WHERE thread_id = :thread_id AND run_id = :run_id"),
                                                {"thread_id": thread_id, "run_id": run_id}
                                            )
                                            query_record = result.fetchone()

                                            if query_record:
                                                # Update the user_query record
                                                await conn.execute(
                                                    sql_text("""
                                                        UPDATE user_query 
                                                        SET status = 'failed', completed_time = NOW() 
                                                        WHERE uid = :uid AND query_id = :query_id AND status = 'pending'
                                                    """),
                                                    {
                                                        "uid": self.auth0_sub,
                                                        "query_id": query_record[0]
                                                    }
                                                )
                                                logger.info(f"[on_exception] Updated user_query record status to error for user {self.auth0_sub}, query {query_record[0]}")
                                        except Exception as e:
                                            logger.error(f"[on_exception] Error updating user_query record: {str(e)}")
                                            logger.error(f"[on_exception] Exception details: {traceback.format_exc()}")
                            finally:
                                await engine.dispose()
                        except Exception as e:
                            logger.error(f"[on_exception] Error updating query record: {str(e)}")
                            logger.error(f"[on_exception] Exception details: {traceback.format_exc()}")
                            thread_id = None  # Ensure thread_id is defined for the error message
                    else:
                        thread_id = None  # Ensure thread_id is defined for the error message
                    
                    # Add error to the queue
                    await self.delta_queue.put({
                        "content": f"Error: {exception}",
                        "error": str(exception),
                        "thread_id": thread_id if 'thread_id' in locals() else None,
                        "run_id": run_id,
                        "timestamp": time.time()
                    })
            
            try:
                # Send an initial message to help establish the connection
                initial_message = {
                    "content": "Assistant is thinking...",
                    "thread_id": thread_id,
                    "run_id": None,
                    "is_initial": True,
                    "timestamp": time.time(),
                    "type": "progress"
                }
                logger.info(f"[_stream_assistant_response] Sending initial message: {json.dumps(initial_message)}")
                yield initial_message
                
                # Create event handler
                handler = EventHandler(self, auth0_sub)
                
                logger.info(f"[_stream_assistant_response] Starting stream for thread_id={thread_id}")
                
                # Set up heartbeat variables
                heartbeat_count = 0
                last_heartbeat_time = time.time()
                heartbeat_interval = 5  # Send a heartbeat every 5 seconds
                
                # Stream and create a new run in one step
                logger.info(f"[_stream_assistant_response] Creating and streaming run for thread_id={thread_id}")
                try:
                    async with self.client.beta.threads.runs.stream(
                        thread_id=thread_id,
                        assistant_id=assistant_id,
                        event_handler=handler
                    ) as stream:
                        logger.info(f"[_stream_assistant_response] Stream connection established")
                        
                        # Store the stream object reference in the handler
                        handler._stream = stream
                        
                        # Get the run ID directly from the stream if available
                        if hasattr(stream, 'current_run') and stream.current_run and stream.current_run.id:
                            handler.current_run_id = stream.current_run.id
                            logger.info(f"[_stream_assistant_response] Got run_id from stream: {handler.current_run_id}")
                        
                        # Create a task for the stream
                        stream_task = asyncio.create_task(stream.until_done())
                        
                        # Process deltas and heartbeats while the stream is active
                        while not stream_task.done():
                            # Check if there are any deltas in the queue
                            try:
                                # Try to get a delta from the queue with a short timeout
                                delta = await asyncio.wait_for(handler.delta_queue.get(), 0.1)
                                # Yield the delta
                                yield delta
                                # Mark the task as done
                                handler.delta_queue.task_done()
                                # Reset heartbeat timer since we just sent a message
                                last_heartbeat_time = time.time()
                            except asyncio.TimeoutError:
                                # No delta available, check if it's time for a heartbeat
                                current_time = time.time()
                                if current_time - last_heartbeat_time >= heartbeat_interval:
                                    heartbeat_count += 1
                                    heartbeat_message = {
                                        "content": "",  # Empty content for heartbeat
                                        "thread_id": thread_id,
                                        "run_id": getattr(handler, 'current_run_id', None),
                                        "is_heartbeat": True,
                                        "heartbeat_count": heartbeat_count,
                                        "timestamp": current_time
                                    }
                                    logger.info(f"[_stream_assistant_response] Sending heartbeat {heartbeat_count}")
                                    yield heartbeat_message
                                    last_heartbeat_time = current_time
                                
                                # Wait a short time before checking again
                                await asyncio.sleep(0.1)
                        
                        # Get the result of the stream task
                        await stream_task
                        
                        # Process any remaining deltas in the queue
                        while not handler.delta_queue.empty():
                            delta = await handler.delta_queue.get()
                            yield delta
                            handler.delta_queue.task_done()
                except Exception as e:
                    error_str = str(e)
                    # Check if this is a vector store expired error
                    if "Vector store" in error_str and "is expired" in error_str:
                        logger.warning(f"Vector store expired error detected: {error_str}")
                        # Extract vector store ID from error message
                        import re
                        vector_store_match = re.search(r"Vector store (vs_[\w]+) is expired", error_str)
                        if vector_store_match:
                            expired_vector_store_id = vector_store_match.group(1)
                            logger.info(f"Attempting to delete expired vector store: {expired_vector_store_id}")

                            try:
                                # Delete the expired vector store
                                await self.client.vector_stores.delete(expired_vector_store_id)
                                logger.info(f"Successfully deleted expired vector store: {expired_vector_store_id}")
                            except Exception as delete_error:
                                logger.error(f"Failed to delete expired vector store {expired_vector_store_id}: {str(delete_error)}")
                    # Re-raise the original error after cleanup
                    raise e
                
                # Get the run_id from the handler for logging and further operations
                run_id = getattr(handler, 'current_run_id', None)
                logger.info(f"[_stream_assistant_response] Stream completed for run_id={run_id}")
                
                # If run_id is still None, try to get it from the stream object
                if run_id is None and hasattr(stream, 'current_run') and stream.current_run:
                    run_id = stream.current_run.id
                    logger.info(f"[_stream_assistant_response] Retrieved run_id from stream object: {run_id}")
                
                # Log whether on_run_created was called
                logger.info(f"[_stream_assistant_response] on_run_created was called: {getattr(handler, 'run_created_called', False)}")
                        
                # After streaming, check if the run is in require_action status
                if run_id:
                    run = await self.client.beta.threads.runs.retrieve(
                        thread_id=thread_id,
                        run_id=run_id
                    )
                    
                    function_call_count = {}

                    logger.info(f"[_stream_assistant_response] Run status after streaming: {run.status}")

                    # Define final states
                    final_states = ["completed", "failed", "cancelled", "incomplete", "expired"]

                    while run.status not in final_states:
                        # If the run is in require_action status, handle the required actions
                        if run.status == "requires_action":
                            logger.info(f"[_stream_assistant_response] Run {run_id} requires action")
                        
                            # Get the required actions
                            required_action = run.required_action
                            if required_action and required_action.type == "submit_tool_outputs":
                                tool_calls = required_action.submit_tool_outputs.tool_calls
                                tool_outputs = []
                            
                                # Process each tool call
                                for tool_call in tool_calls:
                                    # Only process function tool calls
                                    if hasattr(tool_call, "function") and tool_call.function is not None:
                                        function_name = tool_call.function.name
                                        function_args = json.loads(tool_call.function.arguments)
                                        logger.info(f"Executing function: {function_name} with args: {tool_call.function.arguments}")

                                        if function_name == "provide_response_metadata":
                                            # We already processed this in on_tool_call_done
                                            # Just return an empty result to acknowledge it
                                            tool_outputs.append({
                                                "tool_call_id": tool_call.id,
                                                "output": json.dumps({"status": "processed"})
                                            })
                                            logger.info(f"Metadata function processed")
                                        elif function_name in function_map:
                                            try:
                                                if function_name == "search":
                                                    initial_message = {
                                                        "content": "Collecting information via Deepsearch ...",
                                                        "thread_id": thread_id,
                                                        "run_id": None,
                                                        "timestamp": time.time(),
                                                        "type": "progress"
                                                    }
                                                    yield initial_message
                                                elif function_name == "query_stocks":
                                                    initial_message = {
                                                        "content": "Query stock information ...",
                                                        "thread_id": thread_id,
                                                        "run_id": None,
                                                        "timestamp": time.time(),
                                                        "type": "progress"
                                                    }
                                                    yield initial_message
                                                elif function_name == "query_ticker_concepts":
                                                    initial_message = {
                                                        "content": "Get company concepts information ...",
                                                        "thread_id": thread_id,
                                                        "run_id": None,
                                                        "timestamp": time.time(),
                                                        "type": "progress"
                                                    }
                                                    yield initial_message
                                                # Call the function
                                                result = function_map[function_name](**function_args)
                                                # Increment the call count for this function
                                                function_call_count[function_name] = function_call_count.get(function_name, 0) + 1

                                                # Check if the result is a coroutine (from an async function)
                                                if asyncio.iscoroutine(result):
                                                    logger.debug(f"Function {function_name} returned a coroutine, awaiting it")
                                                    # Await the coroutine to get the actual result
                                                    result = await result

                                                # If the function is query_ticker_concepts, send a special chunk with type "company_facts"
                                                if function_name == "query_ticker_concepts":
                                                    # Only send company_facts if context in classification is not True
                                                    if not (self.current_classification and self.current_classification.get('context') is True):
                                                        logger.info(f"Sending company_facts chunk for ticker {function_args.get('ticker', 'unknown')}")
                                                        company_facts_message = {
                                                            "type": "company_facts",
                                                            "data": result,
                                                            "thread_id": thread_id,
                                                            "run_id": run_id,
                                                            "timestamp": time.time()
                                                        }
                                                        yield company_facts_message
                                                        logger.debug(f"Added company_facts to queue: {json.dumps(company_facts_message)[:100]}...")
                                                    else:
                                                        logger.info(f"Skipping company_facts chunk for ticker {function_args.get('ticker', 'unknown')} because context is True in classification")

                                                # Special case for query_ticker_concepts with Screening Stocks query type
                                                logger.debug(f"classification is: {self.current_classification.get('type')}")
                                                if function_name == "query_ticker_concepts" and self.current_classification and self.current_classification.get('type') == 'Screening Stocks':
                                                    # Return a simplified response for this specific case
                                                    result = {"status": "processed"}
                                                    logger.info(f"query_ticker_concepts for Screening Stocks processed with simplified response to assistant")

                                                tool_outputs.append({
                                                    "tool_call_id": tool_call.id,
                                                    "output": json.dumps(result)
                                                })
                                                logger.debug(f"output: {result}")
                                                logger.info(f"Function {function_name} executed successfully with output {len(json.dumps(result))}")
                                            except Exception as e:
                                                error_message = f"Error executing function {function_name}: {str(e)}"
                                                logger.error(error_message)
                                                tool_outputs.append({
                                                    "tool_call_id": tool_call.id,
                                                    "output": json.dumps({"error": error_message})
                                                })
                                        else:
                                            error_message = f"Function {function_name} not found"
                                            logger.error(error_message)
                                            tool_outputs.append({
                                                "tool_call_id": tool_call.id,
                                                "output": json.dumps({"error": error_message})
                                            })
                                    else:
                                        # Not a function tool call (e.g., file_search), skip or handle if needed
                                        logger.info(f"Skipping non-function tool call with id {tool_call.id}")

                                # Submit the tool outputs
                                if tool_outputs:
                                    logger.info(f"[_stream_assistant_response] Submitting {len(tool_outputs)} tool outputs for run_id={run_id}")
                                    for i, output in enumerate(tool_outputs):
                                        logger.debug(f"[_stream_assistant_response] Tool output {i+1}: tool_call_id={output['tool_call_id']}, output={output['output'][:100]}...")
                                
                                    # Create a new handler for the second stream
                                    second_handler = EventHandler(self, auth0_sub)
                                
                                    # Use the submit_tool_outputs_stream helper
                                    logger.info(f"[_stream_assistant_response] Starting second submit and stream after tool outputs for run_id={run_id}")
                                    async with self.client.beta.threads.runs.submit_tool_outputs_stream(
                                        thread_id=thread_id,
                                        run_id=run.id,
                                        tool_outputs=tool_outputs,
                                        event_handler=second_handler
                                    ) as second_stream:
                                        initial_message = {
                                            "content": "Summarising and Reasoning ...",
                                            "thread_id": thread_id,
                                            "run_id": None,
                                            "timestamp": time.time(),
                                            "type": "progress"
                                        }
                                        yield initial_message

                                        # Create a task for the second stream
                                        second_stream_task = asyncio.create_task(second_stream.until_done())
                                    
                                        # Process deltas from the second stream
                                        while not second_stream_task.done():
                                            # Check if there are any deltas in the queue
                                            try:
                                                # Try to get a delta from the queue with a short timeout
                                                delta = await asyncio.wait_for(second_handler.delta_queue.get(), 0.1)
                                                # Yield the delta
                                                yield delta
                                                # Mark the task as done
                                                second_handler.delta_queue.task_done()
                                            except asyncio.TimeoutError:
                                                # No delta available, wait a short time before checking again
                                                await asyncio.sleep(0.1)
                                    
                                        # Get the result of the second stream task
                                        await second_stream_task
                                    
                                        # Process any remaining deltas in the queue
                                        while not second_handler.delta_queue.empty():
                                            delta = await second_handler.delta_queue.get()
                                            yield delta
                                            second_handler.delta_queue.task_done()
                                
                                    logger.info(f"[_stream_assistant_response] Second stream completed for run_id={run_id}")
                        run = await self.client.beta.threads.runs.retrieve(
                            thread_id=thread_id,
                            run_id=run_id
                        )
                        logger.info(f"Current run - {run_id} - status: {run.status}")

                    # After the loop, handle each final state appropriately
                    logger.info(f"[_stream_assistant_response] Run {run_id} reached final state: {run.status}, function_call_count: {function_call_count}")

                    if run.status == "failed":
                        # Handle failed state
                        error_message = f"Assistant run failed with status: {run.status}"
                        if hasattr(run, 'last_error') and run.last_error:
                            error_message += f", error: {run.last_error.code} - {run.last_error.message}"
                        logger.error(error_message)
                        yield {
                            "content": error_message,
                            "error": error_message,
                            "thread_id": thread_id,
                            "run_id": run_id,
                            "timestamp": time.time(),
                            "type": "Error"
                        }
                    elif run.status == "cancelled":
                        # Handle cancelled state
                        logger.info(f"Run {run_id} was cancelled")
                        yield {
                            "content": "The operation was cancelled.",
                            "thread_id": thread_id,
                            "run_id": run_id,
                            "timestamp": time.time()
                        }
                    elif run.status == "incomplete":
                        # Handle incomplete state
                        incomplete_reason = "unknown reason"
                        if hasattr(run, 'incomplete_details') and run.incomplete_details and run.incomplete_details.reason:
                            incomplete_reason = run.incomplete_details.reason
                        logger.info(f"Run {run_id} is incomplete due to: {incomplete_reason}")
                        yield {
                            "content": f"The response is incomplete due to {incomplete_reason}.",
                            "thread_id": thread_id,
                            "run_id": run_id,
                            "timestamp": time.time()
                        }
                    elif run.status == "expired":
                        # Handle expired state
                        logger.info(f"Run {run_id} expired")
                        yield {
                            "content": "The operation timed out. Please try again.",
                            "thread_id": thread_id,
                            "run_id": run_id,
                            "timestamp": time.time()
                        }
                    # The "completed" state is already handled by the existing code
            except Exception as e:
                logger.error(f"Error in streaming: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                yield {
                    "type": "Error",
                    "content": f"Error: {str(e)}",
                    "error": str(e),
                    "thread_id": thread_id,
                    "run_id": None,
                    "timestamp": time.time()
                }
        
        return generate()

    async def fallback_response(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Generate a fallback response when the primary provider fails.
        
        Args:
            messages: List of message objects
            model: The model to use
            **kwargs: Additional arguments to pass to the provider
            
        Returns:
            Dict: A fallback response
        """
        # Log that we're using a fallback response
        logger.info(f"Generating fallback response for model {model}")
        
        # Simple fallback that returns an error message
        fallback_response = {
            "choices": [
                {
                    "message": {
                        "role": "assistant",
                        "content": "I'm sorry, but I'm having trouble connecting to the assistant service. Please try again later."
                    },
                    "finish_reason": "stop",
                    "index": 0
                }
            ],
            "created": int(time.time()),
            "model": model,
            "object": "chat.completion",
            "is_fallback": True
        }
        
        logger.debug(f"Returning fallback response: {fallback_response}")
        return fallback_response
