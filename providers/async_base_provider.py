from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import json
import traceback
from utils.logging import logger
from utils.circuit_breaker import with_circuit_breaker, with_retry, with_fallback
from utils.async_cache import get_cached_response as async_get_cached_response
from utils.async_cache import cache_response as async_cache_response
from utils.function_registry import function_registry

class AsyncModelProvider(ABC):
    """
    Abstract base class for async model providers.
    """
    
    @abstractmethod
    async def _chat_completion_impl(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Async implementation of chat completion.
        
        Args:
            messages: List of message objects
            model: The model to use
            **kwargs: Additional arguments to pass to the provider
            
        Returns:
            Dict: The response from the provider
        """
        pass
    
    async def _has_function_call(self, response: Dict[str, Any]) -> bool:
        """
        Check if a response contains a function call.
        
        Args:
            response: The response from the provider
            
        Returns:
            bool: True if the response contains a function call
        """
        if not response or 'choices' not in response:
            return False
            
        for choice in response['choices']:
            if not choice:
                continue
                
            message = choice.get('message')
            if not message:
                continue
                
            if message.get('function_call'):
                return True
                
        return False
    
    async def _handle_function_call(self, response: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Handle a function call in a response.
        
        Args:
            response: The response from the provider
            **kwargs: Additional arguments
            
        Returns:
            Dict: The updated response
        """
        # Check if function execution is enabled
        if not kwargs.get('execute_functions', True):
            return response
            
        try:
            # Validate response structure
            if not response or 'choices' not in response or not response['choices']:
                logger.warning("Invalid response structure for function call handling")
                return response
                
            # Get the function call
            try:
                choice = response['choices'][0]
                if not choice:
                    logger.warning("Empty choice in response")
                    return response
                    
                message = choice.get('message')
                if not message:
                    logger.warning("No message in choice")
                    return response
                    
                function_call = message.get('function_call')
                if not function_call:
                    logger.debug("No function call in message")
                    return response
            except (KeyError, IndexError, TypeError) as e:
                logger.error(f"Error accessing response structure: {str(e)}")
                return response
                
            # Extract function name and arguments
            function_name = function_call.get('name')
            if not function_name:
                logger.warning("Function call missing name")
                return response
                
            arguments_str = function_call.get('arguments', '{}')
            
            try:
                arguments = json.loads(arguments_str)
            except json.JSONDecodeError:
                logger.error(f"Invalid function arguments: {arguments_str}")
                arguments = {}
            
            # Execute the function
            if not function_registry.has_function(function_name):
                logger.warning(f"Function {function_name} not found in registry")
                return response
                
            function_result = await function_registry.execute_function(function_name, **arguments)
            
            # Add function result to response
            if 'function_results' not in response:
                response['function_results'] = []
                
            response['function_results'].append({
                'name': function_name,
                'arguments': arguments,
                'result': function_result
            })
            
            return response
        except Exception as e:
            logger.error(f"Error handling function call: {str(e)}")
            logger.debug(f"Function call error traceback: {traceback.format_exc()}")
            return response
    
    async def chat_completion(self, messages: List[Dict[str, Any]], model: str, stream: bool = None, **kwargs) -> Dict[str, Any]:
        """
        Generate a chat completion with error recovery and caching.
        
        This method applies circuit breaker, retry, and fallback patterns
        to the chat completion implementation. It also caches responses
        to improve performance and reduce API costs.
        
        Args:
            messages: List of message objects
            model: The model to use
            stream: Whether to stream the response (default: None, which will use the provider's default)
            functions: Optional list of function definitions
            function_call: Optional control for function calling (auto, none, or specific function)
            **kwargs: Additional arguments to pass to the provider
            
        Returns:
            Dict: The response from the provider
        """
        # Check if streaming is requested
        if stream is None:
            stream = kwargs.get('stream', True)  # Default to streaming if not explicitly set
        else:
            kwargs['stream'] = stream  # Ensure stream parameter is passed to implementation
        
        # Get functions if provided
        functions = kwargs.get('functions')
        if functions is None and kwargs.get('auto_functions', False):
            # Use all registered functions if auto_functions is True
            functions = function_registry.get_function_schemas()
            kwargs['functions'] = functions
        
        # Don't use cache for streaming responses or when using functions
        if not stream and not functions:
            # Try to get cached response
            provider_name = self.__class__.__name__.replace('Provider', '').lower()
            cached_response = await async_get_cached_response(provider_name, model, messages, **kwargs)
            if cached_response:
                return cached_response
        
        try:
            # Get response from provider
            response = await self._chat_completion_impl(messages, model, **kwargs)
            
            # Handle function call if present in response
            if not stream and await self._has_function_call(response):
                response = await self._handle_function_call(response, **kwargs)
            
            # Cache the response if not streaming and not using functions
            if not stream and not functions:
                provider_name = self.__class__.__name__.replace('Provider', '').lower()
                await async_cache_response(provider_name, model, messages, response, **kwargs)
            
            return response
        except Exception as e:
            logger.warning(f"{self.__class__.__name__} failed: {str(e)}. Using fallback.")
            return await self.fallback_response(messages, model, **kwargs)
    
    async def fallback_response(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Generate a fallback response when the primary provider fails.
        
        Args:
            messages: List of message objects
            model: The model to use
            **kwargs: Additional arguments to pass to the provider
            
        Returns:
            Dict: A fallback response
        """
        # Log that we're using a fallback response
        logger.info(f"Generating fallback response for model {model}")
        
        # Check if function calling was requested
        functions = kwargs.get('functions')
        if functions:
            logger.debug(f"Function calling was requested in the original call with {len(functions)} functions")
        
        # Simple fallback that returns an error message
        fallback_response = {
            "choices": [
                {
                    "message": {
                        "role": "assistant",
                        "content": "I'm sorry, but I'm having trouble connecting to the service. Please try again later."
                    },
                    "finish_reason": "stop",
                    "index": 0
                }
            ],
            "created": 0,
            "model": model,
            "object": "chat.completion",
            "is_fallback": True
        }
        
        logger.debug(f"Returning fallback response: {fallback_response}")
        return fallback_response
