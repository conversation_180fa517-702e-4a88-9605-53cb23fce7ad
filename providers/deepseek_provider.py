import os
from typing import Dict, Any, List
from openai import OpenAI
from .base_provider import <PERSON><PERSON>rovider
from utils.circuit_breaker import with_circuit_breaker, with_retry, with_fallback
from utils.logging import logger
from config.settings import settings

class DeepSeekProvider(ModelProvider):
    """
    Provider implementation for DeepSeek API using OpenAI client.
    """
    def __init__(self):
        self.client = OpenAI(
            api_key=os.getenv('DEEPSEEK_API_KEY'),
            base_url=os.getenv('DEEPSEEK_API_URL')
        )

    @with_circuit_breaker('deepseek')
    @with_retry(max_attempts=3, min_wait=1, max_wait=10)
    def _chat_completion_impl(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Implementation of chat completion for DeepSeek.
        
        Args:
            messages: List of message objects with role and content
            model: The model identifier to use
            **kwargs: Additional parameters to pass to the DeepSeek API
            
        Returns:
            Dict containing the model response
        """
        try:
            stream = kwargs.pop('stream', False)
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                stream=stream,
                **kwargs
            )
            
            if stream:
                return response  # Return the streaming response directly
            else:
                return response.model_dump()
        except Exception as e:
            logger.error(f"DeepSeek API error: {str(e)}")
            raise

    def chat_completion(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Generate a chat completion with error recovery.
        
        This method applies circuit breaker, retry, and fallback patterns
        to the chat completion implementation.
        
        Args:
            messages: List of message objects
            model: The model to use
            **kwargs: Additional arguments to pass to the provider
            
        Returns:
            Dict: The response from the provider
        """
        try:
            return self._chat_completion_impl(messages, model, **kwargs)
        except Exception as e:
            logger.warning(f"DeepSeek provider failed: {str(e)}. Using fallback.")
            return self.fallback_response(messages, model, **kwargs)
