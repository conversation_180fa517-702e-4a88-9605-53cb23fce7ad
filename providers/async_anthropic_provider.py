import os
from typing import Dict, Any, List
import httpx
from openai import AsyncOpenAI
from .async_base_provider import AsyncModelProvider
from utils.circuit_breaker import with_circuit_breaker, with_retry, with_fallback
from utils.logging import logger
from config.settings import settings

class AsyncA<PERSON>hropic<PERSON><PERSON>ider(AsyncModelProvider):
    """
    Async provider implementation for Anthropic API using OpenAI client.
    """
    def __init__(self):
        api_key = os.getenv('ANTHROPIC_API_KEY')
        base_url = os.getenv('ANTHROPIC_API_URL')
        
        # Log API configuration for debugging
        if not api_key:
            logger.warning("ANTHROPIC_API_KEY is not set or empty")
        else:
            logger.info(f"Using Anthropic API key: {api_key[:5]}...{api_key[-4:] if len(api_key) > 9 else ''}")
        
        logger.info(f"Using Anthropic API URL: {base_url}")
        
        self.client = AsyncOpenAI(
            api_key=api_key,
            base_url=base_url
        )

    async def _chat_completion_impl(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Async implementation of chat completion for Anthropic.
        
        Args:
            messages: List of message objects with role and content
            model: The model identifier to use
            **kwargs: Additional parameters to pass to the Anthropic API
            
        Returns:
            Dict containing the model response or an async generator for streaming
        """
        try:
            stream = kwargs.pop('stream', True)  # Default to streaming
            timeout = kwargs.pop('timeout', 30)  # Default timeout of 30 seconds
            
            # Extract function-related parameters that shouldn't be passed to the API
            # Anthropic may not support these parameters, so we remove them
            functions = kwargs.pop('functions', None)
            function_call = kwargs.pop('function_call', None)
            auto_functions = kwargs.pop('auto_functions', False)
            execute_functions = kwargs.pop('execute_functions', True)
            
            # Log function calling configuration if present
            if functions:
                logger.warning(f"Function calling parameters provided but may not be supported by Anthropic: {len(functions)} functions")
                logger.debug(f"Removed function calling parameters from Anthropic API call")
            
            logger.info(f"Making API request to {self.client.base_url} with model {model}, stream={stream}")
            
            try:
                response = await self.client.chat.completions.create(
                    model=model,
                    messages=messages,
                    stream=stream,
                    timeout=timeout,
                    **kwargs
                )
                logger.debug(f"API request successful, response type: {type(response).__name__}")
            except httpx.ConnectTimeout as e:
                logger.error(f"Connection timeout to {self.client.base_url}: {str(e)}")
                raise Exception(f"Connection timeout to API server: {str(e)}")
            except httpx.ReadTimeout as e:
                logger.error(f"Read timeout from {self.client.base_url}: {str(e)}")
                raise Exception(f"Read timeout from API server: {str(e)}")
            except httpx.ConnectError as e:
                logger.error(f"Connection error to {self.client.base_url}: {str(e)}")
                raise Exception(f"Cannot connect to API server: {str(e)}")
            except Exception as e:
                error_type = type(e).__name__
                error_message = str(e)
                logger.error(f"Unexpected error during API request: {error_type}: {error_message}")
                
                # Check if this is a connection error
                if "Connection" in error_type or "Connection" in error_message:
                    # Log more details about the connection
                    logger.error(f"Connection details: URL={self.client.base_url}, API Key present: {bool(self.client.api_key)}")
                    
                    # Check if we can ping the API endpoint
                    try:
                        import socket
                        from urllib.parse import urlparse
                        
                        parsed_url = urlparse(self.client.base_url)
                        host = parsed_url.netloc
                        port = 443 if parsed_url.scheme == 'https' else 80
                        
                        logger.info(f"Attempting to check connectivity to {host}:{port}")
                        
                        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        s.settimeout(5)
                        result = s.connect_ex((host, port))
                        s.close()
                        
                        if result == 0:
                            logger.info(f"Successfully connected to {host}:{port}")
                        else:
                            logger.error(f"Failed to connect to {host}:{port}, error code: {result}")
                    except Exception as conn_e:
                        logger.error(f"Error checking connectivity: {str(conn_e)}")
                
                raise
            
            if stream:
                # Ensure we're returning an async iterable
                async def response_generator():
                    try:
                        async for chunk in response:
                            yield chunk
                    except Exception as e:
                        logger.error(f"Error in streaming response: {str(e)}")
                        raise
                
                return response_generator()  # Return an async generator
            else:
                return response.model_dump()
        except Exception as e:
            logger.error(f"Anthropic API error: {str(e)}")
            raise

    # Apply circuit breaker and retry decorators
    # Note: These decorators need to be applied differently for async functions
    # We'll implement async versions of these decorators in a real implementation
    # For now, we'll use the async method directly
    async def chat_completion(self, messages: List[Dict[str, Any]], model: str, **kwargs) -> Dict[str, Any]:
        """
        Generate a chat completion with error recovery.
        
        This method applies circuit breaker, retry, and fallback patterns
        to the chat completion implementation.
        
        Args:
            messages: List of message objects
            model: The model to use
            **kwargs: Additional arguments to pass to the provider
            
        Returns:
            Dict: The response from the provider
        """
        try:
            # In a real implementation, we would use async versions of the circuit breaker and retry decorators
            return await self._chat_completion_impl(messages, model, **kwargs)
        except Exception as e:
            logger.warning(f"Anthropic provider failed: {str(e)}. Using fallback.")
            return await self.fallback_response(messages, model, **kwargs)
