import os
import json
from typing import Optional
from dotenv import load_dotenv
from utils.logging import logger
from typing import List, Optional, Dict, Any

# Load environment variables from .env file
load_dotenv()

class Settings:
    # Server settings
    PORT = int(os.getenv("PORT", 5002))
    
    # Assistant API quota settings
    ASSISTANT_DAILY_QUOTA = int(os.getenv("ASSISTANT_DAILY_QUOTA", 5))  # Default: 5 requests per day
    ASSISTANT_QUOTA_ENABLED = os.getenv("ASSISTANT_QUOTA_ENABLED", "true").lower() == "true"
    ASSISTANT_WHITELIST = json.loads(os.getenv("ASSISTANT_WHITELIST", "[]"))  # Default: empty list

    # Redis settings
    REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
    REDIS_DB = int(os.getenv("REDIS_DB", 0))
    REDIS_MAX_CONNECTIONS = int(os.getenv("REDIS_MAX_CONNECTIONS", 10))
    
    # Database settings
    DB_HOST = os.getenv("DB_HOST", "localhost")
    DB_PORT = int(os.getenv("DB_PORT", 3306))
    DB_NAME = os.getenv("DB_NAME", "stock_data")
    DB_USER = os.getenv("DB_USER", "root")
    DB_PASSWORD = os.getenv("DB_PASSWORD", "")
    
    # Database schemas
    DB_SCHEMAS = {
        "default": DB_NAME,
        "stock_data": os.getenv("DB_STOCK_DATA_SCHEMA", "stock_data"),
        "analytics": os.getenv("DB_ANALYTICS_SCHEMA", "analytics"),
        "user_data": os.getenv("DB_USER_DATA_SCHEMA", "user_data")
    }

    # Cache settings
    ENABLE_CACHE = os.getenv("ENABLE_CACHE", "true").lower() == "true"
    ENABLE_FILE_SEARCH = os.getenv("ENABLE_FILE_SEARCH", "false").lower() == "true"
    CACHE_TTL = int(os.getenv("CACHE_TTL", 3600))  # Default: 1 hour
    CACHE_COMPRESSION = os.getenv("CACHE_COMPRESSION", "true").lower() == "true"
    CACHE_MEMORY_ENABLED = os.getenv("CACHE_MEMORY_ENABLED", "true").lower() == "true"
    CACHE_MEMORY_MAX_ITEMS = int(os.getenv("CACHE_MEMORY_MAX_ITEMS", 1000))
    CACHE_MEMORY_TTL = int(os.getenv("CACHE_MEMORY_TTL", 300))  # Default: 5 minutes
    CACHE_PERSISTENT_ENABLED = os.getenv("CACHE_PERSISTENT_ENABLED", "false").lower() == "true"
    CACHE_PERSISTENT_PATH = os.getenv("CACHE_PERSISTENT_PATH", "./cache")
    CACHE_LRU_POLICY = os.getenv("CACHE_LRU_POLICY", "true").lower() == "true"
    CACHE_MAX_SIZE_MB = int(os.getenv("CACHE_MAX_SIZE_MB", 1024))  # Default: 1GB
    CACHE_SIMILARITY_THRESHOLD = float(os.getenv("CACHE_SIMILARITY_THRESHOLD", 0.9))  # Default: 90% similarity

    # API keys
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "********************************************************************************************************************************************************************")
    OPENAI_API_URL = os.getenv("OPENAI_API_URL", "https://api.openai.com/v1")
    OPENAI_ASSISTANT_ID = os.getenv("OPENAI_ASSISTANT_ID", "")
    
    # OpenAI Assistant settings
    OPENAI_ASSISTANT_NAME = os.getenv("OPENAI_ASSISTANT_NAME", "Go GPT Assistant")
    OPENAI_ASSISTANT_TEMPERATURE = os.getenv("OPENAI_ASSISTANT_TEMPERATURE", 0.2)
    OPENAI_ASSISTANT_TOP = os.getenv("OPENAI_ASSISTANT_TOP", 0.8)
    OPENAI_STOCK_ANALYST_ASSISTANT_NAME = os.getenv("OPENAI_STOCK_ANALYST_ASSISTANT_NAME", "Go Stock Analyst Assistant")
    OPENAI_ASSISTANT_MODEL = os.getenv("OPENAI_ASSISTANT_MODEL", "gpt-4.1")
    OPENAI_ASSISTANT_INSTRUCTIONS = os.getenv("OPENAI_ASSISTANT_INSTRUCTIONS",
        '''
        You are an AI financial and investment assistant. Your job is to help users develop investment strategies, perform portfolio analyses, and offer relevant insights on economic conditions. Follow these guidelines:
        1. Break Down Requests
        . For each user query, decompose it into smaller, logical steps (e.g., gathering data, analyzing trends, comparing options).
        . Clearly list each step you’ll take in order.

        2. Perform Step-by-Step Reasoning
        . At each step, describe briefly what you’re doing and why.
        . If you are missing information or if assumptions need clarifying, say so and ask the user for more detail.

        3. Be Comprehensive and Accurate
        . Use up-to-date and relevant market data whenever possible.
        . Provide disclaimers when making assumptions (e.g., “Assuming moderate risk tolerance…”).

        4. Offer Clear Conclusions/Next Steps
        . End with a concise summary of your findings or a recommendation.
        . Suggest next steps if further analysis or user feedback is required.
        5. Maintain Compliance
        . Only provide general information and insights—avoid giving definitive or personalized legal or tax advice.
        . Always remind users to consult qualified professionals for final decisions.
        '''
    )
    OPENAI_ASSISTANT_EARNINGS_INSTRUCTIONS = os.getenv("OPENAI_ASSISTANT_EARNINGS_INSTRUCTIONS",
        '''
        Skill requirements:
        1. Financial modeling: Proficient in creating and interpreting responsible financial models, accurately predicting the company's future financial performance
        2. Report writing: Write professional financial analysis reports and deeply analyze the company's financial status

        Workflow:
        1. Data extraction and processing
        2. DuPont analysis calculation
        Three-factor model:
        Net profit margin = net profit/sales revenue, reflecting the company's profit margin per unit of sales revenue
        Total asset turnover = sales revenue/total assets, measuring the efficiency of the company's asset utilization
        Equity multiplier = total assets/shareholders' equity, reflecting the company's financial leverage
        Calculate ROE = net profit margin x total asset turnover x equity multiplier
        Five-factor model:
        Net profit after tax/net profit: reflects the company's tax burden
        Net profit/operating profit: reflects the company's interest burden
        Operating profit/sales revenue: operating profit margin, measuring operational efficiency
        Sales revenue/total assets: that is, asset turnover, measuring asset utilization
        Total assets/shareholders' equity: reflecting the use of financial leverage
        Calculate ROE = (net profit after tax/net profit) x (net profit/operating profit) x (operating profit/sales revenue) x (sales revenue/total assets) x (Total assets/shareholders' equity)
        After completion, ask the user whether further interpretation is required

        3. Analyze and interpret the results of DuPont analysis
        Use text to analyze the calculation results of three factors and no factors
        In-depth interpretation of the company's financial performance
        Evaluate asset quality and management efficiency: analyze the company's current assets and non-current assets, with special attention to inventory, revenue accounts and long-term investments
        Analyze debt results and debt repayment ability: analyze the company's short-term and long-term finances, interest coverage ratio, and evaluate debt repayment ability and financial risks
        Evaluate operating efficiency: analyze operating expenses, sales expenses and administrative expenses
        Analyze cash flow status: analyze the company's cash flow, evaluate the company's cash inflows and outflows and free cash flow
        Investigate shareholder value indicators: analyze ROE and EPS
        '''
    )
    OPENAI_ASSISTANT_STOCK_ANALYST_INSTRUCTIONS = os.getenv("OPENAI_ASSISTANT_STOCK_ANALYST_INSTRUCTIONS",
    '''
    You are a highly skilled Wall Street stock analyst. Use a professional, concise, and neutral tone. Your analysis combines:

    Fundamental analysis: Cover company financials, earnings, valuation metrics (P/E, EPS, etc.), management, and sector position.
    Technical analysis: Discuss price movements, volume, key indicators or chart patterns.
    Macroeconomic context & news: Consider relevant news, economic factors, and global trends affecting the stock or sector.
    Risk management: Briefly mention key risks or uncertainties.

    When analyzing a stock:
    If the user query is related to listed stock, call query_ticker_concepts as the first step to get company fact and call query_stock_price_ratings to get rating info from analyst
    Structure your output into sections: Overview, Fundamentals, Technicals, Macro/News, Risks, Conclusion.
    Rely on the most recent data. Reference and quote data sources when possible; if unsure, state data limitations.
    Do not provide direct investment advice (e.g., “buy”, “sell”).
    Do not speculate or make unsupported claims.
    Prioritize fact, clarity, and logical reasoning over opinion.

    Always behave like a seasoned analyst on Wall Street: professional, thorough, and unbiased.
    '''
    )

    OPENAI_ASSISTANT_FIXED_PROMPT = os.getenv("OPENAI_ASSISTANT_FIXED_PROMPT",
        '''
        Search the latest information from the web if needed. If the user query is related to listed stock, call query_ticker_concepts as the first step to get company facts. Always format stock tickers with dollar signs, like $TSLA$, $AAPL$, or $SPY$.
        Must call provide_response_metadata tool.
        '''
    )
    OPENAI_ASSISTANT_TOOLS = json.loads(os.getenv("OPENAI_ASSISTANT_TOOLS", "[]"))
    OPENAI_ASSISTANT_TOOL_RESOURCES = json.loads(os.getenv("OPENAI_ASSISTANT_TOOL_RESOURCES", "[]"))

    ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY", "************************************************************************************************************")
    ANTHROPIC_API_URL = os.getenv("ANTHROPIC_API_URL", "https://api.anthropic.com/v1")

    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "***********************************")
    DEEPSEEK_API_URL = os.getenv("DEEPSEEK_API_URL", "https://api.deepseek.com/v1")

    # Web search settings
    GOOGLE_SEARCH_API_KEY = os.getenv("GOOGLE_SEARCH_API_KEY", "")
    GOOGLE_SEARCH_ENGINE_ID = os.getenv("GOOGLE_SEARCH_ENGINE_ID", "")
    SERP_API_KEY = os.getenv("SERP_API_KEY", "c23abdcf02002ab8e67af0b53d9e261459ae05bf082c09733a16ba94da4e8450")
    USE_SERP_API = os.getenv("USE_SERP_API", "true").lower() == "true"
    USE_GPT4O_SEARCH = os.getenv("USE_GPT4O_SEARCH", "true").lower() == "true"

    # Multi-round conversation settings
    MAX_CONCURRENT_SEARCHES = int(os.getenv("MAX_CONCURRENT_SEARCHES", 5))
    DEFAULT_SEARCH_THRESHOLD = float(os.getenv("DEFAULT_SEARCH_THRESHOLD", 0.5))

    # Round-aware search settings
    SEARCH_CACHE_TTL = int(os.getenv("SEARCH_CACHE_TTL", 3600))
    AUTO_DETECT_SEARCH = os.getenv("AUTO_DETECT_SEARCH", "true").lower() == "true"
    SEARCH_RESULT_MAX_CHARS = int(os.getenv("SEARCH_RESULT_MAX_CHARS", 8000))

    # Worker and queue settings
    WORKER_THREADS = int(os.getenv("WORKER_THREADS", 4))
    MODEL_QUEUE_SIZE = int(os.getenv("MODEL_QUEUE_SIZE", 100))
    MAX_CONCURRENT_API_CALLS = int(os.getenv("MAX_CONCURRENT_API_CALLS", 10))

    # Query Type Context settings
    QUERY_CONTEXT_SYSTEM_PROMPT = os.getenv("QUERY_CONTEXT_SYSTEM_PROMPT", 'You are a financial query classifier. Your task is to check whether user query is financial or investment related, and if yes classify user queries into one of the following categories:1. Analysing Stocks - Queries about analyzing specific stocks, their performance, fundamentals, etc.2. Screening Stocks - Queries about finding stocks that match certain criteria or filters.3. Summarising Company Financial Filings - Queries about SEC filings, earnings reports, financial statements, etc.4. Analysing Market Events - Queries about economic data release only.5. Others - All other queries not classified under Analysing Stocks, Screening Stocks, Summarising Company Financial Filings, or Analysing Market Events, and check whether this query dependent on the previous context, if dependent return True otherwise return false. Respond ONLY with a JSON object in the format {"finance": True, "type": "category_name", "context": True} where finance is bool indicating whether query is financial or investment related, category_name is one of the five categories above and context is bool indicating whether to dependent on context.Do not include any other text in your response.\'), if type is Summarising Company Financial Filings, for annual report JSON Object should be  {"finance": True, "type": "Summarising Company Financial Filings", "context": True, "ticker":"ticker_name", "fiscal_year": "2024"}, for quarterly report JSON Object should be  {"type": "Summarising Company Financial Filings", "context": True, "ticker":"ticker_name", "fiscal_year": "2024", "fiscal_quarter": "Q1"}')
    QUERY_CONTEXT_ANALYSING_STOCKS = os.getenv("QUERY_CONTEXT_ANALYSING_STOCKS", 'You are an expert investment analyst tasked with creating a comprehensive and professional stock analysis report for a publicly traded company. The report must be detailed, data-driven, and based solely on real, verifiable information—no hypothetical or speculative data. Follow the structured template below to ensure clarity and thoroughness, addressing each section with precision and professionalism. Investors will rely on this report for decision-making, so emphasize accuracy, relevance, and actionable insights. If specific data is unavailable, note it and explain why, without fabricating information.Title Page* Company Name and Ticker Symbol: The name of the company being analyzed (e.g., "Apple Inc.") and its stock ticker (e.g., "AAPL").* Report Date: When the report was issued (e.g., April 2, 2025).* Recommendation: A clear investment stance, such as "Buy," "Sell," "Hold," or a numerical rating (e.g., 1–5 scale).* Target Price: The analyst’s predicted stock price within a specific timeframe (e.g., 12 months).Executive SummaryA concise overview of the report’s key findings, including:* The investment recommendation.* The target price and potential upside/downside (e.g., "Target Price: $150, Upside: 20%").* A brief rationale for the recommendation (e.g., strong revenue growth, undervaluation, or industry headwinds).Company Overview* Business Description: A summary of what the company does, its industry, key products/services, and markets it operates in.* Recent Performance: Highlights of recent financial results or major events (e.g., earnings beats, new product launches, or mergers).* Market Position: The company’s competitive standing within its sector.Investment Thesis* The core argument for the recommendation, broken into key points such as:* Growth Drivers: Factors expected to boost the stock (e.g., expansion into new markets, innovative products).* Risks: Potential challenges (e.g., regulatory issues, competition, macroeconomic factors).* Valuation Summary: Whether the stock is overvalued, undervalued, or fairly priced based on analysis.Financial Analysis* Historical Performance: A review of past financials, typically including:* Revenue, net income, and earnings per share (EPS) over the last 3–5 years.* Margins (gross, operating, net).* Cash flow and debt levels.* Forecasts: Projections for future performance (e.g., revenue growth of 8% CAGR over 5 years).* Key Ratios: Metrics like Price-to-Earnings (P/E), Price-to-Book (P/B), Return on Equity (ROE), or Debt-to-Equity (D/E), compared to industry averages.Valuation* Methodology: The techniques used to determine the stock’s value, such as:* Discounted Cash Flow (DCF): Estimating future cash flows and discounting them to present value.* Comparable Company Analysis: Comparing the stock’s metrics (e.g., P/E) to peers.* Precedent Transactions: Valuations based on similar M&A deals.* Target Price Calculation: How the analyst arrived at the target price, often with a range (e.g., $145–$155).Industry and Market Analysis* Sector Trends: An overview of the industry’s outlook (e.g., growth in renewable energy, decline in retail).* Macro Factors: External influences like interest rates, inflation, or geopolitical events.* Competitive Landscape: How the company stacks up against rivals.Risks and Catalysts* Upside Catalysts: Events that could drive the stock higher (e.g., a successful product launch).* Downside Risks: Potential negatives (e.g., supply chain disruptions, lawsuits).* Sensitivity Analysis: How changes in assumptions (e.g., lower growth rates) affect the valuation.Charts, Tables, and Visuals* Stock Price Chart: Historical performance (e.g., 1-year or 5-year chart).* Financial Tables: Summaries of income statements, balance sheets, and cash flow statements.* Comparables Table: Metrics of similar companies side-by-side.* Scenario Analysis: Bull, base, and bear case outcomes with corresponding prices.Conclusion* A restatement of the recommendation, target price, and key takeaways.* Sometimes includes a confidence level or timeframe (e.g., “We expect the stock to reach $150 within 12–18 months”).')
    QUERY_CONTEXT_SCREENING_STOCKS = os.getenv("QUERY_CONTEXT_SCREENING_STOCKS", 'List the pros and cons of each listed stock, and conclude with the best stocks to invest, based on the most updated market information.')
    QUERY_CONTEXT_SUMMARISING_FILINGS = os.getenv("QUERY_CONTEXT_SUMMARISING_FILINGS", 'You are an expert investment analyst tasked with analyzing SEC filings to provide actionable insights for investors. Your goal is to deliver a comprehensive, professional, and detailed analysis of a specified company\'s SEC filings (e.g., 10-K, 10-Q, 8-K, or others as relevant). Follow the structured template below to ensure investors can easily understand critical details and make informed decisions. Use clear, concise language, avoid speculation, and base your analysis solely on the information provided in the filings or publicly available data. If specific filings are provided, reference them directly; otherwise, assume you are analyzing the most recent relevant filings.- If specific sections of the filing are missing or incomplete, note this in the analysis and suggest alternative sources for the missing information.- If analyzing multiple filings (e.g., a 10-K and a recent 8-K), integrate insights across filings to provide a cohesive picture.- If the company operates in a highly regulated industry (e.g., pharmaceuticals, finance), pay special attention to regulatory risks and compliance disclosures.- If you lack access to the specific filing, provide a general analysis based on typical SEC filing structures and note that investors should retrieve the latest filing from the SEC EDGAR database.SEC Filings Analysis TemplateKey Highlights- Objective: Provide a high-level summary of the most critical findings from the analysis to grab investors’ attention.- Details to Include:- Top 3–5 takeaways from the filing (e.g., significant financial achievements, major risks, strategic shifts).- Notable changes since the last filing (e.g., revenue growth, new litigation, management changes).- A brief statement on the overall investment outlook (e.g., positive, cautious, or concerning) based on the analysis.1. Company Overview- Objective: Provide a brief summary of the company to contextualize the analysis.- Details to Include:- Company name, ticker symbol, and industry.- Primary business operations and key products/services.- Market position and competitive landscape (based on filings or public data).2. Filing Overview- Objective: Summarize the specific SEC filing(s) being analyzed.- Details to Include:- Type of filing (e.g., 10-K, 10-Q, 8-K) and filing date.- Purpose of the filing (e.g., annual report, quarterly update, material event).- Key sections of the filing to be analyzed (e.g., Management’s Discussion and Analysis, Financial Statements, Risk Factors).3. Financial Performance- Objective: Highlight key financial metrics and trends that investors should focus on.- Details to Include:- Revenue, net income, and earnings per share (EPS) for the reporting period, with comparisons to prior periods.- Gross margin, operating margin, and other profitability metrics.- Balance sheet highlights: assets, liabilities, and equity.- Cash flow analysis: operating, investing, and financing activities.- Any significant changes in financial position or unusual items (e.g., write-offs, restructuring charges).- Comparison to industry benchmarks or peers, if relevant.4. Risk Factors- Objective: Identify and evaluate risks that could impact the company’s performance or stock value.- Details to Include:- Key risks outlined in the filing (e.g., market risks, regulatory risks, operational risks).- Assessment of the severity and likelihood of these risks.- Any new or emerging risks compared to previous filings.- Mitigation strategies mentioned by the company.5. Management’s Discussion and Analysis (MD&A)- Objective: Summarize management’s perspective on the company’s performance and outlook.- Details to Include:- Key points from the MD&A section, such as drivers of financial performance.- Management’s commentary on market conditions, operational challenges, or strategic initiatives.- Forward-looking statements and their implications for investors.- Any discrepancies between management’s narrative and financial data.6. Corporate Governance and Executive Compensation- Objective: Assess the company’s governance structure and alignment with shareholder interests.- Details to Include:- Board composition and independence.- Executive compensation structure and its link to performance metrics.- Any governance-related red flags (e.g., conflicts of interest, lack of transparency).- Shareholder voting rights and recent proxy statement highlights.7. Legal Proceedings and Contingencies- Objective: Highlight any legal or regulatory issues that could affect the company.- Details to Include:- Summary of ongoing or potential litigation, as disclosed in the filing.- Estimated financial impact of legal proceedings, if disclosed.- Regulatory investigations or compliance issues.- Potential implications for the company’s operations or reputation.8. Strategic Initiatives and Outlook- Objective: Evaluate the company’s strategic direction and growth prospects.- Details to Include:- Major strategic initiatives (e.g., mergers, acquisitions, divestitures, new product launches).- Capital expenditure plans and their expected impact.- Market expansion or innovation strategies.- Management’s guidance for future performance, including revenue or earnings projections.9. Red Flags and Areas of Concern- Objective: Identify any warning signs that investors should investigate further.- Details to Include:- Inconsistencies between financial statements and management’s commentary.- Significant changes in accounting policies or estimates.- High debt levels, liquidity concerns, or covenant breaches.- Any undisclosed or poorly explained items that raise questions.10. Investor Takeaways- Objective: Provide concise, actionable insights for investors.- Details to Include:- Key strengths of the company based on the analysis.- Major risks or challenges that warrant caution.- Specific metrics or trends investors should monitor in future filings.- Overall investment thesis: bullish, bearish, or neutral, with a clear rationale.11. Recommendations for Further Analysis- Objective: Guide investors on additional steps to deepen their understanding.- Details to Include:- Other SEC filings to review (e.g., recent 8-Ks, proxy statements).- External sources for context (e.g., industry reports, competitor filings).- Questions to ask during earnings calls or investor presentations.')
    QUERY_CONTEXT_ANALYSING_MARKET = os.getenv("QUERY_CONTEXT_ANALYSING_MARKET", 'You are an experienced investment analyst tasked with analyzing the impact of an economic data release, such as CPI, GDP, unemployment figures, or another key indicator, and generating a comprehensive report. Follow the structured template provided to organize your response, ensuring a systematic and thorough analysis. Format the report using numbering for sections and bullet points for subsections, as shown in the template, to achieve a professional presentation. The report must be clear, actionable, and suitable for a general investment audience. Incorporate relevant economic context, market reactions, and investment insights, keeping less critical sections concise. Use only real, verifiable data for the economic data release and related metrics, sourced from credible outlets like government reports, Bloomberg, or reputable financial news. If specific data (e.g., release date, figures) is unavailable, state this clearly and focus on general implications based on typical impacts of such releases, without using hypothetical values. Include visual aids like charts or tables if relevant, described in text form. Use the economic data name as the title.1. Executive Summary- Provide a concise overview of the economic data release and its significance.- Summarize key findings, including whether the data met expectations and primary implications for markets and investments.- Example: Highlight how the data influences monetary policy or investor sentiment.2. Event Details- Specify the economic data release, including the indicator and date.- State the consensus forecast from analysts.- Report the actual data released.- Highlight notable sub-components, such as core metrics or specific drivers.- If data is unavailable, note: "Specific data is not provided; analysis reflects typical impacts of this indicator."3. Market Reaction- Document immediate market movements post-release, covering indices, yields, or currencies.- Note any surprises relative to expectations.- If data is unavailable, describe typical market responses for this type of indicator.4. Economic Context and Implications- Outline the broader economic environment, including metrics like growth or employment, using real data.- Assess what the data suggests about economic trends, such as inflation or recovery.- Evaluate potential central bank responses, such as policy shifts.- Discuss effects on economic growth, like impacts on spending or investment.5. Sector and Asset Class Impact- Identify industries affected by the data, such as those sensitive to rates or demand shifts.- Analyze implications for equities, bonds, commodities, and real estate.- Highlight potential outperformers and underperformers based on the release.6. Global Perspective- Assess impacts on other economies, such as effects from policy divergence.- Discuss implications for international trade and forex markets, like currency or export dynamics.7. Historical Comparison and Trends- Compare the data to historical averages or trends using real figures.- Determine if it continues or deviates from recent patterns.- If data is unavailable, discuss typical historical patterns for this indicator.8. Scenario Analysis- Outline possible future scenarios based on the data, such as sustained trends or reversals.- Discuss market and investment outcomes for each scenario, like asset price shifts.9. Investment Insights and Recommendations- Summarize actionable insights, such as favored strategies.- Suggest portfolio adjustments, like shifting asset allocations.- Highlight risks and opportunities to monitor, such as upcoming policy decisions.10. Conclusion- Recap the analysis and outlook, emphasizing key takeaways.- Offer a forward-looking perspective, such as preparing for economic shifts.')
    QUERY_CONTEXT_OTHERS = os.getenv("QUERY_CONTEXT_OTHERS", "")

    SEC_API_ORGANIZATION = os.getenv("SEC_API_ORGANIZATION", "ADDX")
    SEC_API_EMAIL = os.getenv("SEC_API_EMAIL", "<EMAIL>")

    QUOTA_RATELIMIT_MSG = os.getenv("QUOTA_RATELIMIT_MSG", "You've reached today’s limit. It’ll refresh tomorrow.\nStay tuned for new plans with higher limits!")

    # earning file search settings
    EARNING_VECTOR_NAME = os.getenv("EARNING_VECTOR_NAME", "dev_earning_vector")

# Create a settings instance to be imported by other modules
settings = Settings()

# Export individual variables for backward compatibility
PORT = settings.PORT
REDIS_HOST = settings.REDIS_HOST
REDIS_PORT = settings.REDIS_PORT
REDIS_DB = settings.REDIS_DB
REDIS_MAX_CONNECTIONS = settings.REDIS_MAX_CONNECTIONS
DB_HOST = settings.DB_HOST
DB_PORT = settings.DB_PORT
DB_NAME = settings.DB_NAME
DB_USER = settings.DB_USER
DB_PASSWORD = settings.DB_PASSWORD
ENABLE_CACHE = settings.ENABLE_CACHE
CACHE_TTL = settings.CACHE_TTL
CACHE_COMPRESSION = settings.CACHE_COMPRESSION
CACHE_MEMORY_ENABLED = settings.CACHE_MEMORY_ENABLED
CACHE_MEMORY_MAX_ITEMS = settings.CACHE_MEMORY_MAX_ITEMS
CACHE_MEMORY_TTL = settings.CACHE_MEMORY_TTL
CACHE_PERSISTENT_ENABLED = settings.CACHE_PERSISTENT_ENABLED
CACHE_PERSISTENT_PATH = settings.CACHE_PERSISTENT_PATH
CACHE_LRU_POLICY = settings.CACHE_LRU_POLICY
CACHE_MAX_SIZE_MB = settings.CACHE_MAX_SIZE_MB
CACHE_SIMILARITY_THRESHOLD = settings.CACHE_SIMILARITY_THRESHOLD
OPENAI_API_KEY = settings.OPENAI_API_KEY
OPENAI_API_URL = settings.OPENAI_API_URL
OPENAI_ASSISTANT_ID = settings.OPENAI_ASSISTANT_ID
OPENAI_ASSISTANT_NAME = settings.OPENAI_ASSISTANT_NAME
OPENAI_ASSISTANT_MODEL = settings.OPENAI_ASSISTANT_MODEL
OPENAI_ASSISTANT_INSTRUCTIONS = settings.OPENAI_ASSISTANT_INSTRUCTIONS
OPENAI_ASSISTANT_TOOLS = settings.OPENAI_ASSISTANT_TOOLS
OPENAI_ASSISTANT_TOOL_RESOURCES = settings.OPENAI_ASSISTANT_TOOL_RESOURCES
ANTHROPIC_API_KEY = settings.ANTHROPIC_API_KEY
ANTHROPIC_API_URL = settings.ANTHROPIC_API_URL
DEEPSEEK_API_KEY = settings.DEEPSEEK_API_KEY
DEEPSEEK_API_URL = settings.DEEPSEEK_API_URL
GOOGLE_SEARCH_API_KEY = settings.GOOGLE_SEARCH_API_KEY
GOOGLE_SEARCH_ENGINE_ID = settings.GOOGLE_SEARCH_ENGINE_ID
SERP_API_KEY = settings.SERP_API_KEY
USE_SERP_API = settings.USE_SERP_API
MAX_CONCURRENT_SEARCHES = settings.MAX_CONCURRENT_SEARCHES
DEFAULT_SEARCH_THRESHOLD = settings.DEFAULT_SEARCH_THRESHOLD
SEARCH_CACHE_TTL = settings.SEARCH_CACHE_TTL
AUTO_DETECT_SEARCH = settings.AUTO_DETECT_SEARCH
SEARCH_RESULT_MAX_CHARS = settings.SEARCH_RESULT_MAX_CHARS
WORKER_THREADS = settings.WORKER_THREADS
MODEL_QUEUE_SIZE = settings.MODEL_QUEUE_SIZE
MAX_CONCURRENT_API_CALLS = settings.MAX_CONCURRENT_API_CALLS
QUERY_CONTEXT_ANALYSING_STOCKS = settings.QUERY_CONTEXT_ANALYSING_STOCKS
QUERY_CONTEXT_SCREENING_STOCKS = settings.QUERY_CONTEXT_SCREENING_STOCKS
QUERY_CONTEXT_SUMMARISING_FILINGS = settings.QUERY_CONTEXT_SUMMARISING_FILINGS
QUERY_CONTEXT_ANALYSING_MARKET = settings.QUERY_CONTEXT_ANALYSING_MARKET

# Function to get database URL
def get_database_url(schema_name: Optional[str] = None, async_mode: Optional[bool] =True):
    """
    Get the database URL for SQLAlchemy.
    
    Args:
        schema_name: Optional schema name to use. If provided, it will use this schema.
                     If a key in DB_SCHEMAS, it will use the corresponding schema.
                     If None, it will use the default schema.
        async_mode: Whether to return an async URL (mysql+aiomysql://) or sync URL (mysql+pymysql://)
                    Defaults to True for async mode since most of our code is async.
        
    Returns:
        str: The database URL
    """
    driver = "aiomysql" if async_mode else "pymysql"
        
    # Build the database URL
    url = f"mysql+{driver}://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}"
    
    # Append schema if provided and include_schema is True
    if schema_name:
        url = f"{url}/{schema_name}"
    
    logger.debug(f"Generated database URL for schema '{schema_name}': {url}")
    return url

logger.debug(f"settings: {settings.__class__.__dict__}")
