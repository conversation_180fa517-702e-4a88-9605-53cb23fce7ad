"""
Configuration file for SEC XBRL concepts to be stored in the database.
This file defines the most common and important financial metrics from the us-gaap taxonomy.
"""

# Common financial concepts in the us-gaap taxonomy to be stored in the database
COMMON_FINANCIAL_CONCEPTS = [
    # Income Statement
    "Revenues",
    "RevenueFromContractWithCustomerExcludingAssessedTax",
    "CostOfGoodsAndServicesSold",
    "GrossProfit",
    "OperatingExpenses",
    "OperatingIncomeLoss",
    "IncomeLossFromContinuingOperationsBeforeIncomeTaxesExtraordinaryItemsNoncontrollingInterest",
    "IncomeTaxExpenseBenefit",
    "NetIncomeLoss",
    "EarningsPerShareBasic",
    "EarningsPerShareDiluted",
    
    # Balance Sheet
    "Assets",
    "AssetsCurrent",
    "CashAndCashEquivalentsAtCarryingValue",
    "ShortTermInvestments",
    "AccountsReceivableNetCurrent",
    "Inventory",
    "AssetsNoncurrent",
    "PropertyPlantAndEquipmentNet",
    "Goodwill",
    "IntangibleAssetsNetExcludingGoodwill",
    "LongTermInvestments",
    
    "Liabilities",
    "LiabilitiesCurrent",
    "AccountsPayableCurrent",
    "AccruedLiabilitiesCurrent",
    "LongTermDebtCurrent",
    "LiabilitiesNoncurrent",
    "LongTermDebt",
    
    "StockholdersEquity",
    "CommonStockParOrStatedValuePerShare",
    "CommonStockSharesAuthorized",
    "CommonStockSharesIssued",
    "CommonStockSharesOutstanding",
    "RetainedEarningsAccumulatedDeficit",
    "AccumulatedOtherComprehensiveIncomeLossNetOfTax",
    
    # Cash Flow
    "NetCashProvidedByUsedInOperatingActivities",
    "NetCashProvidedByUsedInInvestingActivities",
    "NetCashProvidedByUsedInFinancingActivities",
    "CashAndCashEquivalentsPeriodIncreaseDecrease",
    
    # Other Key Metrics
    "ResearchAndDevelopmentExpense",
    "SellingGeneralAndAdministrativeExpense",
    "InterestExpense",
    "InterestIncome",
    "DepreciationAndAmortization",
    "CapitalExpenditure",
    "DividendsPaid",
    "ShareBasedCompensation",
    "WeightedAverageNumberOfSharesOutstandingBasic",
    "WeightedAverageNumberOfDilutedSharesOutstanding",
    
    # Additional Dividend Metrics
    "DividendRate",
    "DividendYield",
    "CommonStockDividendsPerShareDeclared",
    "CommonStockDividendsPerShareCashPaid",
    "DividendPayoutRatio",
    "CashDividendsCashPaid",
]

# Concepts grouped by category for easier reference
INCOME_STATEMENT_CONCEPTS = [
    "Revenues",
    "RevenueFromContractWithCustomerExcludingAssessedTax",
    "CostOfGoodsAndServicesSold",
    "GrossProfit",
    "OperatingExpenses",
    "OperatingIncomeLoss",
    "IncomeLossFromContinuingOperationsBeforeIncomeTaxesExtraordinaryItemsNoncontrollingInterest",
    "IncomeTaxExpenseBenefit",
    "NetIncomeLoss",
    "EarningsPerShareBasic",
    "EarningsPerShareDiluted",
    "ResearchAndDevelopmentExpense",
    "SellingGeneralAndAdministrativeExpense",
    "InterestExpense",
    "InterestIncome",
]

BALANCE_SHEET_CONCEPTS = [
    "Assets",
    "AssetsCurrent",
    "CashAndCashEquivalentsAtCarryingValue",
    "ShortTermInvestments",
    "AccountsReceivableNetCurrent",
    "Inventory",
    "AssetsNoncurrent",
    "PropertyPlantAndEquipmentNet",
    "Goodwill",
    "IntangibleAssetsNetExcludingGoodwill",
    "LongTermInvestments",
    "Liabilities",
    "LiabilitiesCurrent",
    "AccountsPayableCurrent",
    "AccruedLiabilitiesCurrent",
    "LongTermDebtCurrent",
    "LiabilitiesNoncurrent",
    "LongTermDebt",
    "StockholdersEquity",
    "CommonStockParOrStatedValuePerShare",
    "CommonStockSharesAuthorized",
    "CommonStockSharesIssued",
    "CommonStockSharesOutstanding",
    "RetainedEarningsAccumulatedDeficit",
    "AccumulatedOtherComprehensiveIncomeLossNetOfTax",
]

CASH_FLOW_CONCEPTS = [
    "NetCashProvidedByUsedInOperatingActivities",
    "NetCashProvidedByUsedInInvestingActivities",
    "NetCashProvidedByUsedInFinancingActivities",
    "CashAndCashEquivalentsPeriodIncreaseDecrease",
    "DepreciationAndAmortization",
    "CapitalExpenditure",
    "DividendsPaid",
    "ShareBasedCompensation",
]

OTHER_CONCEPTS = [
    "WeightedAverageNumberOfSharesOutstandingBasic",
    "WeightedAverageNumberOfDilutedSharesOutstanding",
]

# Dividend-specific concepts
DIVIDEND_CONCEPTS = [
    # Existing dividend concept
    "DividendsPaid",
    
    # Dividend Rate and Yield
    "DividendRate",
    "DividendYield",
    
    # Dividend Dates and Timing
    "DividendPaymentDate",
    "DividendDeclarationDate",
    "DividendExDate",
    "DividendRecordDate",
    
    # Dividend History and Policy
    "CommonStockDividendsPerShareDeclared",
    "CommonStockDividendsPerShareCashPaid",
    "DividendPayoutRatio",
    "CashDividendsCashPaid",
    
    # Special Dividends
    "SpecialDividendPerShare",
    "StockDividendsPerShare",
    
    # Preferred Stock Dividends
    "PreferredStockDividendsPerShareDeclared",
    "PreferredStockDividendRate",
]

# DEI (Document and Entity Information) concepts to store
DEI_CONCEPTS = [
    "EntityRegistrantName",
    "EntityCentralIndexKey",
    "EntityTaxIdentificationNumber",
    "EntityAddressAddressLine1",
    "EntityAddressAddressLine2",
    "EntityAddressAddressLine3",
    "EntityAddressCityOrTown",
    "EntityAddressStateOrProvince",
    "EntityAddressCountry",
    "EntityAddressPostalZipCode",
    "EntityIncorporationStateCountryCode",
    "EntityTaxIdentificationNumber",
    "EntityFileNumber",
    "EntityFilerCategory",
    "EntityWellKnownSeasonedIssuer",
    "EntityCurrentReportingStatus",
    "EntityVoluntaryFilers",
    "EntityPublicFloat",
    "DocumentType",
    "DocumentPeriodEndDate",
    "DocumentFiscalYearFocus",
    "DocumentFiscalPeriodFocus",
]

# All concepts to store (us-gaap + dei + additional dividend concepts)
ALL_CONCEPTS_TO_STORE = COMMON_FINANCIAL_CONCEPTS + DEI_CONCEPTS + [
    # Additional dividend concepts not already in COMMON_FINANCIAL_CONCEPTS
    "DividendPaymentDate",
    "DividendDeclarationDate",
    "DividendExDate",
    "DividendRecordDate",
    "SpecialDividendPerShare",
    "StockDividendsPerShare",
    "PreferredStockDividendsPerShareDeclared",
    "PreferredStockDividendRate",
]
