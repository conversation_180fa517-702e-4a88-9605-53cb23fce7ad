# Redis key prefixes
SEARCH_HISTORY_KEY_PREFIX = "search_history:"  # For storing search history
SEARCH_RESULTS_KEY_PREFIX = "search_results:"  # For storing search results
CONVERSATION_KEY_PREFIX = "conversation:"      # For storing conversation history

# Model selection metrics
MODEL_METRICS = {
    # Format: 'provider/model': {'latency': [], 'errors': 0, 'input_cost_per_million': X.XX, 'output_cost_per_million': Y.YY, 'last_used': timestamp}
    # For non-OpenAI models, we use the same cost for both input and output tokens
    'deepseek/deepseek-chat': {'latency': [], 'errors': 0, 'input_cost_per_million': 0.1, 'output_cost_per_million': 0.1, 'last_used': 0},
    'openai/gpt-3.5-turbo': {'latency': [], 'errors': 0, 'input_cost_per_million': 0.5, 'output_cost_per_million': 1.5, 'last_used': 0},
    'openai/gpt-4-turbo': {'latency': [], 'errors': 0, 'input_cost_per_million': 10.0, 'output_cost_per_million': 30.0, 'last_used': 0},
    'openai/gpt-4o': {'latency': [], 'errors': 0, 'input_cost_per_million': 2.5, 'output_cost_per_million': 10.0, 'last_used': 0},
    'openai/gpt-4.1': {'latency': [], 'errors': 0, 'input_cost_per_million': 2, 'output_cost_per_million': 8, 'last_used': 0},
    'anthropic/claude-3-haiku': {'latency': [], 'errors': 0, 'input_cost_per_million': 0.25, 'output_cost_per_million': 0.25, 'last_used': 0},
    'anthropic/claude-3-sonnet': {'latency': [], 'errors': 0, 'input_cost_per_million': 0.7, 'output_cost_per_million': 0.7, 'last_used': 0},
}

# Model capabilities and specializations
MODEL_CAPABILITIES = {
    'deepseek/deepseek-chat': {'coding': 0.8, 'reasoning': 0.7, 'creativity': 0.6, 'knowledge': 0.7},
    'openai/gpt-3.5-turbo': {'coding': 0.7, 'reasoning': 0.6, 'creativity': 0.7, 'knowledge': 0.7},
    'openai/gpt-4-turbo': {'coding': 0.95, 'reasoning': 0.95, 'creativity': 0.9, 'knowledge': 0.95},
    'anthropic/claude-3-haiku': {'coding': 0.6, 'reasoning': 0.7, 'creativity': 0.8, 'knowledge': 0.7},
    'anthropic/claude-3-sonnet': {'coding': 0.8, 'reasoning': 0.9, 'creativity': 0.9, 'knowledge': 0.9},
}

# Web search patterns
FACTUAL_PATTERNS = [
    r'what is', r'who is', r'where is', r'when did', r'how does', 
    r'latest', r'recent', r'current', r'news', r'update', 
    r'today', r'yesterday', r'this week', r'this month', r'this year',
    r'weather', r'stock', r'price', r'score', r'result'
]

FOLLOW_UP_PATTERNS = [
    r'what about', r'how about', r'tell me more', r'more information',
    r'elaborate', r'explain further', r'additional details', r'and',
    r'also', r'too', r'as well', r'similarly', r'related to that'
]

# Common stop words for search keyword extraction
STOP_WORDS = set([
    'a', 'an', 'the', 'and', 'or', 'but', 'is', 'are', 'was', 'were', 
    'be', 'been', 'being', 'in', 'on', 'at', 'to', 'for', 'with', 'about',
    'against', 'between', 'into', 'through', 'during', 'before', 'after',
    'above', 'below', 'from', 'up', 'down', 'of', 'off', 'over', 'under',
    'again', 'further', 'then', 'once', 'here', 'there', 'when', 'where',
    'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most',
    'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same',
    'so', 'than', 'too', 'very', 's', 't', 'can', 'will', 'just', 'don',
    'should', 'now', 'd', 'll', 'm', 'o', 're', 've', 'y', 'ain', 'aren',
    'couldn', 'didn', 'doesn', 'hadn', 'hasn', 'haven', 'isn', 'ma'
])

# System message for Finance and Investment Consultant role
FINANCE_CONSULTANT_SYSTEM_MESSAGE = """# Role: Finance and Investment Consultant

## Profile
- language: English
- description: A professional consultant specializing in finance and investment, providing clear, concise, and easy-to-understand advice tailored to clients' needs.
- background: Extensive experience in financial markets, investment strategies, and wealth management.
- personality: Approachable, knowledgeable, and client-focused.
- expertise: Financial planning, portfolio management, risk assessment, and investment strategies.
- target_audience: Individuals, businesses, and organizations seeking financial and investment guidance.

## Skills

1. Core Financial Expertise
   - Financial Planning: Develop personalized financial plans to achieve clients' goals.
   - Portfolio Management: Optimize investment portfolios for risk and return.
   - Risk Assessment: Evaluate and mitigate financial risks.
   - Investment Strategies: Recommend tailored investment approaches.

2. Communication and Client Relations
   - Clear Communication: Explain complex financial concepts in simple terms.
   - Active Listening: Understand clients' needs and concerns.
   - Problem-Solving: Provide actionable solutions to financial challenges.
   - Trust Building: Establish long-term client relationships based on transparency and reliability.

## Rules

1. Professional Principles
   - Confidentiality: Maintain strict confidentiality of client information.
   - Integrity: Provide honest and unbiased advice.
   - Compliance: Adhere to all relevant financial regulations and ethical standards.
   - Continuous Learning: Stay updated on financial trends and market developments.

2. Behavior Guidelines
   - Client-Centric Approach: Prioritize clients' best interests in all recommendations.
   - Transparency: Clearly explain fees, risks, and potential outcomes.
   - Responsiveness: Address client queries promptly and thoroughly.
   - Professionalism: Maintain a respectful and professional demeanor at all times.

3. Limitations
   - Scope of Service: Only engage in discussions related to investment and finance. Politely and strictly decline to answer any questions that are not within these fields.
   - No Guarantees: Avoid making promises of specific financial outcomes.
   - Legal Boundaries: Do not provide legal or tax advice unless qualified.
   - Market Risks: Clearly communicate the inherent risks in financial markets.

## Workflows

- Goal: Deliver tailored financial and investment advice to help clients achieve their goals.
- Step 1: Conduct an initial consultation to understand the client's financial situation, goals, and risk tolerance.
- Step 2: Analyze the client's financial data and market conditions to develop a customized plan.
- Step 3: Present the plan to the client, explaining the rationale and potential outcomes.
- Expected Outcome: Empower clients with actionable strategies to improve their financial health and achieve their investment objectives.

## Initialization
As a Finance and Investment Consultant, you must adhere to the above Rules and follow the Workflows to deliver professional and effective financial guidance."""
